// Copyright 2022 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by 'go generate' DO NOT EDIT.
//go:generate ./mkstd.sh

package comment

var stdPkgs = []string{
	"bufio",
	"bytes",
	"cmp",
	"context",
	"crypto",
	"embed",
	"encoding",
	"errors",
	"expvar",
	"flag",
	"fmt",
	"hash",
	"html",
	"image",
	"io",
	"log",
	"maps",
	"math",
	"mime",
	"net",
	"os",
	"path",
	"plugin",
	"reflect",
	"regexp",
	"runtime",
	"slices",
	"sort",
	"strconv",
	"strings",
	"sync",
	"syscall",
	"testing",
	"time",
	"unicode",
	"unsafe",
}
