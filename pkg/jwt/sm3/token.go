package sm3

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
)

type JwkSets struct {
	Keys     []*Jwk
	ClientId string
}

type Jwk struct {
	Alg string `json:"alg,omitempty"`
	Kid string `json:"kid,omitempty"`
	Kty string `json:"kty,omitempty"`
	K   string `json:"k,omitempty"`
	Use string `json:"use,omitempty"`
	Exp string `json:"exp,omitempty"`
}

type JwkSetRawObject struct {
	Keys []*Jwk `json:"keys"`
}

func NewJwkSets(appSecret string, clientId string, expires int) (*JwkSets, error) {
	var jwks []*Jwk
	if err := json.Unmarshal([]byte(appSecret), &jwks); err != nil {
		return nil, errors.New(fmt.Sprintf("unmarshal appSecret %s failure: %s", appSecret, err.Error()))
	}

	if len(jwks) == 0 {
		return nil, errors.New("jwks is empty")
	}

	// 设置默认值
	for _, jwk := range jwks {
		if jwk.Kid == "" {
			return nil, errors.New("jwk missing kid field")
		}

		if jwk.K == "" {
			return nil, errors.New("jwk missing k field")
		}

		if jwk.Kty == "" {
			jwk.Kty = "oct"
		}

		if jwk.Alg == "" {
			jwk.Alg = "SM3"
		}

		if jwk.Use == "" {
			jwk.Use = "sig"
		}

		if jwk.Exp == "" {
			if expires < 1 {
				jwk.Exp = "7200"
			} else {
				jwk.Exp = strconv.Itoa(expires)
			}
		}
	}

	return &JwkSets{Keys: jwks, ClientId: clientId}, nil
}

func (s *JwkSets) CreateToken(exp int) (string, error) {
	now := time.Now()

	// kid为时间戳，取时间戳最大的密钥
	jwk := s.findFirst()

	signingMethod := jwt.GetSigningMethod(jwk.Alg)

	token := jwt.NewWithClaims(signingMethod, jwt.MapClaims{
		"nbf": now.Add(-time.Hour).Unix(),
		"exp": now.Add(time.Second * time.Duration(exp)).Unix(),
		"jti": uuid.NewString(),
		"sub": s.ClientId,
	})

	token.Header["kid"] = jwk.Kid

	signed, err := token.SignedString(octKeyFunc(jwk.K))
	if err != nil {
		return "", errors.New(fmt.Sprintf("signed token error: %s", err.Error()))
	}
	return signed, nil
}

func (s *JwkSets) findFirst() *Jwk {

	sort.Slice(s.Keys, func(i, j int) bool {
		return cast.ToInt(s.Keys[i].Kid) > cast.ToInt(s.Keys[j].Kid)
	})

	return s.Keys[0]
}

// oct 算法key编码
func octKeyFunc(key string) []byte {
	k, err := base64urlTrailingPadding(key)
	if err != nil {
		return nil
	}
	return k
}

func base64urlTrailingPadding(s string) ([]byte, error) {
	s = strings.TrimRight(s, "=")
	return base64.RawURLEncoding.DecodeString(s)
}
