package sm3

import (
	"crypto/hmac"

	"github.com/emmansun/gmsm/sm3"
	"github.com/golang-jwt/jwt/v5"

	"hash"
)

type SigningMethodGMSM struct {
	Name string
}

var (
	SigningMethodSM3 *SigningMethodGMSM
)

func init() {
	// sm3
	SigningMethodSM3 = &SigningMethodGMSM{"SM3"}
	jwt.RegisterSigningMethod(SigningMethodSM3.Alg(), func() jwt.SigningMethod {
		return SigningMethodSM3
	})
}

func (m *SigningMethodGMSM) Verify(signingString string, sig []byte, key interface{}) error {
	keyBytes, ok := key.([]byte)
	if !ok {
		return jwt.ErrInvalidKeyType
	}

	h := hmac.New(SM3Hash, keyBytes)
	h.Write([]byte(signingString))
	if !hmac.Equal(sig, h.Sum(nil)) {
		return jwt.ErrSignatureInvalid
	}

	return nil
}

func (m *SigningMethodGMSM) Sign(signingString string, key interface{}) ([]byte, error) {
	if keyBytes, ok := key.([]byte); ok {
		h := hmac.New(SM3Hash, keyBytes)
		h.Write([]byte(signingString))
		return h.Sum(nil), nil
	}

	return nil, jwt.ErrInvalidKeyType
}

func (m *SigningMethodGMSM) Alg() string {
	return m.Name
}

func SM3Hash() hash.Hash {
	return sm3.New()
}
