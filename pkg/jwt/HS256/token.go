package HS256

import (
	"fmt"
	"github.com/golang-jwt/jwt/v4"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"time"
)

// 可自定义Claims
type CustomClaims struct {
	jwt.StandardClaims
}

// 生成jwt token方法
func GenJwtToken(apiKey string, apiSecret string, expiresIn int64) (jwtToken string, err error) {
	customClaims := &CustomClaims{
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(time.Duration(expiresIn) * time.Second).Unix(),
			Issuer:    apiKey,
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, customClaims)
	jwtToken, err = token.SignedString([]byte(apiSecret))
	return
}

type SecretGetter func(appKey string) (string, error)

// VerifyOpenapiToken 校验openapi token, 并返回app key
func VerifyOpenapiToken(jwtToken string, f SecretGetter) (key string, err error) {
	_, err = jwt.ParseWithClaims(jwtToken, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		claims := token.Claims.(*CustomClaims)
		key = claims.Issuer

		var secret string
		secret, err = f(key)
		if err != nil {
			err = jwt.NewValidationError(fmt.Sprintf("校验token失败: %s", err.Error()), jwt.ValidationErrorIssuer)
			return nil, errors.UserErrorWrapf(err, "")
		}
		return interface{}([]byte(secret)), nil
	})
	return
}
