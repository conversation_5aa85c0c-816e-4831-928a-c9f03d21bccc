package HS256

import (
	"github.com/stretchr/testify/assert"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"testing"
	"time"
)

func TestVerifyOpenapiToken(t *testing.T) {
	key := "1234"
	secret := "1234567812345678"
	appMap := map[string]string{
		key: secret,
	}
	secretGetter := func(key string) (string, error) {
		v, ok := appMap[key]
		if !ok {
			return "", errors.UserError("获取secret失败")
		}
		return v, nil
	}


	jwt, err := GenJwtToken("1234", "1234567812345678", int64(time.Hour))
	if err != nil {
		panic(err)
	}
	expectKey, err := VerifyOpenapiToken(jwt, secretGetter)
	if err != nil {
		panic(err)
	}
	assert.Equal(t, key, expectKey)
}
