package mysql

import (
	"context"
	"fmt"
	"github.com/antlr4-go/antlr/v4"
	"github.com/nacos-group/nacos-sdk-go/v2/util"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/sqlparse"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/sqlparse/antlr4/mysql/sql/parser"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/sqlparse/antlr4/utils"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/sqlparse/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/trace_utils"
)

var _ sqlparse.ReplaceTableParser = (*TableParser)(nil)

// Deprecated: 因为antlr4实现比java慢很多(2-3倍), 在高并发和大sql解析时时间较长, 因此弃用
type TableParser struct {
	listener *FindTableListener
	sql      string

	// context
	tableContextList []common.TableContext
}

func NewReplaceParser(sql string) *TableParser {
	return &TableParser{
		NewFindTableListener(),
		sql,
		nil,
	}
}

// Parse
func (s *TableParser) Parse(ctx context.Context, tag string) (err error) {
	ctx, t := trace_utils.StartSpanWithContext(ctx, fmt.Sprintf("mysql sql解析 - %s", tag))
	t.Tag("sql", s.sql)
	defer t.End()

	md5 := util.Md5(s.sql)
	if rv, ok := common.LocalCache.Get(ctx, md5); ok {
		s.tableContextList = rv.([]common.TableContext)
		return
	}

	defer func() {
		if e := recover(); e != nil {
			var ok bool
			err, ok = e.(*utils.ParseCancellationException)
			if !ok {
				panic(e)
			}
			return
		}
	}()
	input := antlr.NewInputStream(s.sql)
	upper := utils.NewCaseChangingStream(input, true)
	lexer := parser.NewCustomMySQLLexer(upper)
	stream := antlr.NewCommonTokenStream(lexer, 0)
	p := parser.NewCustomMySQLParser(stream)
	p.RemoveErrorListeners()
	p.AddErrorListener(utils.NewCancelErrorLitener())
	tree := p.Query()
	antlr.ParseTreeWalkerDefault.Walk(s.listener, tree)
	s.tableContextList = s.listener.ParseTableContextList()
	t.Tag("tables", fmt.Sprintf("%v", s.tableContextList))

	common.LocalCache.Set(ctx, md5, s.tableContextList, common.Expire())
	return nil
}

func (s *TableParser) ParseTables() (tables []string) {
	return lo.Uniq(lo.Map(s.tableContextList, func(item common.TableContext, _ int) string {
		return item.TableName
	}))
}

func (s *TableParser) ReplaceTables(ctx context.Context, tableSqls map[string]string) (string, error) {
	return common.ReplaceTables(ctx, s.sql, s.tableContextList, tableSqls)
}

func (s *TableParser) GetTableContextList() []common.TableContext {
	return s.tableContextList
}

func (s *TableParser) GetParserContext() any {
	return s.tableContextList
}

type FindTableListener struct {
	*parser.BaseMySQLParserListener
	refTables []*parser.SingleTableContext
	current   *common.Scope
}

func NewFindTableListener() *FindTableListener {
	listener := new(FindTableListener)
	listener.current = common.NewScope()
	return listener
}

func (s *FindTableListener) EnterCommonTableExpression(ctx *parser.CommonTableExpressionContext) {
	s.current.AddCteTable(utils.NormalizeTableName(ctx.Identifier().GetText()))
}

func (s *FindTableListener) EnterQuerySpecification(_ *parser.QuerySpecificationContext) {
	s.current = common.NewScopeWithParent(s.current)
}

func (s *FindTableListener) ExitQuerySpecification(_ *parser.QuerySpecificationContext) {
	s.current = s.current.Parent
}

func (s *FindTableListener) EnterSingleTable(ctx *parser.SingleTableContext) {
	table := utils.NormalizeTableName(ctx.TableRef().GetText())
	if s.current.ResolveTable(table) {
		return
	}
	s.refTables = append(s.refTables, ctx)
}

func (s *FindTableListener) DistinctRefTableNames() []string {
	return lo.Uniq(lo.Map(s.refTables, func(item *parser.SingleTableContext, _ int) string {
		return utils.NormalizeTableName(item.TableRef().GetText())
	}))
}

func (s *FindTableListener) ParseTableContextList() []common.TableContext {
	tableContextList := []common.TableContext{}
	for _, item := range s.refTables {
		normalTableName := utils.NormalizeTableName(item.TableRef().GetText())
		var alias string
		if item.TableAlias() != nil {
			alias = item.TableAlias().Identifier().GetText()
		}
		tableContextList = append(tableContextList, common.TableContext{
			TableName: normalTableName,
			Start:     item.TableRef().GetStart().GetStart(),
			Stop:      item.TableRef().GetStop().GetStop(),
			Alias:     alias,
		})
	}
	return tableContextList
}
