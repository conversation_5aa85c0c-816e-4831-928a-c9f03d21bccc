// Code generated from MySQLParser.g4 by ANTLR 4.13.1. DO NOT EDIT.

package parser // MySQLParser

import "github.com/antlr4-go/antlr/v4"

// MySQLParserListener is a complete listener for a parse tree produced by MySQLParser.
type MySQLParserListener interface {
	antlr.ParseTreeListener

	// EnterQuery is called when entering the query production.
	EnterQuery(c *QueryContext)

	// EnterSimpleStatement is called when entering the simpleStatement production.
	EnterSimpleStatement(c *SimpleStatementContext)

	// EnterValues is called when entering the values production.
	EnterValues(c *ValuesContext)

	// EnterSelectStatement is called when entering the selectStatement production.
	EnterSelectStatement(c *SelectStatementContext)

	// EnterQueryExpression is called when entering the queryExpression production.
	EnterQueryExpression(c *QueryExpressionContext)

	// EnterQueryExpressionBody is called when entering the queryExpressionBody production.
	EnterQueryExpressionBody(c *QueryExpressionBodyContext)

	// EnterQueryExpressionParens is called when entering the queryExpressionParens production.
	EnterQueryExpressionParens(c *QueryExpressionParensContext)

	// EnterQueryPrimary is called when entering the queryPrimary production.
	EnterQueryPrimary(c *QueryPrimaryContext)

	// EnterQuerySpecification is called when entering the querySpecification production.
	EnterQuerySpecification(c *QuerySpecificationContext)

	// EnterSubquery is called when entering the subquery production.
	EnterSubquery(c *SubqueryContext)

	// EnterQuerySpecOption is called when entering the querySpecOption production.
	EnterQuerySpecOption(c *QuerySpecOptionContext)

	// EnterLimitClause is called when entering the limitClause production.
	EnterLimitClause(c *LimitClauseContext)

	// EnterLimitOptions is called when entering the limitOptions production.
	EnterLimitOptions(c *LimitOptionsContext)

	// EnterLimitOption is called when entering the limitOption production.
	EnterLimitOption(c *LimitOptionContext)

	// EnterHavingClause is called when entering the havingClause production.
	EnterHavingClause(c *HavingClauseContext)

	// EnterWindowClause is called when entering the windowClause production.
	EnterWindowClause(c *WindowClauseContext)

	// EnterWindowDefinition is called when entering the windowDefinition production.
	EnterWindowDefinition(c *WindowDefinitionContext)

	// EnterWindowSpec is called when entering the windowSpec production.
	EnterWindowSpec(c *WindowSpecContext)

	// EnterWindowSpecDetails is called when entering the windowSpecDetails production.
	EnterWindowSpecDetails(c *WindowSpecDetailsContext)

	// EnterWindowFrameClause is called when entering the windowFrameClause production.
	EnterWindowFrameClause(c *WindowFrameClauseContext)

	// EnterWindowFrameUnits is called when entering the windowFrameUnits production.
	EnterWindowFrameUnits(c *WindowFrameUnitsContext)

	// EnterWindowFrameExtent is called when entering the windowFrameExtent production.
	EnterWindowFrameExtent(c *WindowFrameExtentContext)

	// EnterWindowFrameStart is called when entering the windowFrameStart production.
	EnterWindowFrameStart(c *WindowFrameStartContext)

	// EnterWindowFrameBetween is called when entering the windowFrameBetween production.
	EnterWindowFrameBetween(c *WindowFrameBetweenContext)

	// EnterWindowFrameBound is called when entering the windowFrameBound production.
	EnterWindowFrameBound(c *WindowFrameBoundContext)

	// EnterWindowFrameExclusion is called when entering the windowFrameExclusion production.
	EnterWindowFrameExclusion(c *WindowFrameExclusionContext)

	// EnterWithClause is called when entering the withClause production.
	EnterWithClause(c *WithClauseContext)

	// EnterCommonTableExpression is called when entering the commonTableExpression production.
	EnterCommonTableExpression(c *CommonTableExpressionContext)

	// EnterGroupByClause is called when entering the groupByClause production.
	EnterGroupByClause(c *GroupByClauseContext)

	// EnterOlapOption is called when entering the olapOption production.
	EnterOlapOption(c *OlapOptionContext)

	// EnterOrderClause is called when entering the orderClause production.
	EnterOrderClause(c *OrderClauseContext)

	// EnterDirection is called when entering the direction production.
	EnterDirection(c *DirectionContext)

	// EnterFromClause is called when entering the fromClause production.
	EnterFromClause(c *FromClauseContext)

	// EnterTableReferenceList is called when entering the tableReferenceList production.
	EnterTableReferenceList(c *TableReferenceListContext)

	// EnterTableValueConstructor is called when entering the tableValueConstructor production.
	EnterTableValueConstructor(c *TableValueConstructorContext)

	// EnterExplicitTable is called when entering the explicitTable production.
	EnterExplicitTable(c *ExplicitTableContext)

	// EnterRowValueExplicit is called when entering the rowValueExplicit production.
	EnterRowValueExplicit(c *RowValueExplicitContext)

	// EnterSelectOption is called when entering the selectOption production.
	EnterSelectOption(c *SelectOptionContext)

	// EnterSelectItemList is called when entering the selectItemList production.
	EnterSelectItemList(c *SelectItemListContext)

	// EnterSelectItem is called when entering the selectItem production.
	EnterSelectItem(c *SelectItemContext)

	// EnterSelectAlias is called when entering the selectAlias production.
	EnterSelectAlias(c *SelectAliasContext)

	// EnterWhereClause is called when entering the whereClause production.
	EnterWhereClause(c *WhereClauseContext)

	// EnterTableReference is called when entering the tableReference production.
	EnterTableReference(c *TableReferenceContext)

	// EnterEscapedTableReference is called when entering the escapedTableReference production.
	EnterEscapedTableReference(c *EscapedTableReferenceContext)

	// EnterJoinedTable is called when entering the joinedTable production.
	EnterJoinedTable(c *JoinedTableContext)

	// EnterNaturalJoinType is called when entering the naturalJoinType production.
	EnterNaturalJoinType(c *NaturalJoinTypeContext)

	// EnterInnerJoinType is called when entering the innerJoinType production.
	EnterInnerJoinType(c *InnerJoinTypeContext)

	// EnterOuterJoinType is called when entering the outerJoinType production.
	EnterOuterJoinType(c *OuterJoinTypeContext)

	// EnterTableFactor is called when entering the tableFactor production.
	EnterTableFactor(c *TableFactorContext)

	// EnterSingleTable is called when entering the singleTable production.
	EnterSingleTable(c *SingleTableContext)

	// EnterSingleTableParens is called when entering the singleTableParens production.
	EnterSingleTableParens(c *SingleTableParensContext)

	// EnterDerivedTable is called when entering the derivedTable production.
	EnterDerivedTable(c *DerivedTableContext)

	// EnterTableReferenceListParens is called when entering the tableReferenceListParens production.
	EnterTableReferenceListParens(c *TableReferenceListParensContext)

	// EnterUnionOption is called when entering the unionOption production.
	EnterUnionOption(c *UnionOptionContext)

	// EnterTableAlias is called when entering the tableAlias production.
	EnterTableAlias(c *TableAliasContext)

	// EnterIndexHintList is called when entering the indexHintList production.
	EnterIndexHintList(c *IndexHintListContext)

	// EnterIndexHint is called when entering the indexHint production.
	EnterIndexHint(c *IndexHintContext)

	// EnterIndexHintType is called when entering the indexHintType production.
	EnterIndexHintType(c *IndexHintTypeContext)

	// EnterKeyOrIndex is called when entering the keyOrIndex production.
	EnterKeyOrIndex(c *KeyOrIndexContext)

	// EnterIndexHintClause is called when entering the indexHintClause production.
	EnterIndexHintClause(c *IndexHintClauseContext)

	// EnterIndexList is called when entering the indexList production.
	EnterIndexList(c *IndexListContext)

	// EnterIndexListElement is called when entering the indexListElement production.
	EnterIndexListElement(c *IndexListElementContext)

	// EnterExprOr is called when entering the exprOr production.
	EnterExprOr(c *ExprOrContext)

	// EnterExprNot is called when entering the exprNot production.
	EnterExprNot(c *ExprNotContext)

	// EnterExprIs is called when entering the exprIs production.
	EnterExprIs(c *ExprIsContext)

	// EnterExprAnd is called when entering the exprAnd production.
	EnterExprAnd(c *ExprAndContext)

	// EnterExprXor is called when entering the exprXor production.
	EnterExprXor(c *ExprXorContext)

	// EnterPrimaryExprPredicate is called when entering the primaryExprPredicate production.
	EnterPrimaryExprPredicate(c *PrimaryExprPredicateContext)

	// EnterPrimaryExprCompare is called when entering the primaryExprCompare production.
	EnterPrimaryExprCompare(c *PrimaryExprCompareContext)

	// EnterPrimaryExprAllAny is called when entering the primaryExprAllAny production.
	EnterPrimaryExprAllAny(c *PrimaryExprAllAnyContext)

	// EnterPrimaryExprIsNull is called when entering the primaryExprIsNull production.
	EnterPrimaryExprIsNull(c *PrimaryExprIsNullContext)

	// EnterCompOp is called when entering the compOp production.
	EnterCompOp(c *CompOpContext)

	// EnterPredicate is called when entering the predicate production.
	EnterPredicate(c *PredicateContext)

	// EnterPredicateExprIn is called when entering the predicateExprIn production.
	EnterPredicateExprIn(c *PredicateExprInContext)

	// EnterPredicateExprBetween is called when entering the predicateExprBetween production.
	EnterPredicateExprBetween(c *PredicateExprBetweenContext)

	// EnterPredicateExprLike is called when entering the predicateExprLike production.
	EnterPredicateExprLike(c *PredicateExprLikeContext)

	// EnterPredicateExprRegex is called when entering the predicateExprRegex production.
	EnterPredicateExprRegex(c *PredicateExprRegexContext)

	// EnterBitExpr is called when entering the bitExpr production.
	EnterBitExpr(c *BitExprContext)

	// EnterSimpleExprConvert is called when entering the simpleExprConvert production.
	EnterSimpleExprConvert(c *SimpleExprConvertContext)

	// EnterSimpleExprVariable is called when entering the simpleExprVariable production.
	EnterSimpleExprVariable(c *SimpleExprVariableContext)

	// EnterSimpleExprCast is called when entering the simpleExprCast production.
	EnterSimpleExprCast(c *SimpleExprCastContext)

	// EnterSimpleExprUnary is called when entering the simpleExprUnary production.
	EnterSimpleExprUnary(c *SimpleExprUnaryContext)

	// EnterSimpleExprOdbc is called when entering the simpleExprOdbc production.
	EnterSimpleExprOdbc(c *SimpleExprOdbcContext)

	// EnterSimpleExprRuntimeFunction is called when entering the simpleExprRuntimeFunction production.
	EnterSimpleExprRuntimeFunction(c *SimpleExprRuntimeFunctionContext)

	// EnterSimpleExprFunction is called when entering the simpleExprFunction production.
	EnterSimpleExprFunction(c *SimpleExprFunctionContext)

	// EnterSimpleExprCollate is called when entering the simpleExprCollate production.
	EnterSimpleExprCollate(c *SimpleExprCollateContext)

	// EnterSimpleExprMatch is called when entering the simpleExprMatch production.
	EnterSimpleExprMatch(c *SimpleExprMatchContext)

	// EnterSimpleExprWindowingFunction is called when entering the simpleExprWindowingFunction production.
	EnterSimpleExprWindowingFunction(c *SimpleExprWindowingFunctionContext)

	// EnterSimpleExprBinary is called when entering the simpleExprBinary production.
	EnterSimpleExprBinary(c *SimpleExprBinaryContext)

	// EnterSimpleExprColumnRef is called when entering the simpleExprColumnRef production.
	EnterSimpleExprColumnRef(c *SimpleExprColumnRefContext)

	// EnterSimpleExprParamMarker is called when entering the simpleExprParamMarker production.
	EnterSimpleExprParamMarker(c *SimpleExprParamMarkerContext)

	// EnterSimpleExprSum is called when entering the simpleExprSum production.
	EnterSimpleExprSum(c *SimpleExprSumContext)

	// EnterSimpleExprConvertUsing is called when entering the simpleExprConvertUsing production.
	EnterSimpleExprConvertUsing(c *SimpleExprConvertUsingContext)

	// EnterSimpleExprSubQuery is called when entering the simpleExprSubQuery production.
	EnterSimpleExprSubQuery(c *SimpleExprSubQueryContext)

	// EnterSimpleExprGroupingOperation is called when entering the simpleExprGroupingOperation production.
	EnterSimpleExprGroupingOperation(c *SimpleExprGroupingOperationContext)

	// EnterSimpleExprNot is called when entering the simpleExprNot production.
	EnterSimpleExprNot(c *SimpleExprNotContext)

	// EnterSimpleExprValues is called when entering the simpleExprValues production.
	EnterSimpleExprValues(c *SimpleExprValuesContext)

	// EnterSimpleExprDefault is called when entering the simpleExprDefault production.
	EnterSimpleExprDefault(c *SimpleExprDefaultContext)

	// EnterSimpleExprList is called when entering the simpleExprList production.
	EnterSimpleExprList(c *SimpleExprListContext)

	// EnterSimpleExprInterval is called when entering the simpleExprInterval production.
	EnterSimpleExprInterval(c *SimpleExprIntervalContext)

	// EnterSimpleExprCase is called when entering the simpleExprCase production.
	EnterSimpleExprCase(c *SimpleExprCaseContext)

	// EnterSimpleExprConcat is called when entering the simpleExprConcat production.
	EnterSimpleExprConcat(c *SimpleExprConcatContext)

	// EnterSimpleExprLiteral is called when entering the simpleExprLiteral production.
	EnterSimpleExprLiteral(c *SimpleExprLiteralContext)

	// EnterArrayCast is called when entering the arrayCast production.
	EnterArrayCast(c *ArrayCastContext)

	// EnterJsonOperator is called when entering the jsonOperator production.
	EnterJsonOperator(c *JsonOperatorContext)

	// EnterSumExpr is called when entering the sumExpr production.
	EnterSumExpr(c *SumExprContext)

	// EnterGroupingOperation is called when entering the groupingOperation production.
	EnterGroupingOperation(c *GroupingOperationContext)

	// EnterWindowFunctionCall is called when entering the windowFunctionCall production.
	EnterWindowFunctionCall(c *WindowFunctionCallContext)

	// EnterWindowingClause is called when entering the windowingClause production.
	EnterWindowingClause(c *WindowingClauseContext)

	// EnterLeadLagInfo is called when entering the leadLagInfo production.
	EnterLeadLagInfo(c *LeadLagInfoContext)

	// EnterNullTreatment is called when entering the nullTreatment production.
	EnterNullTreatment(c *NullTreatmentContext)

	// EnterJsonFunction is called when entering the jsonFunction production.
	EnterJsonFunction(c *JsonFunctionContext)

	// EnterInSumExpr is called when entering the inSumExpr production.
	EnterInSumExpr(c *InSumExprContext)

	// EnterIdentListArg is called when entering the identListArg production.
	EnterIdentListArg(c *IdentListArgContext)

	// EnterIdentList is called when entering the identList production.
	EnterIdentList(c *IdentListContext)

	// EnterFulltextOptions is called when entering the fulltextOptions production.
	EnterFulltextOptions(c *FulltextOptionsContext)

	// EnterRuntimeFunctionCall is called when entering the runtimeFunctionCall production.
	EnterRuntimeFunctionCall(c *RuntimeFunctionCallContext)

	// EnterTimeFunctionParameters is called when entering the timeFunctionParameters production.
	EnterTimeFunctionParameters(c *TimeFunctionParametersContext)

	// EnterFractionalPrecision is called when entering the fractionalPrecision production.
	EnterFractionalPrecision(c *FractionalPrecisionContext)

	// EnterWeightStringLevels is called when entering the weightStringLevels production.
	EnterWeightStringLevels(c *WeightStringLevelsContext)

	// EnterWeightStringLevelListItem is called when entering the weightStringLevelListItem production.
	EnterWeightStringLevelListItem(c *WeightStringLevelListItemContext)

	// EnterDateTimeTtype is called when entering the dateTimeTtype production.
	EnterDateTimeTtype(c *DateTimeTtypeContext)

	// EnterTrimFunction is called when entering the trimFunction production.
	EnterTrimFunction(c *TrimFunctionContext)

	// EnterSubstringFunction is called when entering the substringFunction production.
	EnterSubstringFunction(c *SubstringFunctionContext)

	// EnterFunctionCall is called when entering the functionCall production.
	EnterFunctionCall(c *FunctionCallContext)

	// EnterUdfExprList is called when entering the udfExprList production.
	EnterUdfExprList(c *UdfExprListContext)

	// EnterUdfExpr is called when entering the udfExpr production.
	EnterUdfExpr(c *UdfExprContext)

	// EnterVariable is called when entering the variable production.
	EnterVariable(c *VariableContext)

	// EnterUserVariable is called when entering the userVariable production.
	EnterUserVariable(c *UserVariableContext)

	// EnterSystemVariable is called when entering the systemVariable production.
	EnterSystemVariable(c *SystemVariableContext)

	// EnterWhenExpression is called when entering the whenExpression production.
	EnterWhenExpression(c *WhenExpressionContext)

	// EnterThenExpression is called when entering the thenExpression production.
	EnterThenExpression(c *ThenExpressionContext)

	// EnterElseExpression is called when entering the elseExpression production.
	EnterElseExpression(c *ElseExpressionContext)

	// EnterCastType is called when entering the castType production.
	EnterCastType(c *CastTypeContext)

	// EnterExprList is called when entering the exprList production.
	EnterExprList(c *ExprListContext)

	// EnterCharset is called when entering the charset production.
	EnterCharset(c *CharsetContext)

	// EnterNotRule is called when entering the notRule production.
	EnterNotRule(c *NotRuleContext)

	// EnterNot2Rule is called when entering the not2Rule production.
	EnterNot2Rule(c *Not2RuleContext)

	// EnterInterval is called when entering the interval production.
	EnterInterval(c *IntervalContext)

	// EnterIntervalTimeStamp is called when entering the intervalTimeStamp production.
	EnterIntervalTimeStamp(c *IntervalTimeStampContext)

	// EnterExprListWithParentheses is called when entering the exprListWithParentheses production.
	EnterExprListWithParentheses(c *ExprListWithParenthesesContext)

	// EnterExprWithParentheses is called when entering the exprWithParentheses production.
	EnterExprWithParentheses(c *ExprWithParenthesesContext)

	// EnterSimpleExprWithParentheses is called when entering the simpleExprWithParentheses production.
	EnterSimpleExprWithParentheses(c *SimpleExprWithParenthesesContext)

	// EnterOrderList is called when entering the orderList production.
	EnterOrderList(c *OrderListContext)

	// EnterOrderExpression is called when entering the orderExpression production.
	EnterOrderExpression(c *OrderExpressionContext)

	// EnterNchar is called when entering the nchar production.
	EnterNchar(c *NcharContext)

	// EnterRealType is called when entering the realType production.
	EnterRealType(c *RealTypeContext)

	// EnterFieldLength is called when entering the fieldLength production.
	EnterFieldLength(c *FieldLengthContext)

	// EnterCharsetWithOptBinary is called when entering the charsetWithOptBinary production.
	EnterCharsetWithOptBinary(c *CharsetWithOptBinaryContext)

	// EnterAscii is called when entering the ascii production.
	EnterAscii(c *AsciiContext)

	// EnterUnicode is called when entering the unicode production.
	EnterUnicode(c *UnicodeContext)

	// EnterWsNumCodepoints is called when entering the wsNumCodepoints production.
	EnterWsNumCodepoints(c *WsNumCodepointsContext)

	// EnterTypeDatetimePrecision is called when entering the typeDatetimePrecision production.
	EnterTypeDatetimePrecision(c *TypeDatetimePrecisionContext)

	// EnterCharsetName is called when entering the charsetName production.
	EnterCharsetName(c *CharsetNameContext)

	// EnterUsePartition is called when entering the usePartition production.
	EnterUsePartition(c *UsePartitionContext)

	// EnterFieldIdentifier is called when entering the fieldIdentifier production.
	EnterFieldIdentifier(c *FieldIdentifierContext)

	// EnterColumnInternalRef is called when entering the columnInternalRef production.
	EnterColumnInternalRef(c *ColumnInternalRefContext)

	// EnterColumnInternalRefList is called when entering the columnInternalRefList production.
	EnterColumnInternalRefList(c *ColumnInternalRefListContext)

	// EnterColumnRef is called when entering the columnRef production.
	EnterColumnRef(c *ColumnRefContext)

	// EnterTableWild is called when entering the tableWild production.
	EnterTableWild(c *TableWildContext)

	// EnterTableRef is called when entering the tableRef production.
	EnterTableRef(c *TableRefContext)

	// EnterLabelIdentifier is called when entering the labelIdentifier production.
	EnterLabelIdentifier(c *LabelIdentifierContext)

	// EnterLabelRef is called when entering the labelRef production.
	EnterLabelRef(c *LabelRefContext)

	// EnterWindowName is called when entering the windowName production.
	EnterWindowName(c *WindowNameContext)

	// EnterPureIdentifier is called when entering the pureIdentifier production.
	EnterPureIdentifier(c *PureIdentifierContext)

	// EnterIdentifier is called when entering the identifier production.
	EnterIdentifier(c *IdentifierContext)

	// EnterIdentifierList is called when entering the identifierList production.
	EnterIdentifierList(c *IdentifierListContext)

	// EnterIdentifierListWithParentheses is called when entering the identifierListWithParentheses production.
	EnterIdentifierListWithParentheses(c *IdentifierListWithParenthesesContext)

	// EnterQualifiedIdentifier is called when entering the qualifiedIdentifier production.
	EnterQualifiedIdentifier(c *QualifiedIdentifierContext)

	// EnterSimpleIdentifier is called when entering the simpleIdentifier production.
	EnterSimpleIdentifier(c *SimpleIdentifierContext)

	// EnterDotIdentifier is called when entering the dotIdentifier production.
	EnterDotIdentifier(c *DotIdentifierContext)

	// EnterUlong_number is called when entering the ulong_number production.
	EnterUlong_number(c *Ulong_numberContext)

	// EnterReal_ulong_number is called when entering the real_ulong_number production.
	EnterReal_ulong_number(c *Real_ulong_numberContext)

	// EnterUlonglong_number is called when entering the ulonglong_number production.
	EnterUlonglong_number(c *Ulonglong_numberContext)

	// EnterReal_ulonglong_number is called when entering the real_ulonglong_number production.
	EnterReal_ulonglong_number(c *Real_ulonglong_numberContext)

	// EnterLiteral is called when entering the literal production.
	EnterLiteral(c *LiteralContext)

	// EnterTextStringLiteral is called when entering the textStringLiteral production.
	EnterTextStringLiteral(c *TextStringLiteralContext)

	// EnterTextString is called when entering the textString production.
	EnterTextString(c *TextStringContext)

	// EnterTextLiteral is called when entering the textLiteral production.
	EnterTextLiteral(c *TextLiteralContext)

	// EnterNumLiteral is called when entering the numLiteral production.
	EnterNumLiteral(c *NumLiteralContext)

	// EnterBoolLiteral is called when entering the boolLiteral production.
	EnterBoolLiteral(c *BoolLiteralContext)

	// EnterNullLiteral is called when entering the nullLiteral production.
	EnterNullLiteral(c *NullLiteralContext)

	// EnterTemporalLiteral is called when entering the temporalLiteral production.
	EnterTemporalLiteral(c *TemporalLiteralContext)

	// EnterFloatOptions is called when entering the floatOptions production.
	EnterFloatOptions(c *FloatOptionsContext)

	// EnterStandardFloatOptions is called when entering the standardFloatOptions production.
	EnterStandardFloatOptions(c *StandardFloatOptionsContext)

	// EnterPrecision is called when entering the precision production.
	EnterPrecision(c *PrecisionContext)

	// EnterTextOrIdentifier is called when entering the textOrIdentifier production.
	EnterTextOrIdentifier(c *TextOrIdentifierContext)

	// EnterParentheses is called when entering the parentheses production.
	EnterParentheses(c *ParenthesesContext)

	// EnterEqual is called when entering the equal production.
	EnterEqual(c *EqualContext)

	// EnterVarIdentType is called when entering the varIdentType production.
	EnterVarIdentType(c *VarIdentTypeContext)

	// ExitQuery is called when exiting the query production.
	ExitQuery(c *QueryContext)

	// ExitSimpleStatement is called when exiting the simpleStatement production.
	ExitSimpleStatement(c *SimpleStatementContext)

	// ExitValues is called when exiting the values production.
	ExitValues(c *ValuesContext)

	// ExitSelectStatement is called when exiting the selectStatement production.
	ExitSelectStatement(c *SelectStatementContext)

	// ExitQueryExpression is called when exiting the queryExpression production.
	ExitQueryExpression(c *QueryExpressionContext)

	// ExitQueryExpressionBody is called when exiting the queryExpressionBody production.
	ExitQueryExpressionBody(c *QueryExpressionBodyContext)

	// ExitQueryExpressionParens is called when exiting the queryExpressionParens production.
	ExitQueryExpressionParens(c *QueryExpressionParensContext)

	// ExitQueryPrimary is called when exiting the queryPrimary production.
	ExitQueryPrimary(c *QueryPrimaryContext)

	// ExitQuerySpecification is called when exiting the querySpecification production.
	ExitQuerySpecification(c *QuerySpecificationContext)

	// ExitSubquery is called when exiting the subquery production.
	ExitSubquery(c *SubqueryContext)

	// ExitQuerySpecOption is called when exiting the querySpecOption production.
	ExitQuerySpecOption(c *QuerySpecOptionContext)

	// ExitLimitClause is called when exiting the limitClause production.
	ExitLimitClause(c *LimitClauseContext)

	// ExitLimitOptions is called when exiting the limitOptions production.
	ExitLimitOptions(c *LimitOptionsContext)

	// ExitLimitOption is called when exiting the limitOption production.
	ExitLimitOption(c *LimitOptionContext)

	// ExitHavingClause is called when exiting the havingClause production.
	ExitHavingClause(c *HavingClauseContext)

	// ExitWindowClause is called when exiting the windowClause production.
	ExitWindowClause(c *WindowClauseContext)

	// ExitWindowDefinition is called when exiting the windowDefinition production.
	ExitWindowDefinition(c *WindowDefinitionContext)

	// ExitWindowSpec is called when exiting the windowSpec production.
	ExitWindowSpec(c *WindowSpecContext)

	// ExitWindowSpecDetails is called when exiting the windowSpecDetails production.
	ExitWindowSpecDetails(c *WindowSpecDetailsContext)

	// ExitWindowFrameClause is called when exiting the windowFrameClause production.
	ExitWindowFrameClause(c *WindowFrameClauseContext)

	// ExitWindowFrameUnits is called when exiting the windowFrameUnits production.
	ExitWindowFrameUnits(c *WindowFrameUnitsContext)

	// ExitWindowFrameExtent is called when exiting the windowFrameExtent production.
	ExitWindowFrameExtent(c *WindowFrameExtentContext)

	// ExitWindowFrameStart is called when exiting the windowFrameStart production.
	ExitWindowFrameStart(c *WindowFrameStartContext)

	// ExitWindowFrameBetween is called when exiting the windowFrameBetween production.
	ExitWindowFrameBetween(c *WindowFrameBetweenContext)

	// ExitWindowFrameBound is called when exiting the windowFrameBound production.
	ExitWindowFrameBound(c *WindowFrameBoundContext)

	// ExitWindowFrameExclusion is called when exiting the windowFrameExclusion production.
	ExitWindowFrameExclusion(c *WindowFrameExclusionContext)

	// ExitWithClause is called when exiting the withClause production.
	ExitWithClause(c *WithClauseContext)

	// ExitCommonTableExpression is called when exiting the commonTableExpression production.
	ExitCommonTableExpression(c *CommonTableExpressionContext)

	// ExitGroupByClause is called when exiting the groupByClause production.
	ExitGroupByClause(c *GroupByClauseContext)

	// ExitOlapOption is called when exiting the olapOption production.
	ExitOlapOption(c *OlapOptionContext)

	// ExitOrderClause is called when exiting the orderClause production.
	ExitOrderClause(c *OrderClauseContext)

	// ExitDirection is called when exiting the direction production.
	ExitDirection(c *DirectionContext)

	// ExitFromClause is called when exiting the fromClause production.
	ExitFromClause(c *FromClauseContext)

	// ExitTableReferenceList is called when exiting the tableReferenceList production.
	ExitTableReferenceList(c *TableReferenceListContext)

	// ExitTableValueConstructor is called when exiting the tableValueConstructor production.
	ExitTableValueConstructor(c *TableValueConstructorContext)

	// ExitExplicitTable is called when exiting the explicitTable production.
	ExitExplicitTable(c *ExplicitTableContext)

	// ExitRowValueExplicit is called when exiting the rowValueExplicit production.
	ExitRowValueExplicit(c *RowValueExplicitContext)

	// ExitSelectOption is called when exiting the selectOption production.
	ExitSelectOption(c *SelectOptionContext)

	// ExitSelectItemList is called when exiting the selectItemList production.
	ExitSelectItemList(c *SelectItemListContext)

	// ExitSelectItem is called when exiting the selectItem production.
	ExitSelectItem(c *SelectItemContext)

	// ExitSelectAlias is called when exiting the selectAlias production.
	ExitSelectAlias(c *SelectAliasContext)

	// ExitWhereClause is called when exiting the whereClause production.
	ExitWhereClause(c *WhereClauseContext)

	// ExitTableReference is called when exiting the tableReference production.
	ExitTableReference(c *TableReferenceContext)

	// ExitEscapedTableReference is called when exiting the escapedTableReference production.
	ExitEscapedTableReference(c *EscapedTableReferenceContext)

	// ExitJoinedTable is called when exiting the joinedTable production.
	ExitJoinedTable(c *JoinedTableContext)

	// ExitNaturalJoinType is called when exiting the naturalJoinType production.
	ExitNaturalJoinType(c *NaturalJoinTypeContext)

	// ExitInnerJoinType is called when exiting the innerJoinType production.
	ExitInnerJoinType(c *InnerJoinTypeContext)

	// ExitOuterJoinType is called when exiting the outerJoinType production.
	ExitOuterJoinType(c *OuterJoinTypeContext)

	// ExitTableFactor is called when exiting the tableFactor production.
	ExitTableFactor(c *TableFactorContext)

	// ExitSingleTable is called when exiting the singleTable production.
	ExitSingleTable(c *SingleTableContext)

	// ExitSingleTableParens is called when exiting the singleTableParens production.
	ExitSingleTableParens(c *SingleTableParensContext)

	// ExitDerivedTable is called when exiting the derivedTable production.
	ExitDerivedTable(c *DerivedTableContext)

	// ExitTableReferenceListParens is called when exiting the tableReferenceListParens production.
	ExitTableReferenceListParens(c *TableReferenceListParensContext)

	// ExitUnionOption is called when exiting the unionOption production.
	ExitUnionOption(c *UnionOptionContext)

	// ExitTableAlias is called when exiting the tableAlias production.
	ExitTableAlias(c *TableAliasContext)

	// ExitIndexHintList is called when exiting the indexHintList production.
	ExitIndexHintList(c *IndexHintListContext)

	// ExitIndexHint is called when exiting the indexHint production.
	ExitIndexHint(c *IndexHintContext)

	// ExitIndexHintType is called when exiting the indexHintType production.
	ExitIndexHintType(c *IndexHintTypeContext)

	// ExitKeyOrIndex is called when exiting the keyOrIndex production.
	ExitKeyOrIndex(c *KeyOrIndexContext)

	// ExitIndexHintClause is called when exiting the indexHintClause production.
	ExitIndexHintClause(c *IndexHintClauseContext)

	// ExitIndexList is called when exiting the indexList production.
	ExitIndexList(c *IndexListContext)

	// ExitIndexListElement is called when exiting the indexListElement production.
	ExitIndexListElement(c *IndexListElementContext)

	// ExitExprOr is called when exiting the exprOr production.
	ExitExprOr(c *ExprOrContext)

	// ExitExprNot is called when exiting the exprNot production.
	ExitExprNot(c *ExprNotContext)

	// ExitExprIs is called when exiting the exprIs production.
	ExitExprIs(c *ExprIsContext)

	// ExitExprAnd is called when exiting the exprAnd production.
	ExitExprAnd(c *ExprAndContext)

	// ExitExprXor is called when exiting the exprXor production.
	ExitExprXor(c *ExprXorContext)

	// ExitPrimaryExprPredicate is called when exiting the primaryExprPredicate production.
	ExitPrimaryExprPredicate(c *PrimaryExprPredicateContext)

	// ExitPrimaryExprCompare is called when exiting the primaryExprCompare production.
	ExitPrimaryExprCompare(c *PrimaryExprCompareContext)

	// ExitPrimaryExprAllAny is called when exiting the primaryExprAllAny production.
	ExitPrimaryExprAllAny(c *PrimaryExprAllAnyContext)

	// ExitPrimaryExprIsNull is called when exiting the primaryExprIsNull production.
	ExitPrimaryExprIsNull(c *PrimaryExprIsNullContext)

	// ExitCompOp is called when exiting the compOp production.
	ExitCompOp(c *CompOpContext)

	// ExitPredicate is called when exiting the predicate production.
	ExitPredicate(c *PredicateContext)

	// ExitPredicateExprIn is called when exiting the predicateExprIn production.
	ExitPredicateExprIn(c *PredicateExprInContext)

	// ExitPredicateExprBetween is called when exiting the predicateExprBetween production.
	ExitPredicateExprBetween(c *PredicateExprBetweenContext)

	// ExitPredicateExprLike is called when exiting the predicateExprLike production.
	ExitPredicateExprLike(c *PredicateExprLikeContext)

	// ExitPredicateExprRegex is called when exiting the predicateExprRegex production.
	ExitPredicateExprRegex(c *PredicateExprRegexContext)

	// ExitBitExpr is called when exiting the bitExpr production.
	ExitBitExpr(c *BitExprContext)

	// ExitSimpleExprConvert is called when exiting the simpleExprConvert production.
	ExitSimpleExprConvert(c *SimpleExprConvertContext)

	// ExitSimpleExprVariable is called when exiting the simpleExprVariable production.
	ExitSimpleExprVariable(c *SimpleExprVariableContext)

	// ExitSimpleExprCast is called when exiting the simpleExprCast production.
	ExitSimpleExprCast(c *SimpleExprCastContext)

	// ExitSimpleExprUnary is called when exiting the simpleExprUnary production.
	ExitSimpleExprUnary(c *SimpleExprUnaryContext)

	// ExitSimpleExprOdbc is called when exiting the simpleExprOdbc production.
	ExitSimpleExprOdbc(c *SimpleExprOdbcContext)

	// ExitSimpleExprRuntimeFunction is called when exiting the simpleExprRuntimeFunction production.
	ExitSimpleExprRuntimeFunction(c *SimpleExprRuntimeFunctionContext)

	// ExitSimpleExprFunction is called when exiting the simpleExprFunction production.
	ExitSimpleExprFunction(c *SimpleExprFunctionContext)

	// ExitSimpleExprCollate is called when exiting the simpleExprCollate production.
	ExitSimpleExprCollate(c *SimpleExprCollateContext)

	// ExitSimpleExprMatch is called when exiting the simpleExprMatch production.
	ExitSimpleExprMatch(c *SimpleExprMatchContext)

	// ExitSimpleExprWindowingFunction is called when exiting the simpleExprWindowingFunction production.
	ExitSimpleExprWindowingFunction(c *SimpleExprWindowingFunctionContext)

	// ExitSimpleExprBinary is called when exiting the simpleExprBinary production.
	ExitSimpleExprBinary(c *SimpleExprBinaryContext)

	// ExitSimpleExprColumnRef is called when exiting the simpleExprColumnRef production.
	ExitSimpleExprColumnRef(c *SimpleExprColumnRefContext)

	// ExitSimpleExprParamMarker is called when exiting the simpleExprParamMarker production.
	ExitSimpleExprParamMarker(c *SimpleExprParamMarkerContext)

	// ExitSimpleExprSum is called when exiting the simpleExprSum production.
	ExitSimpleExprSum(c *SimpleExprSumContext)

	// ExitSimpleExprConvertUsing is called when exiting the simpleExprConvertUsing production.
	ExitSimpleExprConvertUsing(c *SimpleExprConvertUsingContext)

	// ExitSimpleExprSubQuery is called when exiting the simpleExprSubQuery production.
	ExitSimpleExprSubQuery(c *SimpleExprSubQueryContext)

	// ExitSimpleExprGroupingOperation is called when exiting the simpleExprGroupingOperation production.
	ExitSimpleExprGroupingOperation(c *SimpleExprGroupingOperationContext)

	// ExitSimpleExprNot is called when exiting the simpleExprNot production.
	ExitSimpleExprNot(c *SimpleExprNotContext)

	// ExitSimpleExprValues is called when exiting the simpleExprValues production.
	ExitSimpleExprValues(c *SimpleExprValuesContext)

	// ExitSimpleExprDefault is called when exiting the simpleExprDefault production.
	ExitSimpleExprDefault(c *SimpleExprDefaultContext)

	// ExitSimpleExprList is called when exiting the simpleExprList production.
	ExitSimpleExprList(c *SimpleExprListContext)

	// ExitSimpleExprInterval is called when exiting the simpleExprInterval production.
	ExitSimpleExprInterval(c *SimpleExprIntervalContext)

	// ExitSimpleExprCase is called when exiting the simpleExprCase production.
	ExitSimpleExprCase(c *SimpleExprCaseContext)

	// ExitSimpleExprConcat is called when exiting the simpleExprConcat production.
	ExitSimpleExprConcat(c *SimpleExprConcatContext)

	// ExitSimpleExprLiteral is called when exiting the simpleExprLiteral production.
	ExitSimpleExprLiteral(c *SimpleExprLiteralContext)

	// ExitArrayCast is called when exiting the arrayCast production.
	ExitArrayCast(c *ArrayCastContext)

	// ExitJsonOperator is called when exiting the jsonOperator production.
	ExitJsonOperator(c *JsonOperatorContext)

	// ExitSumExpr is called when exiting the sumExpr production.
	ExitSumExpr(c *SumExprContext)

	// ExitGroupingOperation is called when exiting the groupingOperation production.
	ExitGroupingOperation(c *GroupingOperationContext)

	// ExitWindowFunctionCall is called when exiting the windowFunctionCall production.
	ExitWindowFunctionCall(c *WindowFunctionCallContext)

	// ExitWindowingClause is called when exiting the windowingClause production.
	ExitWindowingClause(c *WindowingClauseContext)

	// ExitLeadLagInfo is called when exiting the leadLagInfo production.
	ExitLeadLagInfo(c *LeadLagInfoContext)

	// ExitNullTreatment is called when exiting the nullTreatment production.
	ExitNullTreatment(c *NullTreatmentContext)

	// ExitJsonFunction is called when exiting the jsonFunction production.
	ExitJsonFunction(c *JsonFunctionContext)

	// ExitInSumExpr is called when exiting the inSumExpr production.
	ExitInSumExpr(c *InSumExprContext)

	// ExitIdentListArg is called when exiting the identListArg production.
	ExitIdentListArg(c *IdentListArgContext)

	// ExitIdentList is called when exiting the identList production.
	ExitIdentList(c *IdentListContext)

	// ExitFulltextOptions is called when exiting the fulltextOptions production.
	ExitFulltextOptions(c *FulltextOptionsContext)

	// ExitRuntimeFunctionCall is called when exiting the runtimeFunctionCall production.
	ExitRuntimeFunctionCall(c *RuntimeFunctionCallContext)

	// ExitTimeFunctionParameters is called when exiting the timeFunctionParameters production.
	ExitTimeFunctionParameters(c *TimeFunctionParametersContext)

	// ExitFractionalPrecision is called when exiting the fractionalPrecision production.
	ExitFractionalPrecision(c *FractionalPrecisionContext)

	// ExitWeightStringLevels is called when exiting the weightStringLevels production.
	ExitWeightStringLevels(c *WeightStringLevelsContext)

	// ExitWeightStringLevelListItem is called when exiting the weightStringLevelListItem production.
	ExitWeightStringLevelListItem(c *WeightStringLevelListItemContext)

	// ExitDateTimeTtype is called when exiting the dateTimeTtype production.
	ExitDateTimeTtype(c *DateTimeTtypeContext)

	// ExitTrimFunction is called when exiting the trimFunction production.
	ExitTrimFunction(c *TrimFunctionContext)

	// ExitSubstringFunction is called when exiting the substringFunction production.
	ExitSubstringFunction(c *SubstringFunctionContext)

	// ExitFunctionCall is called when exiting the functionCall production.
	ExitFunctionCall(c *FunctionCallContext)

	// ExitUdfExprList is called when exiting the udfExprList production.
	ExitUdfExprList(c *UdfExprListContext)

	// ExitUdfExpr is called when exiting the udfExpr production.
	ExitUdfExpr(c *UdfExprContext)

	// ExitVariable is called when exiting the variable production.
	ExitVariable(c *VariableContext)

	// ExitUserVariable is called when exiting the userVariable production.
	ExitUserVariable(c *UserVariableContext)

	// ExitSystemVariable is called when exiting the systemVariable production.
	ExitSystemVariable(c *SystemVariableContext)

	// ExitWhenExpression is called when exiting the whenExpression production.
	ExitWhenExpression(c *WhenExpressionContext)

	// ExitThenExpression is called when exiting the thenExpression production.
	ExitThenExpression(c *ThenExpressionContext)

	// ExitElseExpression is called when exiting the elseExpression production.
	ExitElseExpression(c *ElseExpressionContext)

	// ExitCastType is called when exiting the castType production.
	ExitCastType(c *CastTypeContext)

	// ExitExprList is called when exiting the exprList production.
	ExitExprList(c *ExprListContext)

	// ExitCharset is called when exiting the charset production.
	ExitCharset(c *CharsetContext)

	// ExitNotRule is called when exiting the notRule production.
	ExitNotRule(c *NotRuleContext)

	// ExitNot2Rule is called when exiting the not2Rule production.
	ExitNot2Rule(c *Not2RuleContext)

	// ExitInterval is called when exiting the interval production.
	ExitInterval(c *IntervalContext)

	// ExitIntervalTimeStamp is called when exiting the intervalTimeStamp production.
	ExitIntervalTimeStamp(c *IntervalTimeStampContext)

	// ExitExprListWithParentheses is called when exiting the exprListWithParentheses production.
	ExitExprListWithParentheses(c *ExprListWithParenthesesContext)

	// ExitExprWithParentheses is called when exiting the exprWithParentheses production.
	ExitExprWithParentheses(c *ExprWithParenthesesContext)

	// ExitSimpleExprWithParentheses is called when exiting the simpleExprWithParentheses production.
	ExitSimpleExprWithParentheses(c *SimpleExprWithParenthesesContext)

	// ExitOrderList is called when exiting the orderList production.
	ExitOrderList(c *OrderListContext)

	// ExitOrderExpression is called when exiting the orderExpression production.
	ExitOrderExpression(c *OrderExpressionContext)

	// ExitNchar is called when exiting the nchar production.
	ExitNchar(c *NcharContext)

	// ExitRealType is called when exiting the realType production.
	ExitRealType(c *RealTypeContext)

	// ExitFieldLength is called when exiting the fieldLength production.
	ExitFieldLength(c *FieldLengthContext)

	// ExitCharsetWithOptBinary is called when exiting the charsetWithOptBinary production.
	ExitCharsetWithOptBinary(c *CharsetWithOptBinaryContext)

	// ExitAscii is called when exiting the ascii production.
	ExitAscii(c *AsciiContext)

	// ExitUnicode is called when exiting the unicode production.
	ExitUnicode(c *UnicodeContext)

	// ExitWsNumCodepoints is called when exiting the wsNumCodepoints production.
	ExitWsNumCodepoints(c *WsNumCodepointsContext)

	// ExitTypeDatetimePrecision is called when exiting the typeDatetimePrecision production.
	ExitTypeDatetimePrecision(c *TypeDatetimePrecisionContext)

	// ExitCharsetName is called when exiting the charsetName production.
	ExitCharsetName(c *CharsetNameContext)

	// ExitUsePartition is called when exiting the usePartition production.
	ExitUsePartition(c *UsePartitionContext)

	// ExitFieldIdentifier is called when exiting the fieldIdentifier production.
	ExitFieldIdentifier(c *FieldIdentifierContext)

	// ExitColumnInternalRef is called when exiting the columnInternalRef production.
	ExitColumnInternalRef(c *ColumnInternalRefContext)

	// ExitColumnInternalRefList is called when exiting the columnInternalRefList production.
	ExitColumnInternalRefList(c *ColumnInternalRefListContext)

	// ExitColumnRef is called when exiting the columnRef production.
	ExitColumnRef(c *ColumnRefContext)

	// ExitTableWild is called when exiting the tableWild production.
	ExitTableWild(c *TableWildContext)

	// ExitTableRef is called when exiting the tableRef production.
	ExitTableRef(c *TableRefContext)

	// ExitLabelIdentifier is called when exiting the labelIdentifier production.
	ExitLabelIdentifier(c *LabelIdentifierContext)

	// ExitLabelRef is called when exiting the labelRef production.
	ExitLabelRef(c *LabelRefContext)

	// ExitWindowName is called when exiting the windowName production.
	ExitWindowName(c *WindowNameContext)

	// ExitPureIdentifier is called when exiting the pureIdentifier production.
	ExitPureIdentifier(c *PureIdentifierContext)

	// ExitIdentifier is called when exiting the identifier production.
	ExitIdentifier(c *IdentifierContext)

	// ExitIdentifierList is called when exiting the identifierList production.
	ExitIdentifierList(c *IdentifierListContext)

	// ExitIdentifierListWithParentheses is called when exiting the identifierListWithParentheses production.
	ExitIdentifierListWithParentheses(c *IdentifierListWithParenthesesContext)

	// ExitQualifiedIdentifier is called when exiting the qualifiedIdentifier production.
	ExitQualifiedIdentifier(c *QualifiedIdentifierContext)

	// ExitSimpleIdentifier is called when exiting the simpleIdentifier production.
	ExitSimpleIdentifier(c *SimpleIdentifierContext)

	// ExitDotIdentifier is called when exiting the dotIdentifier production.
	ExitDotIdentifier(c *DotIdentifierContext)

	// ExitUlong_number is called when exiting the ulong_number production.
	ExitUlong_number(c *Ulong_numberContext)

	// ExitReal_ulong_number is called when exiting the real_ulong_number production.
	ExitReal_ulong_number(c *Real_ulong_numberContext)

	// ExitUlonglong_number is called when exiting the ulonglong_number production.
	ExitUlonglong_number(c *Ulonglong_numberContext)

	// ExitReal_ulonglong_number is called when exiting the real_ulonglong_number production.
	ExitReal_ulonglong_number(c *Real_ulonglong_numberContext)

	// ExitLiteral is called when exiting the literal production.
	ExitLiteral(c *LiteralContext)

	// ExitTextStringLiteral is called when exiting the textStringLiteral production.
	ExitTextStringLiteral(c *TextStringLiteralContext)

	// ExitTextString is called when exiting the textString production.
	ExitTextString(c *TextStringContext)

	// ExitTextLiteral is called when exiting the textLiteral production.
	ExitTextLiteral(c *TextLiteralContext)

	// ExitNumLiteral is called when exiting the numLiteral production.
	ExitNumLiteral(c *NumLiteralContext)

	// ExitBoolLiteral is called when exiting the boolLiteral production.
	ExitBoolLiteral(c *BoolLiteralContext)

	// ExitNullLiteral is called when exiting the nullLiteral production.
	ExitNullLiteral(c *NullLiteralContext)

	// ExitTemporalLiteral is called when exiting the temporalLiteral production.
	ExitTemporalLiteral(c *TemporalLiteralContext)

	// ExitFloatOptions is called when exiting the floatOptions production.
	ExitFloatOptions(c *FloatOptionsContext)

	// ExitStandardFloatOptions is called when exiting the standardFloatOptions production.
	ExitStandardFloatOptions(c *StandardFloatOptionsContext)

	// ExitPrecision is called when exiting the precision production.
	ExitPrecision(c *PrecisionContext)

	// ExitTextOrIdentifier is called when exiting the textOrIdentifier production.
	ExitTextOrIdentifier(c *TextOrIdentifierContext)

	// ExitParentheses is called when exiting the parentheses production.
	ExitParentheses(c *ParenthesesContext)

	// ExitEqual is called when exiting the equal production.
	ExitEqual(c *EqualContext)

	// ExitVarIdentType is called when exiting the varIdentType production.
	ExitVarIdentType(c *VarIdentTypeContext)
}
