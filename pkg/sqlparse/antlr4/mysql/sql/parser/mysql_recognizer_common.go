package parser

type SqlMode int

const (
	NoMode             SqlMode = 0
	AnsiQuotes         SqlMode = 1 << 0
	HighNotPrecedence  SqlMode = 1 << 1
	PipesAsConcat      SqlMode = 1 << 2
	IgnoreSpace        SqlMode = 1 << 3
	NoBackslashEscapes SqlMode = 1 << 4
)

type MySQLRecognizerCommon struct {
	ServerVersion int
	SqlMode       SqlMode
}

func (l *MySQLRecognizerCommon) IsSqlModeActive(sqlMode SqlMode) bool {
	return (sqlMode & l.SqlMode) != 0
}

func third(cond bool, first int, second int) int {
	if cond {
		return first
	} else {
		return second
	}
}

func checkVersion() bool {
	// TODO: 支持checkVersion
	return false
}
func checkCharset(_ string) int {
	// TODO: 支持charset
	return MySQLLexerIDENTIFIER
}
