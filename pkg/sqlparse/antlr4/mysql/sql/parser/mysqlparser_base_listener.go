// Code generated from MySQLParser.g4 by ANTLR 4.13.1. DO NOT EDIT.

package parser // MySQLParser

import "github.com/antlr4-go/antlr/v4"

// BaseMySQLParserListener is a complete listener for a parse tree produced by MySQLParser.
type BaseMySQLParserListener struct{}

var _ MySQLParserListener = &BaseMySQLParserListener{}

// VisitTerminal is called when a terminal node is visited.
func (s *BaseMySQLParserListener) VisitTerminal(node antlr.TerminalNode) {}

// VisitErrorNode is called when an error node is visited.
func (s *BaseMySQLParserListener) VisitErrorNode(node antlr.ErrorNode) {}

// EnterEveryRule is called when any rule is entered.
func (s *BaseMySQLParserListener) EnterEveryRule(ctx antlr.ParserRuleContext) {}

// ExitEveryRule is called when any rule is exited.
func (s *BaseMySQLParserListener) ExitEveryRule(ctx antlr.ParserRuleContext) {}

// EnterQuery is called when production query is entered.
func (s *BaseMySQLParserListener) EnterQuery(ctx *QueryContext) {}

// ExitQuery is called when production query is exited.
func (s *BaseMySQLParserListener) ExitQuery(ctx *QueryContext) {}

// EnterSimpleStatement is called when production simpleStatement is entered.
func (s *BaseMySQLParserListener) EnterSimpleStatement(ctx *SimpleStatementContext) {}

// ExitSimpleStatement is called when production simpleStatement is exited.
func (s *BaseMySQLParserListener) ExitSimpleStatement(ctx *SimpleStatementContext) {}

// EnterValues is called when production values is entered.
func (s *BaseMySQLParserListener) EnterValues(ctx *ValuesContext) {}

// ExitValues is called when production values is exited.
func (s *BaseMySQLParserListener) ExitValues(ctx *ValuesContext) {}

// EnterSelectStatement is called when production selectStatement is entered.
func (s *BaseMySQLParserListener) EnterSelectStatement(ctx *SelectStatementContext) {}

// ExitSelectStatement is called when production selectStatement is exited.
func (s *BaseMySQLParserListener) ExitSelectStatement(ctx *SelectStatementContext) {}

// EnterQueryExpression is called when production queryExpression is entered.
func (s *BaseMySQLParserListener) EnterQueryExpression(ctx *QueryExpressionContext) {}

// ExitQueryExpression is called when production queryExpression is exited.
func (s *BaseMySQLParserListener) ExitQueryExpression(ctx *QueryExpressionContext) {}

// EnterQueryExpressionBody is called when production queryExpressionBody is entered.
func (s *BaseMySQLParserListener) EnterQueryExpressionBody(ctx *QueryExpressionBodyContext) {}

// ExitQueryExpressionBody is called when production queryExpressionBody is exited.
func (s *BaseMySQLParserListener) ExitQueryExpressionBody(ctx *QueryExpressionBodyContext) {}

// EnterQueryExpressionParens is called when production queryExpressionParens is entered.
func (s *BaseMySQLParserListener) EnterQueryExpressionParens(ctx *QueryExpressionParensContext) {}

// ExitQueryExpressionParens is called when production queryExpressionParens is exited.
func (s *BaseMySQLParserListener) ExitQueryExpressionParens(ctx *QueryExpressionParensContext) {}

// EnterQueryPrimary is called when production queryPrimary is entered.
func (s *BaseMySQLParserListener) EnterQueryPrimary(ctx *QueryPrimaryContext) {}

// ExitQueryPrimary is called when production queryPrimary is exited.
func (s *BaseMySQLParserListener) ExitQueryPrimary(ctx *QueryPrimaryContext) {}

// EnterQuerySpecification is called when production querySpecification is entered.
func (s *BaseMySQLParserListener) EnterQuerySpecification(ctx *QuerySpecificationContext) {}

// ExitQuerySpecification is called when production querySpecification is exited.
func (s *BaseMySQLParserListener) ExitQuerySpecification(ctx *QuerySpecificationContext) {}

// EnterSubquery is called when production subquery is entered.
func (s *BaseMySQLParserListener) EnterSubquery(ctx *SubqueryContext) {}

// ExitSubquery is called when production subquery is exited.
func (s *BaseMySQLParserListener) ExitSubquery(ctx *SubqueryContext) {}

// EnterQuerySpecOption is called when production querySpecOption is entered.
func (s *BaseMySQLParserListener) EnterQuerySpecOption(ctx *QuerySpecOptionContext) {}

// ExitQuerySpecOption is called when production querySpecOption is exited.
func (s *BaseMySQLParserListener) ExitQuerySpecOption(ctx *QuerySpecOptionContext) {}

// EnterLimitClause is called when production limitClause is entered.
func (s *BaseMySQLParserListener) EnterLimitClause(ctx *LimitClauseContext) {}

// ExitLimitClause is called when production limitClause is exited.
func (s *BaseMySQLParserListener) ExitLimitClause(ctx *LimitClauseContext) {}

// EnterLimitOptions is called when production limitOptions is entered.
func (s *BaseMySQLParserListener) EnterLimitOptions(ctx *LimitOptionsContext) {}

// ExitLimitOptions is called when production limitOptions is exited.
func (s *BaseMySQLParserListener) ExitLimitOptions(ctx *LimitOptionsContext) {}

// EnterLimitOption is called when production limitOption is entered.
func (s *BaseMySQLParserListener) EnterLimitOption(ctx *LimitOptionContext) {}

// ExitLimitOption is called when production limitOption is exited.
func (s *BaseMySQLParserListener) ExitLimitOption(ctx *LimitOptionContext) {}

// EnterHavingClause is called when production havingClause is entered.
func (s *BaseMySQLParserListener) EnterHavingClause(ctx *HavingClauseContext) {}

// ExitHavingClause is called when production havingClause is exited.
func (s *BaseMySQLParserListener) ExitHavingClause(ctx *HavingClauseContext) {}

// EnterWindowClause is called when production windowClause is entered.
func (s *BaseMySQLParserListener) EnterWindowClause(ctx *WindowClauseContext) {}

// ExitWindowClause is called when production windowClause is exited.
func (s *BaseMySQLParserListener) ExitWindowClause(ctx *WindowClauseContext) {}

// EnterWindowDefinition is called when production windowDefinition is entered.
func (s *BaseMySQLParserListener) EnterWindowDefinition(ctx *WindowDefinitionContext) {}

// ExitWindowDefinition is called when production windowDefinition is exited.
func (s *BaseMySQLParserListener) ExitWindowDefinition(ctx *WindowDefinitionContext) {}

// EnterWindowSpec is called when production windowSpec is entered.
func (s *BaseMySQLParserListener) EnterWindowSpec(ctx *WindowSpecContext) {}

// ExitWindowSpec is called when production windowSpec is exited.
func (s *BaseMySQLParserListener) ExitWindowSpec(ctx *WindowSpecContext) {}

// EnterWindowSpecDetails is called when production windowSpecDetails is entered.
func (s *BaseMySQLParserListener) EnterWindowSpecDetails(ctx *WindowSpecDetailsContext) {}

// ExitWindowSpecDetails is called when production windowSpecDetails is exited.
func (s *BaseMySQLParserListener) ExitWindowSpecDetails(ctx *WindowSpecDetailsContext) {}

// EnterWindowFrameClause is called when production windowFrameClause is entered.
func (s *BaseMySQLParserListener) EnterWindowFrameClause(ctx *WindowFrameClauseContext) {}

// ExitWindowFrameClause is called when production windowFrameClause is exited.
func (s *BaseMySQLParserListener) ExitWindowFrameClause(ctx *WindowFrameClauseContext) {}

// EnterWindowFrameUnits is called when production windowFrameUnits is entered.
func (s *BaseMySQLParserListener) EnterWindowFrameUnits(ctx *WindowFrameUnitsContext) {}

// ExitWindowFrameUnits is called when production windowFrameUnits is exited.
func (s *BaseMySQLParserListener) ExitWindowFrameUnits(ctx *WindowFrameUnitsContext) {}

// EnterWindowFrameExtent is called when production windowFrameExtent is entered.
func (s *BaseMySQLParserListener) EnterWindowFrameExtent(ctx *WindowFrameExtentContext) {}

// ExitWindowFrameExtent is called when production windowFrameExtent is exited.
func (s *BaseMySQLParserListener) ExitWindowFrameExtent(ctx *WindowFrameExtentContext) {}

// EnterWindowFrameStart is called when production windowFrameStart is entered.
func (s *BaseMySQLParserListener) EnterWindowFrameStart(ctx *WindowFrameStartContext) {}

// ExitWindowFrameStart is called when production windowFrameStart is exited.
func (s *BaseMySQLParserListener) ExitWindowFrameStart(ctx *WindowFrameStartContext) {}

// EnterWindowFrameBetween is called when production windowFrameBetween is entered.
func (s *BaseMySQLParserListener) EnterWindowFrameBetween(ctx *WindowFrameBetweenContext) {}

// ExitWindowFrameBetween is called when production windowFrameBetween is exited.
func (s *BaseMySQLParserListener) ExitWindowFrameBetween(ctx *WindowFrameBetweenContext) {}

// EnterWindowFrameBound is called when production windowFrameBound is entered.
func (s *BaseMySQLParserListener) EnterWindowFrameBound(ctx *WindowFrameBoundContext) {}

// ExitWindowFrameBound is called when production windowFrameBound is exited.
func (s *BaseMySQLParserListener) ExitWindowFrameBound(ctx *WindowFrameBoundContext) {}

// EnterWindowFrameExclusion is called when production windowFrameExclusion is entered.
func (s *BaseMySQLParserListener) EnterWindowFrameExclusion(ctx *WindowFrameExclusionContext) {}

// ExitWindowFrameExclusion is called when production windowFrameExclusion is exited.
func (s *BaseMySQLParserListener) ExitWindowFrameExclusion(ctx *WindowFrameExclusionContext) {}

// EnterWithClause is called when production withClause is entered.
func (s *BaseMySQLParserListener) EnterWithClause(ctx *WithClauseContext) {}

// ExitWithClause is called when production withClause is exited.
func (s *BaseMySQLParserListener) ExitWithClause(ctx *WithClauseContext) {}

// EnterCommonTableExpression is called when production commonTableExpression is entered.
func (s *BaseMySQLParserListener) EnterCommonTableExpression(ctx *CommonTableExpressionContext) {}

// ExitCommonTableExpression is called when production commonTableExpression is exited.
func (s *BaseMySQLParserListener) ExitCommonTableExpression(ctx *CommonTableExpressionContext) {}

// EnterGroupByClause is called when production groupByClause is entered.
func (s *BaseMySQLParserListener) EnterGroupByClause(ctx *GroupByClauseContext) {}

// ExitGroupByClause is called when production groupByClause is exited.
func (s *BaseMySQLParserListener) ExitGroupByClause(ctx *GroupByClauseContext) {}

// EnterOlapOption is called when production olapOption is entered.
func (s *BaseMySQLParserListener) EnterOlapOption(ctx *OlapOptionContext) {}

// ExitOlapOption is called when production olapOption is exited.
func (s *BaseMySQLParserListener) ExitOlapOption(ctx *OlapOptionContext) {}

// EnterOrderClause is called when production orderClause is entered.
func (s *BaseMySQLParserListener) EnterOrderClause(ctx *OrderClauseContext) {}

// ExitOrderClause is called when production orderClause is exited.
func (s *BaseMySQLParserListener) ExitOrderClause(ctx *OrderClauseContext) {}

// EnterDirection is called when production direction is entered.
func (s *BaseMySQLParserListener) EnterDirection(ctx *DirectionContext) {}

// ExitDirection is called when production direction is exited.
func (s *BaseMySQLParserListener) ExitDirection(ctx *DirectionContext) {}

// EnterFromClause is called when production fromClause is entered.
func (s *BaseMySQLParserListener) EnterFromClause(ctx *FromClauseContext) {}

// ExitFromClause is called when production fromClause is exited.
func (s *BaseMySQLParserListener) ExitFromClause(ctx *FromClauseContext) {}

// EnterTableReferenceList is called when production tableReferenceList is entered.
func (s *BaseMySQLParserListener) EnterTableReferenceList(ctx *TableReferenceListContext) {}

// ExitTableReferenceList is called when production tableReferenceList is exited.
func (s *BaseMySQLParserListener) ExitTableReferenceList(ctx *TableReferenceListContext) {}

// EnterTableValueConstructor is called when production tableValueConstructor is entered.
func (s *BaseMySQLParserListener) EnterTableValueConstructor(ctx *TableValueConstructorContext) {}

// ExitTableValueConstructor is called when production tableValueConstructor is exited.
func (s *BaseMySQLParserListener) ExitTableValueConstructor(ctx *TableValueConstructorContext) {}

// EnterExplicitTable is called when production explicitTable is entered.
func (s *BaseMySQLParserListener) EnterExplicitTable(ctx *ExplicitTableContext) {}

// ExitExplicitTable is called when production explicitTable is exited.
func (s *BaseMySQLParserListener) ExitExplicitTable(ctx *ExplicitTableContext) {}

// EnterRowValueExplicit is called when production rowValueExplicit is entered.
func (s *BaseMySQLParserListener) EnterRowValueExplicit(ctx *RowValueExplicitContext) {}

// ExitRowValueExplicit is called when production rowValueExplicit is exited.
func (s *BaseMySQLParserListener) ExitRowValueExplicit(ctx *RowValueExplicitContext) {}

// EnterSelectOption is called when production selectOption is entered.
func (s *BaseMySQLParserListener) EnterSelectOption(ctx *SelectOptionContext) {}

// ExitSelectOption is called when production selectOption is exited.
func (s *BaseMySQLParserListener) ExitSelectOption(ctx *SelectOptionContext) {}

// EnterSelectItemList is called when production selectItemList is entered.
func (s *BaseMySQLParserListener) EnterSelectItemList(ctx *SelectItemListContext) {}

// ExitSelectItemList is called when production selectItemList is exited.
func (s *BaseMySQLParserListener) ExitSelectItemList(ctx *SelectItemListContext) {}

// EnterSelectItem is called when production selectItem is entered.
func (s *BaseMySQLParserListener) EnterSelectItem(ctx *SelectItemContext) {}

// ExitSelectItem is called when production selectItem is exited.
func (s *BaseMySQLParserListener) ExitSelectItem(ctx *SelectItemContext) {}

// EnterSelectAlias is called when production selectAlias is entered.
func (s *BaseMySQLParserListener) EnterSelectAlias(ctx *SelectAliasContext) {}

// ExitSelectAlias is called when production selectAlias is exited.
func (s *BaseMySQLParserListener) ExitSelectAlias(ctx *SelectAliasContext) {}

// EnterWhereClause is called when production whereClause is entered.
func (s *BaseMySQLParserListener) EnterWhereClause(ctx *WhereClauseContext) {}

// ExitWhereClause is called when production whereClause is exited.
func (s *BaseMySQLParserListener) ExitWhereClause(ctx *WhereClauseContext) {}

// EnterTableReference is called when production tableReference is entered.
func (s *BaseMySQLParserListener) EnterTableReference(ctx *TableReferenceContext) {}

// ExitTableReference is called when production tableReference is exited.
func (s *BaseMySQLParserListener) ExitTableReference(ctx *TableReferenceContext) {}

// EnterEscapedTableReference is called when production escapedTableReference is entered.
func (s *BaseMySQLParserListener) EnterEscapedTableReference(ctx *EscapedTableReferenceContext) {}

// ExitEscapedTableReference is called when production escapedTableReference is exited.
func (s *BaseMySQLParserListener) ExitEscapedTableReference(ctx *EscapedTableReferenceContext) {}

// EnterJoinedTable is called when production joinedTable is entered.
func (s *BaseMySQLParserListener) EnterJoinedTable(ctx *JoinedTableContext) {}

// ExitJoinedTable is called when production joinedTable is exited.
func (s *BaseMySQLParserListener) ExitJoinedTable(ctx *JoinedTableContext) {}

// EnterNaturalJoinType is called when production naturalJoinType is entered.
func (s *BaseMySQLParserListener) EnterNaturalJoinType(ctx *NaturalJoinTypeContext) {}

// ExitNaturalJoinType is called when production naturalJoinType is exited.
func (s *BaseMySQLParserListener) ExitNaturalJoinType(ctx *NaturalJoinTypeContext) {}

// EnterInnerJoinType is called when production innerJoinType is entered.
func (s *BaseMySQLParserListener) EnterInnerJoinType(ctx *InnerJoinTypeContext) {}

// ExitInnerJoinType is called when production innerJoinType is exited.
func (s *BaseMySQLParserListener) ExitInnerJoinType(ctx *InnerJoinTypeContext) {}

// EnterOuterJoinType is called when production outerJoinType is entered.
func (s *BaseMySQLParserListener) EnterOuterJoinType(ctx *OuterJoinTypeContext) {}

// ExitOuterJoinType is called when production outerJoinType is exited.
func (s *BaseMySQLParserListener) ExitOuterJoinType(ctx *OuterJoinTypeContext) {}

// EnterTableFactor is called when production tableFactor is entered.
func (s *BaseMySQLParserListener) EnterTableFactor(ctx *TableFactorContext) {}

// ExitTableFactor is called when production tableFactor is exited.
func (s *BaseMySQLParserListener) ExitTableFactor(ctx *TableFactorContext) {}

// EnterSingleTable is called when production singleTable is entered.
func (s *BaseMySQLParserListener) EnterSingleTable(ctx *SingleTableContext) {}

// ExitSingleTable is called when production singleTable is exited.
func (s *BaseMySQLParserListener) ExitSingleTable(ctx *SingleTableContext) {}

// EnterSingleTableParens is called when production singleTableParens is entered.
func (s *BaseMySQLParserListener) EnterSingleTableParens(ctx *SingleTableParensContext) {}

// ExitSingleTableParens is called when production singleTableParens is exited.
func (s *BaseMySQLParserListener) ExitSingleTableParens(ctx *SingleTableParensContext) {}

// EnterDerivedTable is called when production derivedTable is entered.
func (s *BaseMySQLParserListener) EnterDerivedTable(ctx *DerivedTableContext) {}

// ExitDerivedTable is called when production derivedTable is exited.
func (s *BaseMySQLParserListener) ExitDerivedTable(ctx *DerivedTableContext) {}

// EnterTableReferenceListParens is called when production tableReferenceListParens is entered.
func (s *BaseMySQLParserListener) EnterTableReferenceListParens(ctx *TableReferenceListParensContext) {
}

// ExitTableReferenceListParens is called when production tableReferenceListParens is exited.
func (s *BaseMySQLParserListener) ExitTableReferenceListParens(ctx *TableReferenceListParensContext) {
}

// EnterUnionOption is called when production unionOption is entered.
func (s *BaseMySQLParserListener) EnterUnionOption(ctx *UnionOptionContext) {}

// ExitUnionOption is called when production unionOption is exited.
func (s *BaseMySQLParserListener) ExitUnionOption(ctx *UnionOptionContext) {}

// EnterTableAlias is called when production tableAlias is entered.
func (s *BaseMySQLParserListener) EnterTableAlias(ctx *TableAliasContext) {}

// ExitTableAlias is called when production tableAlias is exited.
func (s *BaseMySQLParserListener) ExitTableAlias(ctx *TableAliasContext) {}

// EnterIndexHintList is called when production indexHintList is entered.
func (s *BaseMySQLParserListener) EnterIndexHintList(ctx *IndexHintListContext) {}

// ExitIndexHintList is called when production indexHintList is exited.
func (s *BaseMySQLParserListener) ExitIndexHintList(ctx *IndexHintListContext) {}

// EnterIndexHint is called when production indexHint is entered.
func (s *BaseMySQLParserListener) EnterIndexHint(ctx *IndexHintContext) {}

// ExitIndexHint is called when production indexHint is exited.
func (s *BaseMySQLParserListener) ExitIndexHint(ctx *IndexHintContext) {}

// EnterIndexHintType is called when production indexHintType is entered.
func (s *BaseMySQLParserListener) EnterIndexHintType(ctx *IndexHintTypeContext) {}

// ExitIndexHintType is called when production indexHintType is exited.
func (s *BaseMySQLParserListener) ExitIndexHintType(ctx *IndexHintTypeContext) {}

// EnterKeyOrIndex is called when production keyOrIndex is entered.
func (s *BaseMySQLParserListener) EnterKeyOrIndex(ctx *KeyOrIndexContext) {}

// ExitKeyOrIndex is called when production keyOrIndex is exited.
func (s *BaseMySQLParserListener) ExitKeyOrIndex(ctx *KeyOrIndexContext) {}

// EnterIndexHintClause is called when production indexHintClause is entered.
func (s *BaseMySQLParserListener) EnterIndexHintClause(ctx *IndexHintClauseContext) {}

// ExitIndexHintClause is called when production indexHintClause is exited.
func (s *BaseMySQLParserListener) ExitIndexHintClause(ctx *IndexHintClauseContext) {}

// EnterIndexList is called when production indexList is entered.
func (s *BaseMySQLParserListener) EnterIndexList(ctx *IndexListContext) {}

// ExitIndexList is called when production indexList is exited.
func (s *BaseMySQLParserListener) ExitIndexList(ctx *IndexListContext) {}

// EnterIndexListElement is called when production indexListElement is entered.
func (s *BaseMySQLParserListener) EnterIndexListElement(ctx *IndexListElementContext) {}

// ExitIndexListElement is called when production indexListElement is exited.
func (s *BaseMySQLParserListener) ExitIndexListElement(ctx *IndexListElementContext) {}

// EnterExprOr is called when production exprOr is entered.
func (s *BaseMySQLParserListener) EnterExprOr(ctx *ExprOrContext) {}

// ExitExprOr is called when production exprOr is exited.
func (s *BaseMySQLParserListener) ExitExprOr(ctx *ExprOrContext) {}

// EnterExprNot is called when production exprNot is entered.
func (s *BaseMySQLParserListener) EnterExprNot(ctx *ExprNotContext) {}

// ExitExprNot is called when production exprNot is exited.
func (s *BaseMySQLParserListener) ExitExprNot(ctx *ExprNotContext) {}

// EnterExprIs is called when production exprIs is entered.
func (s *BaseMySQLParserListener) EnterExprIs(ctx *ExprIsContext) {}

// ExitExprIs is called when production exprIs is exited.
func (s *BaseMySQLParserListener) ExitExprIs(ctx *ExprIsContext) {}

// EnterExprAnd is called when production exprAnd is entered.
func (s *BaseMySQLParserListener) EnterExprAnd(ctx *ExprAndContext) {}

// ExitExprAnd is called when production exprAnd is exited.
func (s *BaseMySQLParserListener) ExitExprAnd(ctx *ExprAndContext) {}

// EnterExprXor is called when production exprXor is entered.
func (s *BaseMySQLParserListener) EnterExprXor(ctx *ExprXorContext) {}

// ExitExprXor is called when production exprXor is exited.
func (s *BaseMySQLParserListener) ExitExprXor(ctx *ExprXorContext) {}

// EnterPrimaryExprPredicate is called when production primaryExprPredicate is entered.
func (s *BaseMySQLParserListener) EnterPrimaryExprPredicate(ctx *PrimaryExprPredicateContext) {}

// ExitPrimaryExprPredicate is called when production primaryExprPredicate is exited.
func (s *BaseMySQLParserListener) ExitPrimaryExprPredicate(ctx *PrimaryExprPredicateContext) {}

// EnterPrimaryExprCompare is called when production primaryExprCompare is entered.
func (s *BaseMySQLParserListener) EnterPrimaryExprCompare(ctx *PrimaryExprCompareContext) {}

// ExitPrimaryExprCompare is called when production primaryExprCompare is exited.
func (s *BaseMySQLParserListener) ExitPrimaryExprCompare(ctx *PrimaryExprCompareContext) {}

// EnterPrimaryExprAllAny is called when production primaryExprAllAny is entered.
func (s *BaseMySQLParserListener) EnterPrimaryExprAllAny(ctx *PrimaryExprAllAnyContext) {}

// ExitPrimaryExprAllAny is called when production primaryExprAllAny is exited.
func (s *BaseMySQLParserListener) ExitPrimaryExprAllAny(ctx *PrimaryExprAllAnyContext) {}

// EnterPrimaryExprIsNull is called when production primaryExprIsNull is entered.
func (s *BaseMySQLParserListener) EnterPrimaryExprIsNull(ctx *PrimaryExprIsNullContext) {}

// ExitPrimaryExprIsNull is called when production primaryExprIsNull is exited.
func (s *BaseMySQLParserListener) ExitPrimaryExprIsNull(ctx *PrimaryExprIsNullContext) {}

// EnterCompOp is called when production compOp is entered.
func (s *BaseMySQLParserListener) EnterCompOp(ctx *CompOpContext) {}

// ExitCompOp is called when production compOp is exited.
func (s *BaseMySQLParserListener) ExitCompOp(ctx *CompOpContext) {}

// EnterPredicate is called when production predicate is entered.
func (s *BaseMySQLParserListener) EnterPredicate(ctx *PredicateContext) {}

// ExitPredicate is called when production predicate is exited.
func (s *BaseMySQLParserListener) ExitPredicate(ctx *PredicateContext) {}

// EnterPredicateExprIn is called when production predicateExprIn is entered.
func (s *BaseMySQLParserListener) EnterPredicateExprIn(ctx *PredicateExprInContext) {}

// ExitPredicateExprIn is called when production predicateExprIn is exited.
func (s *BaseMySQLParserListener) ExitPredicateExprIn(ctx *PredicateExprInContext) {}

// EnterPredicateExprBetween is called when production predicateExprBetween is entered.
func (s *BaseMySQLParserListener) EnterPredicateExprBetween(ctx *PredicateExprBetweenContext) {}

// ExitPredicateExprBetween is called when production predicateExprBetween is exited.
func (s *BaseMySQLParserListener) ExitPredicateExprBetween(ctx *PredicateExprBetweenContext) {}

// EnterPredicateExprLike is called when production predicateExprLike is entered.
func (s *BaseMySQLParserListener) EnterPredicateExprLike(ctx *PredicateExprLikeContext) {}

// ExitPredicateExprLike is called when production predicateExprLike is exited.
func (s *BaseMySQLParserListener) ExitPredicateExprLike(ctx *PredicateExprLikeContext) {}

// EnterPredicateExprRegex is called when production predicateExprRegex is entered.
func (s *BaseMySQLParserListener) EnterPredicateExprRegex(ctx *PredicateExprRegexContext) {}

// ExitPredicateExprRegex is called when production predicateExprRegex is exited.
func (s *BaseMySQLParserListener) ExitPredicateExprRegex(ctx *PredicateExprRegexContext) {}

// EnterBitExpr is called when production bitExpr is entered.
func (s *BaseMySQLParserListener) EnterBitExpr(ctx *BitExprContext) {}

// ExitBitExpr is called when production bitExpr is exited.
func (s *BaseMySQLParserListener) ExitBitExpr(ctx *BitExprContext) {}

// EnterSimpleExprConvert is called when production simpleExprConvert is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprConvert(ctx *SimpleExprConvertContext) {}

// ExitSimpleExprConvert is called when production simpleExprConvert is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprConvert(ctx *SimpleExprConvertContext) {}

// EnterSimpleExprVariable is called when production simpleExprVariable is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprVariable(ctx *SimpleExprVariableContext) {}

// ExitSimpleExprVariable is called when production simpleExprVariable is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprVariable(ctx *SimpleExprVariableContext) {}

// EnterSimpleExprCast is called when production simpleExprCast is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprCast(ctx *SimpleExprCastContext) {}

// ExitSimpleExprCast is called when production simpleExprCast is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprCast(ctx *SimpleExprCastContext) {}

// EnterSimpleExprUnary is called when production simpleExprUnary is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprUnary(ctx *SimpleExprUnaryContext) {}

// ExitSimpleExprUnary is called when production simpleExprUnary is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprUnary(ctx *SimpleExprUnaryContext) {}

// EnterSimpleExprOdbc is called when production simpleExprOdbc is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprOdbc(ctx *SimpleExprOdbcContext) {}

// ExitSimpleExprOdbc is called when production simpleExprOdbc is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprOdbc(ctx *SimpleExprOdbcContext) {}

// EnterSimpleExprRuntimeFunction is called when production simpleExprRuntimeFunction is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprRuntimeFunction(ctx *SimpleExprRuntimeFunctionContext) {
}

// ExitSimpleExprRuntimeFunction is called when production simpleExprRuntimeFunction is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprRuntimeFunction(ctx *SimpleExprRuntimeFunctionContext) {
}

// EnterSimpleExprFunction is called when production simpleExprFunction is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprFunction(ctx *SimpleExprFunctionContext) {}

// ExitSimpleExprFunction is called when production simpleExprFunction is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprFunction(ctx *SimpleExprFunctionContext) {}

// EnterSimpleExprCollate is called when production simpleExprCollate is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprCollate(ctx *SimpleExprCollateContext) {}

// ExitSimpleExprCollate is called when production simpleExprCollate is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprCollate(ctx *SimpleExprCollateContext) {}

// EnterSimpleExprMatch is called when production simpleExprMatch is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprMatch(ctx *SimpleExprMatchContext) {}

// ExitSimpleExprMatch is called when production simpleExprMatch is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprMatch(ctx *SimpleExprMatchContext) {}

// EnterSimpleExprWindowingFunction is called when production simpleExprWindowingFunction is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprWindowingFunction(ctx *SimpleExprWindowingFunctionContext) {
}

// ExitSimpleExprWindowingFunction is called when production simpleExprWindowingFunction is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprWindowingFunction(ctx *SimpleExprWindowingFunctionContext) {
}

// EnterSimpleExprBinary is called when production simpleExprBinary is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprBinary(ctx *SimpleExprBinaryContext) {}

// ExitSimpleExprBinary is called when production simpleExprBinary is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprBinary(ctx *SimpleExprBinaryContext) {}

// EnterSimpleExprColumnRef is called when production simpleExprColumnRef is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprColumnRef(ctx *SimpleExprColumnRefContext) {}

// ExitSimpleExprColumnRef is called when production simpleExprColumnRef is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprColumnRef(ctx *SimpleExprColumnRefContext) {}

// EnterSimpleExprParamMarker is called when production simpleExprParamMarker is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprParamMarker(ctx *SimpleExprParamMarkerContext) {}

// ExitSimpleExprParamMarker is called when production simpleExprParamMarker is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprParamMarker(ctx *SimpleExprParamMarkerContext) {}

// EnterSimpleExprSum is called when production simpleExprSum is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprSum(ctx *SimpleExprSumContext) {}

// ExitSimpleExprSum is called when production simpleExprSum is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprSum(ctx *SimpleExprSumContext) {}

// EnterSimpleExprConvertUsing is called when production simpleExprConvertUsing is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprConvertUsing(ctx *SimpleExprConvertUsingContext) {}

// ExitSimpleExprConvertUsing is called when production simpleExprConvertUsing is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprConvertUsing(ctx *SimpleExprConvertUsingContext) {}

// EnterSimpleExprSubQuery is called when production simpleExprSubQuery is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprSubQuery(ctx *SimpleExprSubQueryContext) {}

// ExitSimpleExprSubQuery is called when production simpleExprSubQuery is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprSubQuery(ctx *SimpleExprSubQueryContext) {}

// EnterSimpleExprGroupingOperation is called when production simpleExprGroupingOperation is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprGroupingOperation(ctx *SimpleExprGroupingOperationContext) {
}

// ExitSimpleExprGroupingOperation is called when production simpleExprGroupingOperation is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprGroupingOperation(ctx *SimpleExprGroupingOperationContext) {
}

// EnterSimpleExprNot is called when production simpleExprNot is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprNot(ctx *SimpleExprNotContext) {}

// ExitSimpleExprNot is called when production simpleExprNot is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprNot(ctx *SimpleExprNotContext) {}

// EnterSimpleExprValues is called when production simpleExprValues is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprValues(ctx *SimpleExprValuesContext) {}

// ExitSimpleExprValues is called when production simpleExprValues is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprValues(ctx *SimpleExprValuesContext) {}

// EnterSimpleExprDefault is called when production simpleExprDefault is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprDefault(ctx *SimpleExprDefaultContext) {}

// ExitSimpleExprDefault is called when production simpleExprDefault is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprDefault(ctx *SimpleExprDefaultContext) {}

// EnterSimpleExprList is called when production simpleExprList is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprList(ctx *SimpleExprListContext) {}

// ExitSimpleExprList is called when production simpleExprList is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprList(ctx *SimpleExprListContext) {}

// EnterSimpleExprInterval is called when production simpleExprInterval is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprInterval(ctx *SimpleExprIntervalContext) {}

// ExitSimpleExprInterval is called when production simpleExprInterval is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprInterval(ctx *SimpleExprIntervalContext) {}

// EnterSimpleExprCase is called when production simpleExprCase is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprCase(ctx *SimpleExprCaseContext) {}

// ExitSimpleExprCase is called when production simpleExprCase is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprCase(ctx *SimpleExprCaseContext) {}

// EnterSimpleExprConcat is called when production simpleExprConcat is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprConcat(ctx *SimpleExprConcatContext) {}

// ExitSimpleExprConcat is called when production simpleExprConcat is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprConcat(ctx *SimpleExprConcatContext) {}

// EnterSimpleExprLiteral is called when production simpleExprLiteral is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprLiteral(ctx *SimpleExprLiteralContext) {}

// ExitSimpleExprLiteral is called when production simpleExprLiteral is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprLiteral(ctx *SimpleExprLiteralContext) {}

// EnterArrayCast is called when production arrayCast is entered.
func (s *BaseMySQLParserListener) EnterArrayCast(ctx *ArrayCastContext) {}

// ExitArrayCast is called when production arrayCast is exited.
func (s *BaseMySQLParserListener) ExitArrayCast(ctx *ArrayCastContext) {}

// EnterJsonOperator is called when production jsonOperator is entered.
func (s *BaseMySQLParserListener) EnterJsonOperator(ctx *JsonOperatorContext) {}

// ExitJsonOperator is called when production jsonOperator is exited.
func (s *BaseMySQLParserListener) ExitJsonOperator(ctx *JsonOperatorContext) {}

// EnterSumExpr is called when production sumExpr is entered.
func (s *BaseMySQLParserListener) EnterSumExpr(ctx *SumExprContext) {}

// ExitSumExpr is called when production sumExpr is exited.
func (s *BaseMySQLParserListener) ExitSumExpr(ctx *SumExprContext) {}

// EnterGroupingOperation is called when production groupingOperation is entered.
func (s *BaseMySQLParserListener) EnterGroupingOperation(ctx *GroupingOperationContext) {}

// ExitGroupingOperation is called when production groupingOperation is exited.
func (s *BaseMySQLParserListener) ExitGroupingOperation(ctx *GroupingOperationContext) {}

// EnterWindowFunctionCall is called when production windowFunctionCall is entered.
func (s *BaseMySQLParserListener) EnterWindowFunctionCall(ctx *WindowFunctionCallContext) {}

// ExitWindowFunctionCall is called when production windowFunctionCall is exited.
func (s *BaseMySQLParserListener) ExitWindowFunctionCall(ctx *WindowFunctionCallContext) {}

// EnterWindowingClause is called when production windowingClause is entered.
func (s *BaseMySQLParserListener) EnterWindowingClause(ctx *WindowingClauseContext) {}

// ExitWindowingClause is called when production windowingClause is exited.
func (s *BaseMySQLParserListener) ExitWindowingClause(ctx *WindowingClauseContext) {}

// EnterLeadLagInfo is called when production leadLagInfo is entered.
func (s *BaseMySQLParserListener) EnterLeadLagInfo(ctx *LeadLagInfoContext) {}

// ExitLeadLagInfo is called when production leadLagInfo is exited.
func (s *BaseMySQLParserListener) ExitLeadLagInfo(ctx *LeadLagInfoContext) {}

// EnterNullTreatment is called when production nullTreatment is entered.
func (s *BaseMySQLParserListener) EnterNullTreatment(ctx *NullTreatmentContext) {}

// ExitNullTreatment is called when production nullTreatment is exited.
func (s *BaseMySQLParserListener) ExitNullTreatment(ctx *NullTreatmentContext) {}

// EnterJsonFunction is called when production jsonFunction is entered.
func (s *BaseMySQLParserListener) EnterJsonFunction(ctx *JsonFunctionContext) {}

// ExitJsonFunction is called when production jsonFunction is exited.
func (s *BaseMySQLParserListener) ExitJsonFunction(ctx *JsonFunctionContext) {}

// EnterInSumExpr is called when production inSumExpr is entered.
func (s *BaseMySQLParserListener) EnterInSumExpr(ctx *InSumExprContext) {}

// ExitInSumExpr is called when production inSumExpr is exited.
func (s *BaseMySQLParserListener) ExitInSumExpr(ctx *InSumExprContext) {}

// EnterIdentListArg is called when production identListArg is entered.
func (s *BaseMySQLParserListener) EnterIdentListArg(ctx *IdentListArgContext) {}

// ExitIdentListArg is called when production identListArg is exited.
func (s *BaseMySQLParserListener) ExitIdentListArg(ctx *IdentListArgContext) {}

// EnterIdentList is called when production identList is entered.
func (s *BaseMySQLParserListener) EnterIdentList(ctx *IdentListContext) {}

// ExitIdentList is called when production identList is exited.
func (s *BaseMySQLParserListener) ExitIdentList(ctx *IdentListContext) {}

// EnterFulltextOptions is called when production fulltextOptions is entered.
func (s *BaseMySQLParserListener) EnterFulltextOptions(ctx *FulltextOptionsContext) {}

// ExitFulltextOptions is called when production fulltextOptions is exited.
func (s *BaseMySQLParserListener) ExitFulltextOptions(ctx *FulltextOptionsContext) {}

// EnterRuntimeFunctionCall is called when production runtimeFunctionCall is entered.
func (s *BaseMySQLParserListener) EnterRuntimeFunctionCall(ctx *RuntimeFunctionCallContext) {}

// ExitRuntimeFunctionCall is called when production runtimeFunctionCall is exited.
func (s *BaseMySQLParserListener) ExitRuntimeFunctionCall(ctx *RuntimeFunctionCallContext) {}

// EnterTimeFunctionParameters is called when production timeFunctionParameters is entered.
func (s *BaseMySQLParserListener) EnterTimeFunctionParameters(ctx *TimeFunctionParametersContext) {}

// ExitTimeFunctionParameters is called when production timeFunctionParameters is exited.
func (s *BaseMySQLParserListener) ExitTimeFunctionParameters(ctx *TimeFunctionParametersContext) {}

// EnterFractionalPrecision is called when production fractionalPrecision is entered.
func (s *BaseMySQLParserListener) EnterFractionalPrecision(ctx *FractionalPrecisionContext) {}

// ExitFractionalPrecision is called when production fractionalPrecision is exited.
func (s *BaseMySQLParserListener) ExitFractionalPrecision(ctx *FractionalPrecisionContext) {}

// EnterWeightStringLevels is called when production weightStringLevels is entered.
func (s *BaseMySQLParserListener) EnterWeightStringLevels(ctx *WeightStringLevelsContext) {}

// ExitWeightStringLevels is called when production weightStringLevels is exited.
func (s *BaseMySQLParserListener) ExitWeightStringLevels(ctx *WeightStringLevelsContext) {}

// EnterWeightStringLevelListItem is called when production weightStringLevelListItem is entered.
func (s *BaseMySQLParserListener) EnterWeightStringLevelListItem(ctx *WeightStringLevelListItemContext) {
}

// ExitWeightStringLevelListItem is called when production weightStringLevelListItem is exited.
func (s *BaseMySQLParserListener) ExitWeightStringLevelListItem(ctx *WeightStringLevelListItemContext) {
}

// EnterDateTimeTtype is called when production dateTimeTtype is entered.
func (s *BaseMySQLParserListener) EnterDateTimeTtype(ctx *DateTimeTtypeContext) {}

// ExitDateTimeTtype is called when production dateTimeTtype is exited.
func (s *BaseMySQLParserListener) ExitDateTimeTtype(ctx *DateTimeTtypeContext) {}

// EnterTrimFunction is called when production trimFunction is entered.
func (s *BaseMySQLParserListener) EnterTrimFunction(ctx *TrimFunctionContext) {}

// ExitTrimFunction is called when production trimFunction is exited.
func (s *BaseMySQLParserListener) ExitTrimFunction(ctx *TrimFunctionContext) {}

// EnterSubstringFunction is called when production substringFunction is entered.
func (s *BaseMySQLParserListener) EnterSubstringFunction(ctx *SubstringFunctionContext) {}

// ExitSubstringFunction is called when production substringFunction is exited.
func (s *BaseMySQLParserListener) ExitSubstringFunction(ctx *SubstringFunctionContext) {}

// EnterFunctionCall is called when production functionCall is entered.
func (s *BaseMySQLParserListener) EnterFunctionCall(ctx *FunctionCallContext) {}

// ExitFunctionCall is called when production functionCall is exited.
func (s *BaseMySQLParserListener) ExitFunctionCall(ctx *FunctionCallContext) {}

// EnterUdfExprList is called when production udfExprList is entered.
func (s *BaseMySQLParserListener) EnterUdfExprList(ctx *UdfExprListContext) {}

// ExitUdfExprList is called when production udfExprList is exited.
func (s *BaseMySQLParserListener) ExitUdfExprList(ctx *UdfExprListContext) {}

// EnterUdfExpr is called when production udfExpr is entered.
func (s *BaseMySQLParserListener) EnterUdfExpr(ctx *UdfExprContext) {}

// ExitUdfExpr is called when production udfExpr is exited.
func (s *BaseMySQLParserListener) ExitUdfExpr(ctx *UdfExprContext) {}

// EnterVariable is called when production variable is entered.
func (s *BaseMySQLParserListener) EnterVariable(ctx *VariableContext) {}

// ExitVariable is called when production variable is exited.
func (s *BaseMySQLParserListener) ExitVariable(ctx *VariableContext) {}

// EnterUserVariable is called when production userVariable is entered.
func (s *BaseMySQLParserListener) EnterUserVariable(ctx *UserVariableContext) {}

// ExitUserVariable is called when production userVariable is exited.
func (s *BaseMySQLParserListener) ExitUserVariable(ctx *UserVariableContext) {}

// EnterSystemVariable is called when production systemVariable is entered.
func (s *BaseMySQLParserListener) EnterSystemVariable(ctx *SystemVariableContext) {}

// ExitSystemVariable is called when production systemVariable is exited.
func (s *BaseMySQLParserListener) ExitSystemVariable(ctx *SystemVariableContext) {}

// EnterWhenExpression is called when production whenExpression is entered.
func (s *BaseMySQLParserListener) EnterWhenExpression(ctx *WhenExpressionContext) {}

// ExitWhenExpression is called when production whenExpression is exited.
func (s *BaseMySQLParserListener) ExitWhenExpression(ctx *WhenExpressionContext) {}

// EnterThenExpression is called when production thenExpression is entered.
func (s *BaseMySQLParserListener) EnterThenExpression(ctx *ThenExpressionContext) {}

// ExitThenExpression is called when production thenExpression is exited.
func (s *BaseMySQLParserListener) ExitThenExpression(ctx *ThenExpressionContext) {}

// EnterElseExpression is called when production elseExpression is entered.
func (s *BaseMySQLParserListener) EnterElseExpression(ctx *ElseExpressionContext) {}

// ExitElseExpression is called when production elseExpression is exited.
func (s *BaseMySQLParserListener) ExitElseExpression(ctx *ElseExpressionContext) {}

// EnterCastType is called when production castType is entered.
func (s *BaseMySQLParserListener) EnterCastType(ctx *CastTypeContext) {}

// ExitCastType is called when production castType is exited.
func (s *BaseMySQLParserListener) ExitCastType(ctx *CastTypeContext) {}

// EnterExprList is called when production exprList is entered.
func (s *BaseMySQLParserListener) EnterExprList(ctx *ExprListContext) {}

// ExitExprList is called when production exprList is exited.
func (s *BaseMySQLParserListener) ExitExprList(ctx *ExprListContext) {}

// EnterCharset is called when production charset is entered.
func (s *BaseMySQLParserListener) EnterCharset(ctx *CharsetContext) {}

// ExitCharset is called when production charset is exited.
func (s *BaseMySQLParserListener) ExitCharset(ctx *CharsetContext) {}

// EnterNotRule is called when production notRule is entered.
func (s *BaseMySQLParserListener) EnterNotRule(ctx *NotRuleContext) {}

// ExitNotRule is called when production notRule is exited.
func (s *BaseMySQLParserListener) ExitNotRule(ctx *NotRuleContext) {}

// EnterNot2Rule is called when production not2Rule is entered.
func (s *BaseMySQLParserListener) EnterNot2Rule(ctx *Not2RuleContext) {}

// ExitNot2Rule is called when production not2Rule is exited.
func (s *BaseMySQLParserListener) ExitNot2Rule(ctx *Not2RuleContext) {}

// EnterInterval is called when production interval is entered.
func (s *BaseMySQLParserListener) EnterInterval(ctx *IntervalContext) {}

// ExitInterval is called when production interval is exited.
func (s *BaseMySQLParserListener) ExitInterval(ctx *IntervalContext) {}

// EnterIntervalTimeStamp is called when production intervalTimeStamp is entered.
func (s *BaseMySQLParserListener) EnterIntervalTimeStamp(ctx *IntervalTimeStampContext) {}

// ExitIntervalTimeStamp is called when production intervalTimeStamp is exited.
func (s *BaseMySQLParserListener) ExitIntervalTimeStamp(ctx *IntervalTimeStampContext) {}

// EnterExprListWithParentheses is called when production exprListWithParentheses is entered.
func (s *BaseMySQLParserListener) EnterExprListWithParentheses(ctx *ExprListWithParenthesesContext) {}

// ExitExprListWithParentheses is called when production exprListWithParentheses is exited.
func (s *BaseMySQLParserListener) ExitExprListWithParentheses(ctx *ExprListWithParenthesesContext) {}

// EnterExprWithParentheses is called when production exprWithParentheses is entered.
func (s *BaseMySQLParserListener) EnterExprWithParentheses(ctx *ExprWithParenthesesContext) {}

// ExitExprWithParentheses is called when production exprWithParentheses is exited.
func (s *BaseMySQLParserListener) ExitExprWithParentheses(ctx *ExprWithParenthesesContext) {}

// EnterSimpleExprWithParentheses is called when production simpleExprWithParentheses is entered.
func (s *BaseMySQLParserListener) EnterSimpleExprWithParentheses(ctx *SimpleExprWithParenthesesContext) {
}

// ExitSimpleExprWithParentheses is called when production simpleExprWithParentheses is exited.
func (s *BaseMySQLParserListener) ExitSimpleExprWithParentheses(ctx *SimpleExprWithParenthesesContext) {
}

// EnterOrderList is called when production orderList is entered.
func (s *BaseMySQLParserListener) EnterOrderList(ctx *OrderListContext) {}

// ExitOrderList is called when production orderList is exited.
func (s *BaseMySQLParserListener) ExitOrderList(ctx *OrderListContext) {}

// EnterOrderExpression is called when production orderExpression is entered.
func (s *BaseMySQLParserListener) EnterOrderExpression(ctx *OrderExpressionContext) {}

// ExitOrderExpression is called when production orderExpression is exited.
func (s *BaseMySQLParserListener) ExitOrderExpression(ctx *OrderExpressionContext) {}

// EnterNchar is called when production nchar is entered.
func (s *BaseMySQLParserListener) EnterNchar(ctx *NcharContext) {}

// ExitNchar is called when production nchar is exited.
func (s *BaseMySQLParserListener) ExitNchar(ctx *NcharContext) {}

// EnterRealType is called when production realType is entered.
func (s *BaseMySQLParserListener) EnterRealType(ctx *RealTypeContext) {}

// ExitRealType is called when production realType is exited.
func (s *BaseMySQLParserListener) ExitRealType(ctx *RealTypeContext) {}

// EnterFieldLength is called when production fieldLength is entered.
func (s *BaseMySQLParserListener) EnterFieldLength(ctx *FieldLengthContext) {}

// ExitFieldLength is called when production fieldLength is exited.
func (s *BaseMySQLParserListener) ExitFieldLength(ctx *FieldLengthContext) {}

// EnterCharsetWithOptBinary is called when production charsetWithOptBinary is entered.
func (s *BaseMySQLParserListener) EnterCharsetWithOptBinary(ctx *CharsetWithOptBinaryContext) {}

// ExitCharsetWithOptBinary is called when production charsetWithOptBinary is exited.
func (s *BaseMySQLParserListener) ExitCharsetWithOptBinary(ctx *CharsetWithOptBinaryContext) {}

// EnterAscii is called when production ascii is entered.
func (s *BaseMySQLParserListener) EnterAscii(ctx *AsciiContext) {}

// ExitAscii is called when production ascii is exited.
func (s *BaseMySQLParserListener) ExitAscii(ctx *AsciiContext) {}

// EnterUnicode is called when production unicode is entered.
func (s *BaseMySQLParserListener) EnterUnicode(ctx *UnicodeContext) {}

// ExitUnicode is called when production unicode is exited.
func (s *BaseMySQLParserListener) ExitUnicode(ctx *UnicodeContext) {}

// EnterWsNumCodepoints is called when production wsNumCodepoints is entered.
func (s *BaseMySQLParserListener) EnterWsNumCodepoints(ctx *WsNumCodepointsContext) {}

// ExitWsNumCodepoints is called when production wsNumCodepoints is exited.
func (s *BaseMySQLParserListener) ExitWsNumCodepoints(ctx *WsNumCodepointsContext) {}

// EnterTypeDatetimePrecision is called when production typeDatetimePrecision is entered.
func (s *BaseMySQLParserListener) EnterTypeDatetimePrecision(ctx *TypeDatetimePrecisionContext) {}

// ExitTypeDatetimePrecision is called when production typeDatetimePrecision is exited.
func (s *BaseMySQLParserListener) ExitTypeDatetimePrecision(ctx *TypeDatetimePrecisionContext) {}

// EnterCharsetName is called when production charsetName is entered.
func (s *BaseMySQLParserListener) EnterCharsetName(ctx *CharsetNameContext) {}

// ExitCharsetName is called when production charsetName is exited.
func (s *BaseMySQLParserListener) ExitCharsetName(ctx *CharsetNameContext) {}

// EnterUsePartition is called when production usePartition is entered.
func (s *BaseMySQLParserListener) EnterUsePartition(ctx *UsePartitionContext) {}

// ExitUsePartition is called when production usePartition is exited.
func (s *BaseMySQLParserListener) ExitUsePartition(ctx *UsePartitionContext) {}

// EnterFieldIdentifier is called when production fieldIdentifier is entered.
func (s *BaseMySQLParserListener) EnterFieldIdentifier(ctx *FieldIdentifierContext) {}

// ExitFieldIdentifier is called when production fieldIdentifier is exited.
func (s *BaseMySQLParserListener) ExitFieldIdentifier(ctx *FieldIdentifierContext) {}

// EnterColumnInternalRef is called when production columnInternalRef is entered.
func (s *BaseMySQLParserListener) EnterColumnInternalRef(ctx *ColumnInternalRefContext) {}

// ExitColumnInternalRef is called when production columnInternalRef is exited.
func (s *BaseMySQLParserListener) ExitColumnInternalRef(ctx *ColumnInternalRefContext) {}

// EnterColumnInternalRefList is called when production columnInternalRefList is entered.
func (s *BaseMySQLParserListener) EnterColumnInternalRefList(ctx *ColumnInternalRefListContext) {}

// ExitColumnInternalRefList is called when production columnInternalRefList is exited.
func (s *BaseMySQLParserListener) ExitColumnInternalRefList(ctx *ColumnInternalRefListContext) {}

// EnterColumnRef is called when production columnRef is entered.
func (s *BaseMySQLParserListener) EnterColumnRef(ctx *ColumnRefContext) {}

// ExitColumnRef is called when production columnRef is exited.
func (s *BaseMySQLParserListener) ExitColumnRef(ctx *ColumnRefContext) {}

// EnterTableWild is called when production tableWild is entered.
func (s *BaseMySQLParserListener) EnterTableWild(ctx *TableWildContext) {}

// ExitTableWild is called when production tableWild is exited.
func (s *BaseMySQLParserListener) ExitTableWild(ctx *TableWildContext) {}

// EnterTableRef is called when production tableRef is entered.
func (s *BaseMySQLParserListener) EnterTableRef(ctx *TableRefContext) {}

// ExitTableRef is called when production tableRef is exited.
func (s *BaseMySQLParserListener) ExitTableRef(ctx *TableRefContext) {}

// EnterLabelIdentifier is called when production labelIdentifier is entered.
func (s *BaseMySQLParserListener) EnterLabelIdentifier(ctx *LabelIdentifierContext) {}

// ExitLabelIdentifier is called when production labelIdentifier is exited.
func (s *BaseMySQLParserListener) ExitLabelIdentifier(ctx *LabelIdentifierContext) {}

// EnterLabelRef is called when production labelRef is entered.
func (s *BaseMySQLParserListener) EnterLabelRef(ctx *LabelRefContext) {}

// ExitLabelRef is called when production labelRef is exited.
func (s *BaseMySQLParserListener) ExitLabelRef(ctx *LabelRefContext) {}

// EnterWindowName is called when production windowName is entered.
func (s *BaseMySQLParserListener) EnterWindowName(ctx *WindowNameContext) {}

// ExitWindowName is called when production windowName is exited.
func (s *BaseMySQLParserListener) ExitWindowName(ctx *WindowNameContext) {}

// EnterPureIdentifier is called when production pureIdentifier is entered.
func (s *BaseMySQLParserListener) EnterPureIdentifier(ctx *PureIdentifierContext) {}

// ExitPureIdentifier is called when production pureIdentifier is exited.
func (s *BaseMySQLParserListener) ExitPureIdentifier(ctx *PureIdentifierContext) {}

// EnterIdentifier is called when production identifier is entered.
func (s *BaseMySQLParserListener) EnterIdentifier(ctx *IdentifierContext) {}

// ExitIdentifier is called when production identifier is exited.
func (s *BaseMySQLParserListener) ExitIdentifier(ctx *IdentifierContext) {}

// EnterIdentifierList is called when production identifierList is entered.
func (s *BaseMySQLParserListener) EnterIdentifierList(ctx *IdentifierListContext) {}

// ExitIdentifierList is called when production identifierList is exited.
func (s *BaseMySQLParserListener) ExitIdentifierList(ctx *IdentifierListContext) {}

// EnterIdentifierListWithParentheses is called when production identifierListWithParentheses is entered.
func (s *BaseMySQLParserListener) EnterIdentifierListWithParentheses(ctx *IdentifierListWithParenthesesContext) {
}

// ExitIdentifierListWithParentheses is called when production identifierListWithParentheses is exited.
func (s *BaseMySQLParserListener) ExitIdentifierListWithParentheses(ctx *IdentifierListWithParenthesesContext) {
}

// EnterQualifiedIdentifier is called when production qualifiedIdentifier is entered.
func (s *BaseMySQLParserListener) EnterQualifiedIdentifier(ctx *QualifiedIdentifierContext) {}

// ExitQualifiedIdentifier is called when production qualifiedIdentifier is exited.
func (s *BaseMySQLParserListener) ExitQualifiedIdentifier(ctx *QualifiedIdentifierContext) {}

// EnterSimpleIdentifier is called when production simpleIdentifier is entered.
func (s *BaseMySQLParserListener) EnterSimpleIdentifier(ctx *SimpleIdentifierContext) {}

// ExitSimpleIdentifier is called when production simpleIdentifier is exited.
func (s *BaseMySQLParserListener) ExitSimpleIdentifier(ctx *SimpleIdentifierContext) {}

// EnterDotIdentifier is called when production dotIdentifier is entered.
func (s *BaseMySQLParserListener) EnterDotIdentifier(ctx *DotIdentifierContext) {}

// ExitDotIdentifier is called when production dotIdentifier is exited.
func (s *BaseMySQLParserListener) ExitDotIdentifier(ctx *DotIdentifierContext) {}

// EnterUlong_number is called when production ulong_number is entered.
func (s *BaseMySQLParserListener) EnterUlong_number(ctx *Ulong_numberContext) {}

// ExitUlong_number is called when production ulong_number is exited.
func (s *BaseMySQLParserListener) ExitUlong_number(ctx *Ulong_numberContext) {}

// EnterReal_ulong_number is called when production real_ulong_number is entered.
func (s *BaseMySQLParserListener) EnterReal_ulong_number(ctx *Real_ulong_numberContext) {}

// ExitReal_ulong_number is called when production real_ulong_number is exited.
func (s *BaseMySQLParserListener) ExitReal_ulong_number(ctx *Real_ulong_numberContext) {}

// EnterUlonglong_number is called when production ulonglong_number is entered.
func (s *BaseMySQLParserListener) EnterUlonglong_number(ctx *Ulonglong_numberContext) {}

// ExitUlonglong_number is called when production ulonglong_number is exited.
func (s *BaseMySQLParserListener) ExitUlonglong_number(ctx *Ulonglong_numberContext) {}

// EnterReal_ulonglong_number is called when production real_ulonglong_number is entered.
func (s *BaseMySQLParserListener) EnterReal_ulonglong_number(ctx *Real_ulonglong_numberContext) {}

// ExitReal_ulonglong_number is called when production real_ulonglong_number is exited.
func (s *BaseMySQLParserListener) ExitReal_ulonglong_number(ctx *Real_ulonglong_numberContext) {}

// EnterLiteral is called when production literal is entered.
func (s *BaseMySQLParserListener) EnterLiteral(ctx *LiteralContext) {}

// ExitLiteral is called when production literal is exited.
func (s *BaseMySQLParserListener) ExitLiteral(ctx *LiteralContext) {}

// EnterTextStringLiteral is called when production textStringLiteral is entered.
func (s *BaseMySQLParserListener) EnterTextStringLiteral(ctx *TextStringLiteralContext) {}

// ExitTextStringLiteral is called when production textStringLiteral is exited.
func (s *BaseMySQLParserListener) ExitTextStringLiteral(ctx *TextStringLiteralContext) {}

// EnterTextString is called when production textString is entered.
func (s *BaseMySQLParserListener) EnterTextString(ctx *TextStringContext) {}

// ExitTextString is called when production textString is exited.
func (s *BaseMySQLParserListener) ExitTextString(ctx *TextStringContext) {}

// EnterTextLiteral is called when production textLiteral is entered.
func (s *BaseMySQLParserListener) EnterTextLiteral(ctx *TextLiteralContext) {}

// ExitTextLiteral is called when production textLiteral is exited.
func (s *BaseMySQLParserListener) ExitTextLiteral(ctx *TextLiteralContext) {}

// EnterNumLiteral is called when production numLiteral is entered.
func (s *BaseMySQLParserListener) EnterNumLiteral(ctx *NumLiteralContext) {}

// ExitNumLiteral is called when production numLiteral is exited.
func (s *BaseMySQLParserListener) ExitNumLiteral(ctx *NumLiteralContext) {}

// EnterBoolLiteral is called when production boolLiteral is entered.
func (s *BaseMySQLParserListener) EnterBoolLiteral(ctx *BoolLiteralContext) {}

// ExitBoolLiteral is called when production boolLiteral is exited.
func (s *BaseMySQLParserListener) ExitBoolLiteral(ctx *BoolLiteralContext) {}

// EnterNullLiteral is called when production nullLiteral is entered.
func (s *BaseMySQLParserListener) EnterNullLiteral(ctx *NullLiteralContext) {}

// ExitNullLiteral is called when production nullLiteral is exited.
func (s *BaseMySQLParserListener) ExitNullLiteral(ctx *NullLiteralContext) {}

// EnterTemporalLiteral is called when production temporalLiteral is entered.
func (s *BaseMySQLParserListener) EnterTemporalLiteral(ctx *TemporalLiteralContext) {}

// ExitTemporalLiteral is called when production temporalLiteral is exited.
func (s *BaseMySQLParserListener) ExitTemporalLiteral(ctx *TemporalLiteralContext) {}

// EnterFloatOptions is called when production floatOptions is entered.
func (s *BaseMySQLParserListener) EnterFloatOptions(ctx *FloatOptionsContext) {}

// ExitFloatOptions is called when production floatOptions is exited.
func (s *BaseMySQLParserListener) ExitFloatOptions(ctx *FloatOptionsContext) {}

// EnterStandardFloatOptions is called when production standardFloatOptions is entered.
func (s *BaseMySQLParserListener) EnterStandardFloatOptions(ctx *StandardFloatOptionsContext) {}

// ExitStandardFloatOptions is called when production standardFloatOptions is exited.
func (s *BaseMySQLParserListener) ExitStandardFloatOptions(ctx *StandardFloatOptionsContext) {}

// EnterPrecision is called when production precision is entered.
func (s *BaseMySQLParserListener) EnterPrecision(ctx *PrecisionContext) {}

// ExitPrecision is called when production precision is exited.
func (s *BaseMySQLParserListener) ExitPrecision(ctx *PrecisionContext) {}

// EnterTextOrIdentifier is called when production textOrIdentifier is entered.
func (s *BaseMySQLParserListener) EnterTextOrIdentifier(ctx *TextOrIdentifierContext) {}

// ExitTextOrIdentifier is called when production textOrIdentifier is exited.
func (s *BaseMySQLParserListener) ExitTextOrIdentifier(ctx *TextOrIdentifierContext) {}

// EnterParentheses is called when production parentheses is entered.
func (s *BaseMySQLParserListener) EnterParentheses(ctx *ParenthesesContext) {}

// ExitParentheses is called when production parentheses is exited.
func (s *BaseMySQLParserListener) ExitParentheses(ctx *ParenthesesContext) {}

// EnterEqual is called when production equal is entered.
func (s *BaseMySQLParserListener) EnterEqual(ctx *EqualContext) {}

// ExitEqual is called when production equal is exited.
func (s *BaseMySQLParserListener) ExitEqual(ctx *EqualContext) {}

// EnterVarIdentType is called when production varIdentType is entered.
func (s *BaseMySQLParserListener) EnterVarIdentType(ctx *VarIdentTypeContext) {}

// ExitVarIdentType is called when production varIdentType is exited.
func (s *BaseMySQLParserListener) ExitVarIdentType(ctx *VarIdentTypeContext) {}
