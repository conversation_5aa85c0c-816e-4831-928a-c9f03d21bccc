lexer grammar MySQLLexer;

options {
    superClass = MySQLBaseLexer;
    tokenVocab = predefined; // Keyword tokens in a predefined order for simpler checks.
    exportMacro = PARSERS_PUBLIC_TYPE;
}

tokens {
    NOT2_SYMBOL,
    CONCAT_PIPES_SYMBOL,

    // Tokens assigned in NUMBER rule.
    INT_NUMBER, // NUM in sql_yacc.yy
    LONG_NUMBER,
    ULONGLONG_NUMBER
}

//-------------------------------------------------------------------------------------------------

// Operators
EQUAL_OPERATOR:            '='; // Also assign.
ASSIGN_OPERATOR:           ':=';
NULL_SAFE_EQUAL_OPERATOR:  '<=>';
GREATER_OR_EQUAL_OPERATOR: '>=';
GREATER_THAN_OPERATOR:     '>';
LESS_OR_EQUAL_OPERATOR:    '<=';
LESS_THAN_OPERATOR:        '<';
NOT_EQUAL_OPERATOR:        '!=';
NOT_EQUAL2_OPERATOR:       '<>' -> type(NOT_EQUAL_OPERATOR);

PLUS_OPERATOR:  '+';
MINUS_OPERATOR: '-';
MULT_OPERATOR:  '*';
DIV_OPERATOR:   '/';

MOD_OPERATOR: '%';

LOGICAL_NOT_OPERATOR: '!';
BITWISE_NOT_OPERATOR: '~';

SHIFT_LEFT_OPERATOR:  '<<';
SHIFT_RIGHT_OPERATOR: '>>';

LOGICAL_AND_OPERATOR: '&&';
BITWISE_AND_OPERATOR: '&';

BITWISE_XOR_OPERATOR: '^';

LOGICAL_OR_OPERATOR: '||';
BITWISE_OR_OPERATOR: '|';

DOT_SYMBOL:         '.';
COMMA_SYMBOL:       ',';
SEMICOLON_SYMBOL:   ';';
COLON_SYMBOL:       ':';
OPEN_PAR_SYMBOL:    '(';
CLOSE_PAR_SYMBOL:   ')';
OPEN_CURLY_SYMBOL:  '{';
CLOSE_CURLY_SYMBOL: '}';
UNDERLINE_SYMBOL:   '_';

JSON_SEPARATOR_SYMBOL:          '->' ;  // MYSQL
JSON_UNQUOTED_SEPARATOR_SYMBOL: '->>' ; // MYSQL

// The MySQL server parser uses custom code in its lexer to allow base alphanum chars (and ._$) as variable name.
// For this it handles user variables in 2 different ways and we have to model this to match that behavior.
AT_SIGN_SYMBOL: '@';
AT_TEXT_SUFFIX: '@' SIMPLE_IDENTIFIER;

AT_AT_SIGN_SYMBOL: '@@';

NULL2_SYMBOL: '\\N';
PARAM_MARKER: '?';

fragment A: [aA];
fragment B: [bB];
fragment C: [cC];
fragment D: [dD];
fragment E: [eE];
fragment F: [fF];
fragment G: [gG];
fragment H: [hH];
fragment I: [iI];
fragment J: [jJ];
fragment K: [kK];
fragment L: [lL];
fragment M: [mM];
fragment N: [nN];
fragment O: [oO];
fragment P: [pP];
fragment Q: [qQ];
fragment R: [rR];
fragment S: [sS];
fragment T: [tT];
fragment U: [uU];
fragment V: [vV];
fragment W: [wW];
fragment X: [xX];
fragment Y: [yY];
fragment Z: [zZ];

fragment DIGIT:    [0-9];
fragment DIGITS:   DIGIT+;
fragment HEXDIGIT: [0-9a-fA-F];

// Only lower case 'x' and 'b' count for hex + bin numbers. Otherwise it's an identifier.
HEX_NUMBER: ('0x' HEXDIGIT+) | ('x\'' HEXDIGIT+ '\'');
BIN_NUMBER: ('0b' [01]+) | ('b\'' [01]+ '\'');

INT_NUMBER: DIGITS;

// Float types must be handled first or the DOT_IDENTIIFER rule will make them to identifiers
// (if there is no leading digit before the dot).
DECIMAL_NUMBER: DIGITS? DOT_SYMBOL DIGITS;
FLOAT_NUMBER:   (DIGITS? DOT_SYMBOL)? DIGITS [eE] (MINUS_OPERATOR | PLUS_OPERATOR)? DIGITS;

//ABORT_SYMBOL: 'ABORT';                     // INTERNAL (used in lex)
ADDDATE_SYMBOL:                  A D D D A T E                               ; // MYSQL-FUNC
AGAINST_SYMBOL:                  A G A I N S T;
ALL_SYMBOL:                      A L L;                                      // SQL-2003-R
AND_SYMBOL:                      A N D;                                      // SQL-2003-R
ANY_SYMBOL:                      A N Y;                                      // SQL-2003-R
AS_SYMBOL:                       A S;                                        // SQL-2003-R
ASC_SYMBOL:                      A S C;                                      // SQL-2003-N
ASCII_SYMBOL:                    A S C I I;                                  // MYSQL-FUNC
AVG_SYMBOL:                      A V G;                                      // SQL-2003-N
BETWEEN_SYMBOL:                  B E T W E E N;                              // SQL-2003-R
BIGINT_SYMBOL:                   B I G I N T;                                // SQL-2003-R
BINARY_SYMBOL:                   B I N A R Y;                                // SQL-2003-R
BIT_AND_SYMBOL:                  B I T '_' A N D                             ; // MYSQL-FUNC
BIT_OR_SYMBOL:                   B I T '_' O R                               ; // MYSQL-FUNC
BIT_XOR_SYMBOL:                  B I T '_' X O R                             ; // MYSQL-FUNC
BOOLEAN_SYMBOL:                  B O O L E A N;                              // SQL-2003-R
BOTH_SYMBOL:                     B O T H;                                    // SQL-2003-R
BY_SYMBOL:                       B Y;                                        // SQL-2003-R
BYTE_SYMBOL:                     B Y T E;
CASE_SYMBOL:                     C A S E;                                    // SQL-2003-R
CAST_SYMBOL:                     C A S T                                     ; // SQL-2003-R
CHARSET_SYMBOL:                  C H A R S E T;
CHARACTER_SYMBOL:                C H A R A C T E R                           -> type(CHAR_SYMBOL); // Synonym
CHAR_SYMBOL:                     C H A R;                                    // SQL-2003-R
COALESCE_SYMBOL:                 C O A L E S C E;                            // SQL-2003-N
COLLATE_SYMBOL:                  C O L L A T E;                              // SQL-2003-R
COLLATION_SYMBOL:                C O L L A T I O N;                          // SQL-2003-N
COLUMNS_SYMBOL:                  C O L U M N S;
CONTAINS_SYMBOL:                 C O N T A I N S;                            // SQL-2003-N
CONVERT_SYMBOL:                  C O N V E R T;                              // SQL-2003-N
COUNT_SYMBOL:                    C O U N T                                   ; // SQL-2003-N
CROSS_SYMBOL:                    C R O S S;                                  // SQL-2003-R
CUBE_SYMBOL:                     C U B E;                                    // SQL-2003-R
CURDATE_SYMBOL:                  C U R D A T E                               ; // MYSQL-FUNC
CURRENT_SYMBOL:                  C U R R E N T                               ;
CURRENT_TIMESTAMP_SYMBOL:        C U R R E N T '_' T I M E S T A M P         -> type(NOW_SYMBOL); // Synonym
DATABASE_SYMBOL:                 D A T A B A S E;
DATETIME_SYMBOL:                 D A T E T I M E;                            // MYSQL
DATE_ADD_SYMBOL:                 D A T E '_' A D D                           ;
DATE_SUB_SYMBOL:                 D A T E '_' S U B                           ;
DATE_SYMBOL:                     D A T E;                                    // SQL-2003-R
DAYOFMONTH_SYMBOL:               D A Y O F M O N T H                         -> type(DAY_SYMBOL); // Synonym
DAY_HOUR_SYMBOL:                 D A Y '_' H O U R;
DAY_MICROSECOND_SYMBOL:          D A Y '_' M I C R O S E C O N D;
DAY_MINUTE_SYMBOL:               D A Y '_' M I N U T E;
DAY_SECOND_SYMBOL:               D A Y '_' S E C O N D;
DAY_SYMBOL:                      D A Y;                                      // SQL-2003-R
DEC_SYMBOL:                      D E C                                       -> type(DECIMAL_SYMBOL); // Synonym
DECIMAL_SYMBOL:                  D E C I M A L;                              // SQL-2003-R
DEFAULT_SYMBOL:                  D E F A U L T;                              // SQL-2003-R
DESC_SYMBOL:                     D E S C;                                    // SQL-2003-N
DISTINCT_SYMBOL:                 D I S T I N C T;                            // SQL-2003-R
DISTINCTROW_SYMBOL:              D I S T I N C T R O W                       -> type(DISTINCT_SYMBOL); // Synonym
DIV_SYMBOL:                      D I V;
DOUBLE_SYMBOL:                   D O U B L E;                                // SQL-2003-R
DUAL_SYMBOL:                     D U A L;
ELSE_SYMBOL:                     E L S E;                                    // SQL-2003-R
ELSEIF_SYMBOL:                   E L S E I F;
END_SYMBOL:                      E N D;                                      // SQL-2003-R
ESCAPE_SYMBOL:                   E S C A P E;                                // SQL-2003-R
EXISTS_SYMBOL:                   E X I S T S;                                // SQL-2003-R
EXPANSION_SYMBOL:                E X P A N S I O N;
EXTRACT_SYMBOL:                  E X T R A C T                               ; // SQL-2003-N
FALSE_SYMBOL:                    F A L S E;                                  // SQL-2003-R
FIELDS_SYMBOL:                   F I E L D S                                 -> type(COLUMNS_SYMBOL); // Synonym
FIRST_SYMBOL:                    F I R S T;                                  // SQL-2003-N
FLOAT4_SYMBOL:                   F L O A T '4'                               -> type(FLOAT_SYMBOL); // Synonym
FLOAT8_SYMBOL:                   F L O A T '8'                               -> type(DOUBLE_SYMBOL); // Synonym
FLOAT_SYMBOL:                    F L O A T;                                  // SQL-2003-R
FORCE_SYMBOL:                    F O R C E;
FOR_SYMBOL:                      F O R;                                      // SQL-2003-R
FORMAT_SYMBOL:                   F O R M A T;
FROM_SYMBOL:                     F R O M;
GEOMETRYCOLLECTION_SYMBOL:       G E O M E T R Y C O L L E C T I O N;        // MYSQL
GET_FORMAT_SYMBOL:               G E T '_' F O R M A T;                      // MYSQL-FUNC
GLOBAL_SYMBOL:                   G L O B A L;                                // SQL-2003-R
GROUP_SYMBOL:                    G R O U P;                                  // SQL-2003-R
GROUP_CONCAT_SYMBOL:
    G R O U P '_' C O N C A T
;
HAVING_SYMBOL:                   H A V I N G;                                // SQL-2003-R
HIGH_PRIORITY_SYMBOL:            H I G H '_' P R I O R I T Y;
HOUR_MICROSECOND_SYMBOL:         H O U R '_' M I C R O S E C O N D;
HOUR_MINUTE_SYMBOL:              H O U R '_' M I N U T E;
HOUR_SECOND_SYMBOL:              H O U R '_' S E C O N D;
HOUR_SYMBOL:                     H O U R;                                    // SQL-2003-R
IF_SYMBOL:                       I F;
IGNORE_SYMBOL:                   I G N O R E;
INDEX_SYMBOL:                    I N D E X;
INNER_SYMBOL:                    I N N E R;                                  // SQL-2003-R
INSERT_SYMBOL:                   I N S E R T;                                // SQL-2003-R
INTEGER_SYMBOL:                  I N T E G E R                               -> type(INT_SYMBOL); // Synonym
INTERVAL_SYMBOL:                 I N T E R V A L;                            // SQL-2003-R
INT_SYMBOL:                      I N T;                                      // SQL-2003-R
IN_SYMBOL:                       I N;                                        // SQL-2003-R
IS_SYMBOL:                       I S;                                        // SQL-2003-R
ITERATE_SYMBOL:                  I T E R A T E;
JOIN_SYMBOL:                     J O I N;                                    // SQL-2003-R
JSON_SYMBOL:                     J S O N                                     ; // MYSQL
KEY_SYMBOL:                      K E Y;                                      // SQL-2003-N
LANGUAGE_SYMBOL:                 L A N G U A G E;                            // SQL-2003-R
LAST_SYMBOL:                     L A S T;                                    // SQL-2003-N
LEADING_SYMBOL:                  L E A D I N G;                              // SQL-2003-R
LEAVE_SYMBOL:                    L E A V E;
LEFT_SYMBOL:                     L E F T;                                    // SQL-2003-R
LEVEL_SYMBOL:                    L E V E L;
LIKE_SYMBOL:                     L I K E;                                    // SQL-2003-R
LIMIT_SYMBOL:                    L I M I T;
LINESTRING_SYMBOL:               L I N E S T R I N G;                        // MYSQL
LOCALTIME_SYMBOL:                L O C A L T I M E                           -> type(NOW_SYMBOL); // Synonym
LOCALTIMESTAMP_SYMBOL:           L O C A L T I M E S T A M P                 -> type(NOW_SYMBOL); // Synonym
LOCAL_SYMBOL:                    L O C A L;                                  // SQL-2003-R
MATCH_SYMBOL:                    M A T C H;                                  // SQL-2003-R
MAX_SYMBOL:                      M A X                                       ; // SQL-2003-N
MEDIUMINT_SYMBOL:                M E D I U M I N T;                          // MYSQL
MICROSECOND_SYMBOL:              M I C R O S E C O N D;                      // MYSQL-FUNC
MINUTE_MICROSECOND_SYMBOL:       M I N U T E '_' M I C R O S E C O N D;
MINUTE_SECOND_SYMBOL:            M I N U T E '_' S E C O N D;
MINUTE_SYMBOL:                   M I N U T E;                                // SQL-2003-R
MIN_SYMBOL:                      M I N                                       ; // SQL-2003-N
MODE_SYMBOL:                     M O D E;
MOD_SYMBOL:                      M O D;                                      // SQL-2003-N
MONTH_SYMBOL:                    M O N T H;                                  // SQL-2003-R
MULTILINESTRING_SYMBOL:          M U L T I L I N E S T R I N G;              // MYSQL
MULTIPOINT_SYMBOL:               M U L T I P O I N T;                        // MYSQL
MULTIPOLYGON_SYMBOL:             M U L T I P O L Y G O N;                    // MYSQL
NATIONAL_SYMBOL:                 N A T I O N A L;                            // SQL-2003-R
NATURAL_SYMBOL:                  N A T U R A L;                              // SQL-2003-R
NCHAR_SYMBOL:                    N C H A R;                                  // SQL-2003-R
NOT_SYMBOL:
    N O T
;                                                                            // SQL-2003-R
NOW_SYMBOL:                      N O W                                       ;
NO_SYMBOL:                       N O;                                        // SQL-2003-R
NULL_SYMBOL:                     N U L L;                                    // SQL-2003-R
NVARCHAR_SYMBOL:                 N V A R C H A R;
OFFSET_SYMBOL:                   O F F S E T;
OLD_PASSWORD_SYMBOL:             O L D '_' P A S S W O R D                   ;
ON_SYMBOL:                       O N;                                        // SQL-2003-R
ORDER_SYMBOL:                    O R D E R;                                  // SQL-2003-R
OR_SYMBOL:                       O R;                                        // SQL-2003-R
OUTER_SYMBOL:                    O U T E R;
PARTITION_SYMBOL:                P A R T I T I O N;                          // SQL-2003-R
POINT_SYMBOL:                    P O I N T;
POLYGON_SYMBOL:                  P O L Y G O N;                              // MYSQL
POSITION_SYMBOL:                 P O S I T I O N                             ; // SQL-2003-N
PRECISION_SYMBOL:                P R E C I S I O N;                          // SQL-2003-R
PRIMARY_SYMBOL:                  P R I M A R Y;                              // SQL-2003-R
QUARTER_SYMBOL:                  Q U A R T E R;
QUERY_SYMBOL:                    Q U E R Y;
RANGE_SYMBOL:                    R A N G E;                                  // SQL-2003-R
REAL_SYMBOL:                     R E A L;                                    // SQL-2003-R
REGEXP_SYMBOL:                   R E G E X P;
REPEAT_SYMBOL:                   R E P E A T;                                // MYSQL-FUNC
REPLACE_SYMBOL:                  R E P L A C E;                              // MYSQL-FUNC
REVERSE_SYMBOL:                  R E V E R S E;
RIGHT_SYMBOL:                    R I G H T;                                  // SQL-2003-R
RLIKE_SYMBOL:                    R L I K E                                   -> type(REGEXP_SYMBOL); // Synonym (like in mSQL2)
ROLLUP_SYMBOL:                   R O L L U P;                                // SQL-2003-R
ROWS_SYMBOL:                     R O W S;                                    // SQL-2003-R
ROW_COUNT_SYMBOL:                R O W '_' C O U N T;
ROW_SYMBOL:                      R O W;                                      // SQL-2003-R
SCHEMA_SYMBOL:                   S C H E M A                                 -> type(DATABASE_SYMBOL); // Synonym
SECOND_MICROSECOND_SYMBOL:       S E C O N D '_' M I C R O S E C O N D;
SECOND_SYMBOL:                   S E C O N D;                                // SQL-2003-R
SELECT_SYMBOL:                   S E L E C T;                                // SQL-2003-R
SEPARATOR_SYMBOL:                S E P A R A T O R;
SESSION_SYMBOL:                  S E S S I O N;                              // SQL-2003-N
SET_SYMBOL:                      S E T;                                      // SQL-2003-R
SIGNED_SYMBOL:                   S I G N E D;
SMALLINT_SYMBOL:                 S M A L L I N T;                            // SQL-2003-R
SOUNDS_SYMBOL:                   S O U N D S;
SQL_BIG_RESULT_SYMBOL:           S Q L '_' B I G '_' R E S U L T;
SQL_BUFFER_RESULT_SYMBOL:        S Q L '_' B U F F E R '_' R E S U L T;
SQL_CALC_FOUND_ROWS_SYMBOL:      S Q L '_' C A L C '_' F O U N D '_' R O W S;
SQL_SMALL_RESULT_SYMBOL:         S Q L '_' S M A L L '_' R E S U L T;
STDDEV_SAMP_SYMBOL:
    S T D D E V '_' S A M P
;                                                                            // SQL-2003-N
STD_SYMBOL:                      S T D                                       ;
STRAIGHT_JOIN_SYMBOL:            S T R A I G H T '_' J O I N;
SUBDATE_SYMBOL:                  S U B D A T E                               ;
SUBSTRING_SYMBOL:                S U B S T R I N G                           ; // SQL-2003-N
SUM_SYMBOL:                      S U M                                       ; // SQL-2003-N
SYSDATE_SYMBOL:                  S Y S D A T E                               ;
TABLE_SYMBOL:                    T A B L E;                                  // SQL-2003-R
THEN_SYMBOL:                     T H E N;                                    // SQL-2003-R
TIMESTAMP_SYMBOL:                T I M E S T A M P;                          // SQL-2003-R
TIMESTAMP_ADD_SYMBOL:            T I M E S T A M P '_' A D D;
TIMESTAMP_DIFF_SYMBOL:           T I M E S T A M P '_' D I F F;
TIME_SYMBOL:                     T I M E;                                    // SQL-2003-R
TINYINT_SYMBOL:                  T I N Y I N T;                              // MYSQL
TRAILING_SYMBOL:                 T R A I L I N G;                            // SQL-2003-R
TRIM_SYMBOL:                     T R I M                                     ; // SQL-2003-N
TRUE_SYMBOL:                     T R U E;                                    // SQL-2003-R
TRUNCATE_SYMBOL:                 T R U N C A T E;
UNICODE_SYMBOL:                  U N I C O D E;
UNION_SYMBOL:                    U N I O N;                                  // SQL-2003-R
UNKNOWN_SYMBOL:                  U N K N O W N;                              // SQL-2003-R
UNSIGNED_SYMBOL:                 U N S I G N E D;                            // MYSQL
USER_SYMBOL:                     U S E R;                                    // SQL-2003-R
USE_SYMBOL:                      U S E;
USING_SYMBOL:                    U S I N G;                                  // SQL-2003-R
UTC_DATE_SYMBOL:                 U T C '_' D A T E;
UTC_TIMESTAMP_SYMBOL:            U T C '_' T I M E S T A M P;
UTC_TIME_SYMBOL:                 U T C '_' T I M E;
VALUES_SYMBOL:                   V A L U E S;                                // SQL-2003-R
VARIANCE_SYMBOL:                 V A R I A N C E                             ;
VAR_SAMP_SYMBOL:                 V A R '_' S A M P                           ;
WEEK_SYMBOL:                     W E E K;
WEIGHT_STRING_SYMBOL:            W E I G H T '_' S T R I N G;
WHEN_SYMBOL:                     W H E N;                                    // SQL-2003-R
WHERE_SYMBOL:                    W H E R E;                                  // SQL-2003-R
WITH_SYMBOL:                     W I T H;                                    // SQL-2003-R
XOR_SYMBOL:                      X O R;
YEAR_MONTH_SYMBOL:               Y E A R '_' M O N T H;
YEAR_SYMBOL:                     Y E A R;                                    // SQL-2003-R

/*
   Tokens from MySQL 8.0
*/
RECURSIVE_SYMBOL:                R E C U R S I V E                           ; // SQL-1999-R
JSON_OBJECTAGG_SYMBOL:           J S O N '_' O B J E C T A G G               ; // SQL-2015-R
JSON_ARRAYAGG_SYMBOL:            J S O N '_' A R R A Y A G G                 ; // SQL-2015-R
OF_SYMBOL:                       O F                                         ; // SQL-1999-R
GROUPING_SYMBOL:                 G R O U P I N G                             ; // SQL-2011-R
CUME_DIST_SYMBOL:                C U M E '_' D I S T                         ; // SQL-2003-R
DENSE_RANK_SYMBOL:               D E N S E '_' R A N K                       ; // SQL-2003-R
EXCLUDE_SYMBOL:                  E X C L U D E                               ; // SQL-2003-N
FIRST_VALUE_SYMBOL:              F I R S T '_' V A L U E                     ; // SQL-2011-R
FOLLOWING_SYMBOL:                F O L L O W I N G                           ; // SQL-2003-N
GROUPS_SYMBOL:                   G R O U P S                                 ; // SQL-2011-R
LAG_SYMBOL:                      L A G                                       ; // SQL-2011-R
LAST_VALUE_SYMBOL:               L A S T '_' V A L U E                       ; // SQL-2011-R
LEAD_SYMBOL:                     L E A D                                     ; // SQL-2011-R
NTH_VALUE_SYMBOL:                N T H '_' V A L U E                         ; // SQL-2011-R
NTILE_SYMBOL:                    N T I L E                                   ; // SQL-2011-R
NULLS_SYMBOL:                    N U L L S                                   ; // SQL-2003-N
OTHERS_SYMBOL:                   O T H E R S                                 ; // SQL-2003-N
OVER_SYMBOL:                     O V E R                                     ; // SQL-2003-R
PERCENT_RANK_SYMBOL:             P E R C E N T '_' R A N K                   ; // SQL-2003-R
PRECEDING_SYMBOL:                P R E C E D I N G                           ; // SQL-2003-N
RANK_SYMBOL:                     R A N K                                     ; // SQL-2003-R
RESPECT_SYMBOL:                  R E S P E C T                               ; // SQL_2011-N
ROW_NUMBER_SYMBOL:               R O W '_' N U M B E R                       ; // SQL-2003-R
TIES_SYMBOL:                     T I E S                                     ; // SQL-2003-N
UNBOUNDED_SYMBOL:                U N B O U N D E D                           ; // SQL-2003-N
WINDOW_SYMBOL:                   W I N D O W                                 ; // SQL-2003-R

LATERAL_SYMBOL:                  L A T E R A L                               ; // SQL-2003-R

ARRAY_SYMBOL:                    A R R A Y                                   ; // SQL-2003-R
OJ_SYMBOL:                       O J                                         ; // ODBC
MEMBER_SYMBOL:                   M E M B E R                                 ; // SQL-2003-R

// $antlr-format groupedAlignments on, alignTrailers off, alignLexerCommands on

// Additional tokens which are mapped to existing tokens.
INT1_SYMBOL: I N T '1' -> type(TINYINT_SYMBOL);   // Synonym
INT2_SYMBOL: I N T '2' -> type(SMALLINT_SYMBOL);  // Synonym
INT3_SYMBOL: I N T '3' -> type(MEDIUMINT_SYMBOL); // Synonym
INT4_SYMBOL: I N T '4' -> type(INT_SYMBOL);       // Synonym
INT8_SYMBOL: I N T '8' -> type(BIGINT_SYMBOL);    // Synonym

SQL_TSI_SECOND_SYMBOL:  S Q L '_' T S I '_' S E C O N D   -> type(SECOND_SYMBOL);  // Synonym
SQL_TSI_MINUTE_SYMBOL:  S Q L '_' T S I '_' M I N U T E   -> type(MINUTE_SYMBOL);  // Synonym
SQL_TSI_HOUR_SYMBOL:    S Q L '_' T S I '_' H O U R       -> type(HOUR_SYMBOL);    // Synonym
SQL_TSI_DAY_SYMBOL:     S Q L '_' T S I '_' D A Y         -> type(DAY_SYMBOL);     // Synonym
SQL_TSI_WEEK_SYMBOL:    S Q L '_' T S I '_' W E E K       -> type(WEEK_SYMBOL);    // Synonym
SQL_TSI_MONTH_SYMBOL:   S Q L '_' T S I '_' M O N T H     -> type(MONTH_SYMBOL);   // Synonym
SQL_TSI_QUARTER_SYMBOL: S Q L '_' T S I '_' Q U A R T E R -> type(QUARTER_SYMBOL); // Synonym
SQL_TSI_YEAR_SYMBOL:    S Q L '_' T S I '_' Y E A R       -> type(YEAR_SYMBOL);    // Synonym

// White space handling
WHITESPACE: [ \t\f\r\n] -> channel(HIDDEN); // Ignore whitespaces.

// Input not covered elsewhere (unless quoted).
INVALID_INPUT:
    [\u0001-\u0008]   // Control codes.
    | '\u000B'        // Line tabulation.
    | '\u000C'        // Form feed.
    | [\u000E-\u001F] // More control codes.
    | '['
    | ']'
;

// String and text types.

// The underscore charset token is used to defined the repertoire of a string, though it conflicts
// with normal identifiers, which also can start with an underscore.
UNDERSCORE_CHARSET: UNDERLINE_SYMBOL [a-z0-9]+ { l.SetType(checkCharset(l.GetText())); };

// Identifiers might start with a digit, even though it is discouraged, and may not consist entirely of digits only.
// All keywords above are automatically excluded.
IDENTIFIER:
    DIGITS+ [eE] (LETTER_WHEN_UNQUOTED_NO_DIGIT LETTER_WHEN_UNQUOTED*)? // Have to exclude float pattern, as this rule matches more.
    | DIGITS+ LETTER_WITHOUT_FLOAT_PART LETTER_WHEN_UNQUOTED*
    | LETTER_WHEN_UNQUOTED_NO_DIGIT LETTER_WHEN_UNQUOTED* // INT_NUMBER matches first if there are only digits.
;

NCHAR_TEXT: [nN] SINGLE_QUOTED_TEXT;

// MySQL supports automatic concatenation of multiple single and double quoted strings if they follow each other as separate
// tokens. This is reflected in the `textLiteral` parser rule.
// Here we handle duplication of quotation chars only (which must be replaced by a single char in the target code).

fragment BACK_TICK:    '`';
fragment SINGLE_QUOTE: '\'';
fragment DOUBLE_QUOTE: '"';

BACK_TICK_QUOTED_ID: BACK_TICK (({!p.IsSqlModeActive(NoBackslashEscapes)}? '\\')? .)*? BACK_TICK;

DOUBLE_QUOTED_TEXT: (
        DOUBLE_QUOTE (({!p.IsSqlModeActive(NoBackslashEscapes)}? '\\' .)? .)*? DOUBLE_QUOTE
    )+
;

SINGLE_QUOTED_TEXT: (
        SINGLE_QUOTE (({!p.IsSqlModeActive(NoBackslashEscapes)}? '\\')? .)*? SINGLE_QUOTE
    )+
;

// There are 3 types of block comments:
// /* ... */ - The standard multi line comment.
// /*! ... */ - A comment used to mask code for other clients. In MySQL the content is handled as normal code.
// /*!12345 ... */ - Same as the previous one except code is only used when the given number is lower
//                   than the current server version (specifying so the minimum server version the code can run with).
VERSION_COMMENT_START: ('/*!' DIGITS) (
        {checkVersion()}? // Will set inVersionComment if the number matches.
        | .*? '*/'
    ) -> channel(HIDDEN)
;

// inVersionComment is a variable in the base lexer.
// TODO: use a lexer mode instead of a member variable.
MYSQL_COMMENT_START: '/*!'                                                  -> channel(HIDDEN);
VERSION_COMMENT_END: '*/'                                                   -> channel(HIDDEN);
BLOCK_COMMENT:       ( '/**/' | '/*' ~[!] .*? '*/')                         -> channel(HIDDEN);

POUND_COMMENT:    '#' ~([\n\r])*                                   -> channel(HIDDEN);
DASHDASH_COMMENT: DOUBLE_DASH ([ \t] (~[\n\r])* | LINEBREAK | EOF) -> channel(HIDDEN);

fragment DOUBLE_DASH: '--';
fragment LINEBREAK:   [\n\r];

fragment SIMPLE_IDENTIFIER: (DIGIT | [a-zA-Z_$] | DOT_SYMBOL)+;

fragment ML_COMMENT_HEAD: '/*';
fragment ML_COMMENT_END:  '*/';

// As defined in https://dev.mysql.com/doc/refman/8.0/en/identifiers.html.
fragment LETTER_WHEN_UNQUOTED: DIGIT | LETTER_WHEN_UNQUOTED_NO_DIGIT;

fragment LETTER_WHEN_UNQUOTED_NO_DIGIT: [a-zA-Z_$\u0080-\uffff];

// Any letter but without e/E and digits (which are used to match a decimal number).
fragment LETTER_WITHOUT_FLOAT_PART: [a-df-zA-DF-Z_$\u0080-\uffff];
