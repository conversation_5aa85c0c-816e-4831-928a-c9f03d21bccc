package parser

import (
	"github.com/antlr4-go/antlr/v4"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
)

// NewCustomMySQLParserWithVersionAndSqlMode
//
//	serverVersion: 50000-89999, 以mysql8为例, 可以传入89999, 支持当前mysql8+所有语法
//	sqlMode: 默认NoMode, 按需传入
func NewCustomMySQLParserWithVersionAndSqlMode(input antlr.TokenStream, serverVersion int, sqlMode SqlMode) *MySQLParser {
	MySQLParserInit()
	s := new(MySQLParser)
	s.BaseParser = antlr.NewBaseParser(input)
	s.MySQLRecognizerCommon = &MySQLRecognizerCommon{SqlMode: sqlMode, ServerVersion: serverVersion}
	staticData := &MySQLParserParserStaticData
	s.Interpreter = antlr.NewParserATNSimulator(s, staticData.atn, staticData.decisionToDFA, staticData.PredictionContextCache)
	if !global.AppConfig.DmpApi.EnableAntlr4Cache {
		// fix: https://github.com/antlr/antlr4/issues/499
		// 处理长时间不同sql解析后的内存泄漏问题
		dfa := lo.Map(s.GetInterpreter().DecisionToDFA(), func(item *antlr.DFA, idx int) *antlr.DFA {
			return antlr.NewDFA(s.GetATN().DecisionToState[idx], idx)
		})
		s.Interpreter = antlr.NewParserATNSimulator(s, s.GetATN(), dfa, antlr.NewPredictionContextCache())
	}
	s.RuleNames = staticData.RuleNames
	s.LiteralNames = staticData.LiteralNames
	s.SymbolicNames = staticData.SymbolicNames
	s.GrammarFileName = "MySQLParser.g4"
	return s
}

// NewCustomMySQLParser serverVersion: 89999, sqlMode: noMode
func NewCustomMySQLParser(input antlr.TokenStream) *MySQLParser {
	return NewCustomMySQLParserWithVersionAndSqlMode(input, 89999, NoMode)
}

type MySQLBaseRecognizer struct {
	*antlr.BaseParser
	*MySQLRecognizerCommon
}
