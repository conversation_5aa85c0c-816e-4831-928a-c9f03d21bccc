package parser

import (
	"github.com/antlr4-go/antlr/v4"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
)

var _ antlr.Lexer = (*MySQLLexer)(nil)

// NewCustomMySQLLexerWithVersionAndSqlMode produces a new lexer instance for the optional input antlr.CharStream.
func NewCustomMySQLLexerWithVersionAndSqlMode(input antlr.CharStream, serverVersion int, sqlMode SqlMode) *MySQLLexer {
	MySQLLexerInit()
	l := new(MySQLLexer)
	l.MySQLRecognizerCommon = &MySQLRecognizerCommon{SqlMode: sqlMode, ServerVersion: serverVersion}
	l.inVersionComment = false
	l.BaseLexer = antlr.NewBaseLexer(input)
	staticData := &MySQLLexerLexerStaticData
	l.Interpreter = antlr.NewLexerATNSimulator(l, staticData.atn, staticData.decisionToDFA, staticData.PredictionContextCache)
	if !global.AppConfig.DmpApi.EnableAntlr4Cache {
		// fix: https://github.com/antlr/antlr4/issues/499
		// 处理长时间不同sql解析后的内存泄漏问题
		dfa := lo.Map(l.GetInterpreter().DecisionToDFA(), func(item *antlr.DFA, idx int) *antlr.DFA {
			return antlr.NewDFA(l.GetATN().DecisionToState[idx], idx)
		})
		l.Interpreter = antlr.NewLexerATNSimulator(l, l.GetATN(), dfa, antlr.NewPredictionContextCache())
	}
	l.channelNames = staticData.ChannelNames
	l.modeNames = staticData.ModeNames
	l.RuleNames = staticData.RuleNames
	l.LiteralNames = staticData.LiteralNames
	l.SymbolicNames = staticData.SymbolicNames
	l.GrammarFileName = "MySQLLexer.g4"
	// TODO: l.EOF = antlr.TokenEOF

	return l
}

func NewCustomMySQLLexer(input antlr.CharStream) *MySQLLexer {
	return NewCustomMySQLLexerWithVersionAndSqlMode(input, 89999, NoMode)
}

type MySQLBaseLexer struct {
	*antlr.BaseLexer
	*MySQLRecognizerCommon
	inVersionComment bool
	pendingTokens    []antlr.Token
	lexerRef         *MySQLLexer
}

func (l *MySQLBaseLexer) NextToken() antlr.Token {
	if len(l.pendingTokens) > 0 {
		pending := l.pendingTokens[0]
		l.pendingTokens = l.pendingTokens[1:]
		return pending
	}
	next := l.BaseLexer.NextToken()
	if len(l.pendingTokens) > 0 {
		pending := l.pendingTokens[0]
		l.pendingTokens = l.pendingTokens[1:]
		l.pendingTokens = append(l.pendingTokens, next)
		return pending
	}
	return next
}

//func (l *MySQLBaseLexer) EmitDot() {
//	//DOT_IDENTIFIER:
//	//    DOT_SYMBOL LETTER_WHEN_UNQUOTED_NO_DIGIT LETTER_WHEN_UNQUOTED* { l.EmitDot(); } -> type(IDENTIFIER)
//	//;
//	l.pendingTokens = append(l.pendingTokens, l.GetTokenFactory().Create(
//		l.GetTokenSourceCharStreamPair(),
//		MySQLLexerDOT_SYMBOL,
//		l.GetText(),
//		antlr.TokenDefaultChannel, // FIXME: 应该使用l.channel, 但作为私有变量无法访问
//		l.TokenStartCharIndex,
//		l.TokenStartCharIndex,
//		l.TokenStartLine,
//		l.TokenStartColumn,
//	))
//	l.TokenStartCharIndex += 1
//}

func (l *MySQLBaseLexer) DetermineFunction(proposed int) int {
	if l.IsSqlModeActive(IgnoreSpace) {
		input := l.GetInputStream().LA(1)
		for input == ' ' || input == '\t' || input == '\r' || input == '\n' {
			l.GetInterpreter().Consume(l.GetInputStream())
			l.SetChannel(antlr.TokenHiddenChannel)
			l.SetType(MySQLLexerWHITESPACE)
			input = l.GetInputStream().LA(1)
		}
	}
	if l.GetInputStream().LA(1) == '(' {
		return proposed
	} else {
		return MySQLLexerIDENTIFIER
	}
}

func (l *MySQLBaseLexer) DetermineNumericType(text string) int {
	// TODO: 如果需要确定数字的类型，需要实现DetermineNumericType，当前默认返回MySQLLexerINT_NUMBER
	return MySQLLexerINT_NUMBER
}

func (l *MySQLBaseLexer) Action(localctx antlr.RuleContext, ruleIndex, actionIndex int) {
	l.lexerRef.Action(localctx, ruleIndex, actionIndex)
}
