package starrocks

import (
	"context"
	"github.com/antlr4-go/antlr/v4"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/sqlparse"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/sqlparse/antlr4/starrocks/sql/parser"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/sqlparse/antlr4/utils"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/sqlparse/common"
)

var _ sqlparse.StmtTypeParser = (*StmtTypeParser)(nil)

type StmtTypeParser struct {
	listener *StmtTypeListener
	sql      string
}

func NewStmtTypeParser(sql string) *StmtTypeParser {
	return &StmtTypeParser{
		NewStmtTypeListener(),
		sql,
	}
}

func (s *StmtTypeParser) Parse(ctx context.Context) (stmtType common.StmtType, err error) {
	defer func() {
		if e := recover(); e != nil {
			var ok bool
			err, ok = e.(*utils.ParseCancellationException)
			if !ok {
				panic(e)
			}
			return
		}
	}()
	input := antlr.NewInputStream(s.sql)
	upper := utils.NewCaseChangingStream(input, true)
	lexer := parser.NewCustomStarRocksLexer(upper)
	stream := antlr.NewCommonTokenStream(lexer, 0)
	p := parser.NewCustomStarRocksParser(stream)
	p.RemoveErrorListeners()
	p.AddErrorListener(utils.NewCancelErrorLitener())
	tree := p.Statement()
	antlr.ParseTreeWalkerDefault.Walk(s.listener, tree)
	return s.listener.StmtType, nil
}

func NewStmtTypeListener() *StmtTypeListener {
	return new(StmtTypeListener)
}

type StmtTypeListener struct {
	*parser.BaseStarRocksListener
	StmtType common.StmtType
}

func (s *StmtTypeListener) EnterQueryStatement(c *parser.QueryStatementContext) {
	s.StmtType = common.SelectStmt
	return
}
