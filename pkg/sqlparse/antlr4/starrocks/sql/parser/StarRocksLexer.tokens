T__0=1
T__1=2
T__2=3
T__3=4
T__4=5
T__5=6
T__6=7
T__7=8
T__8=9
T__9=10
T__10=11
ACCESS=12
ACTIVE=13
ADD=14
ADMIN=15
AFTER=16
AGGREGATE=17
ALL=18
ALTER=19
ANALYZE=20
AND=21
ANTI=22
APPLY=23
ARRAY=24
ARRAY_AGG=25
AS=26
ASC=27
ASYNC=28
AUTHORS=29
AUTHENTICATION=30
AUTO_INCREMENT=31
AVG=32
BACKEND=33
BACKENDS=34
BACKUP=35
BASE=36
BEGIN=37
BETWEEN=38
BIGINT=39
BINARY=40
BITMAP=41
BITMAP_UNION=42
BLACKLIST=43
BODY=44
BOOLEAN=45
BOTH=46
BROKER=47
BUCKETS=48
BUILTIN=49
BY=50
CANCEL=51
CASE=52
CAST=53
CATALOG=54
CATALOGS=55
CEIL=56
CHAIN=57
CHAR=58
CHARACTER=59
CHARSET=60
CHECK=61
CLEAN=62
CLUSTER=63
CLUSTERS=64
COLLATE=65
COLLATION=66
COLUMN=67
COLUMNS=68
COMMENT=69
COMMIT=70
COMMITTED=71
COMPACT=72
COMPACTION=73
COMPUTE=74
CONFIG=75
CONNECTION=76
CONSISTENT=77
CONVERT=78
COSTS=79
COUNT=80
CREATE=81
CROSS=82
CUBE=83
CUME_DIST=84
CUMULATIVE=85
CURRENT=86
CURRENT_DATE=87
CURRENT_ROLE=88
CURRENT_TIME=89
CURRENT_TIMESTAMP=90
CURRENT_USER=91
DATA=92
DATABASE=93
DATABASES=94
DATE=95
DATETIME=96
DAY=97
DECIMAL=98
DECIMALV2=99
DECIMAL32=100
DECIMAL64=101
DECIMAL128=102
DECOMMISSION=103
DEFAULT=104
DELETE=105
DENSE_RANK=106
DEFERRED=107
NTILE=108
DESC=109
DESCRIBE=110
DISTINCT=111
DISTRIBUTED=112
DISTRIBUTION=113
DOUBLE=114
DROP=115
DUAL=116
DUPLICATE=117
DYNAMIC=118
ELSE=119
ENCLOSE=120
END=121
ENGINE=122
ENGINES=123
ERRORS=124
ESCAPE=125
EVENTS=126
EXCEPT=127
EXECUTE=128
EXISTS=129
EXPLAIN=130
EXPORT=131
EXTERNAL=132
EXTRACT=133
EVERY=134
FALSE=135
FIELDS=136
FILE=137
FILES=138
FILTER=139
FIRST=140
FIRST_VALUE=141
FLOAT=142
FLOOR=143
FN=144
FOLLOWING=145
FOLLOWER=146
FOR=147
FORCE=148
FORMAT=149
FREE=150
FROM=151
FRONTEND=152
FRONTENDS=153
FULL=154
FUNCTION=155
FUNCTIONS=156
GLOBAL=157
GRANT=158
GRANTS=159
GROUP=160
GROUPS=161
GROUPING=162
GROUPING_ID=163
GROUP_CONCAT=164
HASH=165
HAVING=166
HELP=167
HISTOGRAM=168
HLL=169
HLL_UNION=170
HOST=171
HOUR=172
HUB=173
IDENTIFIED=174
IF=175
IMPERSONATE=176
IMMEDIATE=177
IGNORE=178
IMAGE=179
IN=180
INACTIVE=181
INCREMENTAL=182
INDEX=183
INDEXES=184
INFILE=185
INNER=186
INSTALL=187
INSERT=188
INT=189
INTEGER=190
INTEGRATION=191
INTEGRATIONS=192
INTERMEDIATE=193
INTERSECT=194
INTERVAL=195
INTO=196
OVERWRITE=197
IS=198
ISOLATION=199
JOB=200
JOIN=201
JSON=202
KEY=203
KEYS=204
KILL=205
LABEL=206
LAG=207
LARGEINT=208
LAST=209
LAST_VALUE=210
LATERAL=211
LEAD=212
LEFT=213
LESS=214
LEVEL=215
LIKE=216
LIMIT=217
LIST=218
LOAD=219
LOCAL=220
LOCALTIME=221
LOCALTIMESTAMP=222
LOCATION=223
LOCATIONS=224
LOGICAL=225
MANUAL=226
MAP=227
MAPPING=228
MAPPINGS=229
MASKING=230
MATERIALIZED=231
MAX=232
MAXVALUE=233
MERGE=234
MIN=235
MINUTE=236
MINUS=237
META=238
MOD=239
MODE=240
MODIFY=241
MONTH=242
NAME=243
NAMES=244
NEGATIVE=245
NO=246
NODE=247
NODES=248
NONE=249
NOT=250
NULL=251
NULLS=252
NUMBER=253
NUMERIC=254
OBSERVER=255
OF=256
OFFSET=257
ON=258
ONLY=259
OPEN=260
OPERATE=261
OPTIMIZER=262
OPTION=263
OR=264
ORDER=265
OUTER=266
OUTFILE=267
OVER=268
PARTITION=269
PARTITIONS=270
PASSWORD=271
PATH=272
PAUSE=273
PENDING=274
PERCENT_RANK=275
PERCENTILE=276
PERCENTILE_UNION=277
PLUGIN=278
PLUGINS=279
POLICY=280
POLICIES=281
PRECEDING=282
PRIMARY=283
PRIVILEGES=284
PROC=285
PROCEDURE=286
PROCESSLIST=287
PROFILE=288
PROFILELIST=289
PROPERTIES=290
PROPERTY=291
QUALIFY=292
QUARTER=293
QUERY=294
QUERIES=295
QUEUE=296
QUOTA=297
RANDOM=298
RANGE=299
RANK=300
READ=301
RECOVER=302
REFRESH=303
REWRITE=304
REGEXP=305
RELEASE=306
REMOVE=307
RENAME=308
REPAIR=309
REPEATABLE=310
REPLACE=311
REPLACE_IF_NOT_NULL=312
REPLICA=313
REPOSITORY=314
REPOSITORIES=315
RESOURCE=316
RESOURCES=317
RESTORE=318
RESUME=319
RETURNS=320
REVOKE=321
REVERT=322
RIGHT=323
RLIKE=324
ROLE=325
ROLES=326
ROLLBACK=327
ROLLUP=328
ROUTINE=329
ROW=330
ROWS=331
ROW_NUMBER=332
RUNNING=333
SAMPLE=334
SCHEDULER=335
SCHEMA=336
SCHEMAS=337
SECOND=338
SECURITY=339
SELECT=340
SEMI=341
SEPARATOR=342
SERIALIZABLE=343
SESSION=344
SET=345
SETS=346
SET_VAR=347
SIGNED=348
SKIP_HEADER=349
SHOW=350
SMALLINT=351
SNAPSHOT=352
SQLBLACKLIST=353
START=354
STATS=355
STATUS=356
STOP=357
STORAGE=358
STREAM=359
STRING=360
TEXT=361
SUBMIT=362
SUM=363
SUSPEND=364
SYNC=365
SYSTEM=366
SYSTEM_TIME=367
SWAP=368
STRUCT=369
TABLE=370
TABLES=371
TABLET=372
TASK=373
TEMPORARY=374
TERMINATED=375
THAN=376
THEN=377
TIME=378
TIMESTAMP=379
TIMESTAMPADD=380
TIMESTAMPDIFF=381
TINYINT=382
TRANSACTION=383
TO=384
TRACE=385
TRIGGERS=386
TRIM_SPACE=387
TRUE=388
TRUNCATE=389
TYPE=390
TYPES=391
UNBOUNDED=392
UNCOMMITTED=393
UNION=394
UNIQUE=395
UNINSTALL=396
UNSET=397
UNSIGNED=398
UPDATE=399
USAGE=400
USE=401
USER=402
USERS=403
USING=404
VALUE=405
VALUES=406
VARBINARY=407
VARCHAR=408
VARIABLES=409
VERBOSE=410
VIEW=411
VIEWS=412
VOLUME=413
VOLUMES=414
WAREHOUSE=415
WAREHOUSES=416
WARNINGS=417
WEEK=418
WHEN=419
WHERE=420
WHITELIST=421
WITH=422
WORK=423
WRITE=424
YEAR=425
LOCK=426
UNLOCK=427
LOW_PRIORITY=428
EQ=429
NEQ=430
LT=431
LTE=432
GT=433
GTE=434
EQ_FOR_NULL=435
PLUS_SYMBOL=436
MINUS_SYMBOL=437
ASTERISK_SYMBOL=438
SLASH_SYMBOL=439
PERCENT_SYMBOL=440
LOGICAL_OR=441
LOGICAL_AND=442
LOGICAL_NOT=443
INT_DIV=444
BITAND=445
BITOR=446
BITXOR=447
BITNOT=448
BIT_SHIFT_LEFT=449
BIT_SHIFT_RIGHT=450
BIT_SHIFT_RIGHT_LOGICAL=451
ARROW=452
AT=453
INTEGER_VALUE=454
DECIMAL_VALUE=455
DOUBLE_VALUE=456
SINGLE_QUOTED_TEXT=457
DOUBLE_QUOTED_TEXT=458
BINARY_SINGLE_QUOTED_TEXT=459
BINARY_DOUBLE_QUOTED_TEXT=460
LETTER_IDENTIFIER=461
DIGIT_IDENTIFIER=462
BACKQUOTED_IDENTIFIER=463
DOT_IDENTIFIER=464
SIMPLE_COMMENT=465
BRACKETED_COMMENT=466
SEMICOLON=467
DOTDOTDOT=468
WS=469
'.'=1
'('=2
','=3
')'=4
'['=5
']'=6
'/*+'=7
'*/'=8
':'=9
'{'=10
'}'=11
'ACCESS'=12
'ACTIVE'=13
'ADD'=14
'ADMIN'=15
'AFTER'=16
'AGGREGATE'=17
'ALL'=18
'ALTER'=19
'ANALYZE'=20
'AND'=21
'ANTI'=22
'APPLY'=23
'ARRAY'=24
'ARRAY_AGG'=25
'AS'=26
'ASC'=27
'ASYNC'=28
'AUTHORS'=29
'AUTHENTICATION'=30
'AUTO_INCREMENT'=31
'AVG'=32
'BACKEND'=33
'BACKENDS'=34
'BACKUP'=35
'BASE'=36
'BEGIN'=37
'BETWEEN'=38
'BIGINT'=39
'BINARY'=40
'BITMAP'=41
'BITMAP_UNION'=42
'BLACKLIST'=43
'BODY'=44
'BOOLEAN'=45
'BOTH'=46
'BROKER'=47
'BUCKETS'=48
'BUILTIN'=49
'BY'=50
'CANCEL'=51
'CASE'=52
'CAST'=53
'CATALOG'=54
'CATALOGS'=55
'CEIL'=56
'CHAIN'=57
'CHAR'=58
'CHARACTER'=59
'CHARSET'=60
'CHECK'=61
'CLEAN'=62
'CLUSTER'=63
'CLUSTERS'=64
'COLLATE'=65
'COLLATION'=66
'COLUMN'=67
'COLUMNS'=68
'COMMENT'=69
'COMMIT'=70
'COMMITTED'=71
'COMPACT'=72
'COMPACTION'=73
'COMPUTE'=74
'CONFIG'=75
'CONNECTION'=76
'CONSISTENT'=77
'CONVERT'=78
'COSTS'=79
'COUNT'=80
'CREATE'=81
'CROSS'=82
'CUBE'=83
'CUME_DIST'=84
'CUMULATIVE'=85
'CURRENT'=86
'CURRENT_DATE'=87
'CURRENT_ROLE'=88
'CURRENT_TIME'=89
'CURRENT_TIMESTAMP'=90
'CURRENT_USER'=91
'DATA'=92
'DATABASE'=93
'DATABASES'=94
'DATE'=95
'DATETIME'=96
'DAY'=97
'DECIMAL'=98
'DECIMALV2'=99
'DECIMAL32'=100
'DECIMAL64'=101
'DECIMAL128'=102
'DECOMMISSION'=103
'DEFAULT'=104
'DELETE'=105
'DENSE_RANK'=106
'DEFERRED'=107
'NTILE'=108
'DESC'=109
'DESCRIBE'=110
'DISTINCT'=111
'DISTRIBUTED'=112
'DISTRIBUTION'=113
'DOUBLE'=114
'DROP'=115
'DUAL'=116
'DUPLICATE'=117
'DYNAMIC'=118
'ELSE'=119
'ENCLOSE'=120
'END'=121
'ENGINE'=122
'ENGINES'=123
'ERRORS'=124
'ESCAPE'=125
'EVENTS'=126
'EXCEPT'=127
'EXECUTE'=128
'EXISTS'=129
'EXPLAIN'=130
'EXPORT'=131
'EXTERNAL'=132
'EXTRACT'=133
'EVERY'=134
'FALSE'=135
'FIELDS'=136
'FILE'=137
'FILES'=138
'FILTER'=139
'FIRST'=140
'FIRST_VALUE'=141
'FLOAT'=142
'FLOOR'=143
'FN'=144
'FOLLOWING'=145
'FOLLOWER'=146
'FOR'=147
'FORCE'=148
'FORMAT'=149
'FREE'=150
'FROM'=151
'FRONTEND'=152
'FRONTENDS'=153
'FULL'=154
'FUNCTION'=155
'FUNCTIONS'=156
'GLOBAL'=157
'GRANT'=158
'GRANTS'=159
'GROUP'=160
'GROUPS'=161
'GROUPING'=162
'GROUPING_ID'=163
'GROUP_CONCAT'=164
'HASH'=165
'HAVING'=166
'HELP'=167
'HISTOGRAM'=168
'HLL'=169
'HLL_UNION'=170
'HOST'=171
'HOUR'=172
'HUB'=173
'IDENTIFIED'=174
'IF'=175
'IMPERSONATE'=176
'IMMEDIATE'=177
'IGNORE'=178
'IMAGE'=179
'IN'=180
'INACTIVE'=181
'INCREMENTAL'=182
'INDEX'=183
'INDEXES'=184
'INFILE'=185
'INNER'=186
'INSTALL'=187
'INSERT'=188
'INT'=189
'INTEGER'=190
'INTEGRATION'=191
'INTEGRATIONS'=192
'INTERMEDIATE'=193
'INTERSECT'=194
'INTERVAL'=195
'INTO'=196
'OVERWRITE'=197
'IS'=198
'ISOLATION'=199
'JOB'=200
'JOIN'=201
'JSON'=202
'KEY'=203
'KEYS'=204
'KILL'=205
'LABEL'=206
'LAG'=207
'LARGEINT'=208
'LAST'=209
'LAST_VALUE'=210
'LATERAL'=211
'LEAD'=212
'LEFT'=213
'LESS'=214
'LEVEL'=215
'LIKE'=216
'LIMIT'=217
'LIST'=218
'LOAD'=219
'LOCAL'=220
'LOCALTIME'=221
'LOCALTIMESTAMP'=222
'LOCATION'=223
'LOCATIONS'=224
'LOGICAL'=225
'MANUAL'=226
'MAP'=227
'MAPPING'=228
'MAPPINGS'=229
'MASKING'=230
'MATERIALIZED'=231
'MAX'=232
'MAXVALUE'=233
'MERGE'=234
'MIN'=235
'MINUTE'=236
'MINUS'=237
'META'=238
'MOD'=239
'MODE'=240
'MODIFY'=241
'MONTH'=242
'NAME'=243
'NAMES'=244
'NEGATIVE'=245
'NO'=246
'NODE'=247
'NODES'=248
'NONE'=249
'NOT'=250
'NULL'=251
'NULLS'=252
'NUMBER'=253
'NUMERIC'=254
'OBSERVER'=255
'OF'=256
'OFFSET'=257
'ON'=258
'ONLY'=259
'OPEN'=260
'OPERATE'=261
'OPTIMIZER'=262
'OPTION'=263
'OR'=264
'ORDER'=265
'OUTER'=266
'OUTFILE'=267
'OVER'=268
'PARTITION'=269
'PARTITIONS'=270
'PASSWORD'=271
'PATH'=272
'PAUSE'=273
'PENDING'=274
'PERCENT_RANK'=275
'PERCENTILE'=276
'PERCENTILE_UNION'=277
'PLUGIN'=278
'PLUGINS'=279
'POLICY'=280
'POLICIES'=281
'PRECEDING'=282
'PRIMARY'=283
'PRIVILEGES'=284
'PROC'=285
'PROCEDURE'=286
'PROCESSLIST'=287
'PROFILE'=288
'PROFILELIST'=289
'PROPERTIES'=290
'PROPERTY'=291
'QUALIFY'=292
'QUARTER'=293
'QUERY'=294
'QUERIES'=295
'QUEUE'=296
'QUOTA'=297
'RANDOM'=298
'RANGE'=299
'RANK'=300
'READ'=301
'RECOVER'=302
'REFRESH'=303
'REWRITE'=304
'REGEXP'=305
'RELEASE'=306
'REMOVE'=307
'RENAME'=308
'REPAIR'=309
'REPEATABLE'=310
'REPLACE'=311
'REPLACE_IF_NOT_NULL'=312
'REPLICA'=313
'REPOSITORY'=314
'REPOSITORIES'=315
'RESOURCE'=316
'RESOURCES'=317
'RESTORE'=318
'RESUME'=319
'RETURNS'=320
'REVOKE'=321
'REVERT'=322
'RIGHT'=323
'RLIKE'=324
'ROLE'=325
'ROLES'=326
'ROLLBACK'=327
'ROLLUP'=328
'ROUTINE'=329
'ROW'=330
'ROWS'=331
'ROW_NUMBER'=332
'RUNNING'=333
'SAMPLE'=334
'SCHEDULER'=335
'SCHEMA'=336
'SCHEMAS'=337
'SECOND'=338
'SECURITY'=339
'SELECT'=340
'SEMI'=341
'SEPARATOR'=342
'SERIALIZABLE'=343
'SESSION'=344
'SET'=345
'SETS'=346
'SET_VAR'=347
'SIGNED'=348
'SKIP_HEADER'=349
'SHOW'=350
'SMALLINT'=351
'SNAPSHOT'=352
'SQLBLACKLIST'=353
'START'=354
'STATS'=355
'STATUS'=356
'STOP'=357
'STORAGE'=358
'STREAM'=359
'STRING'=360
'TEXT'=361
'SUBMIT'=362
'SUM'=363
'SUSPEND'=364
'SYNC'=365
'SYSTEM'=366
'SYSTEM_TIME'=367
'SWAP'=368
'STRUCT'=369
'TABLE'=370
'TABLES'=371
'TABLET'=372
'TASK'=373
'TEMPORARY'=374
'TERMINATED'=375
'THAN'=376
'THEN'=377
'TIME'=378
'TIMESTAMP'=379
'TIMESTAMPADD'=380
'TIMESTAMPDIFF'=381
'TINYINT'=382
'TRANSACTION'=383
'TO'=384
'TRACE'=385
'TRIGGERS'=386
'TRIM_SPACE'=387
'TRUE'=388
'TRUNCATE'=389
'TYPE'=390
'TYPES'=391
'UNBOUNDED'=392
'UNCOMMITTED'=393
'UNION'=394
'UNIQUE'=395
'UNINSTALL'=396
'UNSET'=397
'UNSIGNED'=398
'UPDATE'=399
'USAGE'=400
'USE'=401
'USER'=402
'USERS'=403
'USING'=404
'VALUE'=405
'VALUES'=406
'VARBINARY'=407
'VARCHAR'=408
'VARIABLES'=409
'VERBOSE'=410
'VIEW'=411
'VIEWS'=412
'VOLUME'=413
'VOLUMES'=414
'WAREHOUSE'=415
'WAREHOUSES'=416
'WARNINGS'=417
'WEEK'=418
'WHEN'=419
'WHERE'=420
'WHITELIST'=421
'WITH'=422
'WORK'=423
'WRITE'=424
'YEAR'=425
'LOCK'=426
'UNLOCK'=427
'LOW_PRIORITY'=428
'='=429
'<'=431
'<='=432
'>'=433
'>='=434
'<=>'=435
'+'=436
'-'=437
'*'=438
'/'=439
'%'=440
'||'=441
'&&'=442
'!'=443
'DIV'=444
'&'=445
'|'=446
'^'=447
'~'=448
'BITSHIFTLEFT'=449
'BITSHIFTRIGHT'=450
'BITSHIFTRIGHTLOGICAL'=451
'->'=452
'@'=453
';'=467
'...'=468
