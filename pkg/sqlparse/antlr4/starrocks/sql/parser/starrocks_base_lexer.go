package parser

import (
	"github.com/antlr4-go/antlr/v4"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
)

// NewCustomStarRocksLexer
func NewCustomStarRocksLexer(input antlr.CharStream) *StarRocksLexer {
	StarRocksLexerInit()
	l := new(StarRocksLexer)
	l.BaseLexer = antlr.NewBaseLexer(input)
	staticData := &StarRocksLexerLexerStaticData
	l.Interpreter = antlr.NewLexerATNSimulator(l, staticData.atn, staticData.decisionToDFA, staticData.PredictionContextCache)
	if !global.AppConfig.DmpApi.EnableAntlr4Cache {
		// fix: https://github.com/antlr/antlr4/issues/499
		// 处理长时间不同sql解析后的内存泄漏问题
		dfa := lo.Map(l.GetInterpreter().DecisionToDFA(), func(item *antlr.DFA, idx int) *antlr.DFA {
			return antlr.NewDFA(l.GetATN().DecisionToState[idx], idx)
		})
		l.Interpreter = antlr.NewLexerATNSimulator(l, l.GetATN(), dfa, antlr.NewPredictionContextCache())
	}
	l.channelNames = staticData.ChannelNames
	l.modeNames = staticData.ModeNames
	l.RuleNames = staticData.RuleNames
	l.LiteralNames = staticData.LiteralNames
	l.SymbolicNames = staticData.SymbolicNames
	l.GrammarFileName = "StarRocks.g4"
	// TODO: l.EOF = antlr.TokenEOF

	return l
}
