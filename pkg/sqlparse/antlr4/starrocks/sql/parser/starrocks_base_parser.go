package parser

import (
	"github.com/antlr4-go/antlr/v4"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
)

// NewCustomStarRocksParser
func NewCustomStarRocksParser(input antlr.TokenStream) *StarRocksParser {
	StarRocksParserInit()
	this := new(StarRocksParser)
	this.BaseParser = antlr.NewBaseParser(input)
	staticData := &StarRocksParserStaticData
	this.Interpreter = antlr.NewParserATNSimulator(this, staticData.atn, staticData.decisionToDFA, staticData.PredictionContextCache)
	if !global.AppConfig.DmpApi.EnableAntlr4Cache {
		// fix: https://github.com/antlr/antlr4/issues/499
		// 处理长时间不同sql解析后的内存泄漏问题
		dfa := lo.Map(this.GetInterpreter().DecisionToDFA(), func(item *antlr.DFA, idx int) *antlr.DFA {
			return antlr.NewDFA(this.GetATN().DecisionToState[idx], idx)
		})
		this.Interpreter = antlr.NewParserATNSimulator(this, this.GetATN(), dfa, antlr.NewPredictionContextCache())
	}
	this.RuleNames = staticData.RuleNames
	this.LiteralNames = staticData.LiteralNames
	this.SymbolicNames = staticData.SymbolicNames
	this.GrammarFileName = "StarRocks.g4"

	return this
}
