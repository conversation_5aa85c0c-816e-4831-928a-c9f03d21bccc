CONCAT=1
ACCESS=2
ACTIVE=3
ADD=4
ADMIN=5
AFTER=6
AGGREGATE=7
ALL=8
ALTER=9
ANALYZE=10
AND=11
ANTI=12
APPLY=13
ARRAY=14
ARRAY_AGG=15
AS=16
ASC=17
ASYNC=18
AUTHORS=19
AUTHENTICATION=20
AUTO_INCREMENT=21
AVG=22
BACKEND=23
BACKENDS=24
BACKUP=25
BASE=26
BEGIN=27
BETWEEN=28
BIGINT=29
BINARY=30
BITMAP=31
BITMAP_UNION=32
BLACKLIST=33
BODY=34
BOOLEAN=35
BOTH=36
BROKER=37
BUCKETS=38
BUILTIN=39
BY=40
CANCEL=41
CASE=42
CAST=43
CATALOG=44
CATALOGS=45
CEIL=46
CHAIN=47
CHAR=48
CHARACTER=49
CHARSET=50
CHECK=51
CLEAN=52
CLUSTER=53
CLUSTERS=54
COLLATE=55
COLLATION=56
COLUMN=57
COLUMNS=58
COMMENT=59
COMMIT=60
COMMITTED=61
COMPACT=62
COMPACTION=63
COMPUTE=64
CONFIG=65
CONNECTION=66
CONSISTENT=67
CONVERT=68
COSTS=69
COUNT=70
CREATE=71
CROSS=72
CUBE=73
CUME_DIST=74
CUMULATIVE=75
CURRENT=76
CURRENT_DATE=77
CURRENT_ROLE=78
CURRENT_TIME=79
CURRENT_TIMESTAMP=80
CURRENT_USER=81
DATA=82
DATABASE=83
DATABASES=84
DATE=85
DATETIME=86
DAY=87
DECIMAL=88
DECIMALV2=89
DECIMAL32=90
DECIMAL64=91
DECIMAL128=92
DECOMMISSION=93
DEFAULT=94
DELETE=95
DENSE_RANK=96
DEFERRED=97
NTILE=98
DESC=99
DESCRIBE=100
DISTINCT=101
DISTRIBUTED=102
DISTRIBUTION=103
DOUBLE=104
DROP=105
DUAL=106
DUPLICATE=107
DYNAMIC=108
ELSE=109
ENCLOSE=110
END=111
ENGINE=112
ENGINES=113
ERRORS=114
ESCAPE=115
EVENTS=116
EXCEPT=117
EXECUTE=118
EXISTS=119
EXPLAIN=120
EXPORT=121
EXTERNAL=122
EXTRACT=123
EVERY=124
FALSE=125
FIELDS=126
FILE=127
FILES=128
FILTER=129
FIRST=130
FIRST_VALUE=131
FLOAT=132
FLOOR=133
FN=134
FOLLOWING=135
FOLLOWER=136
FOR=137
FORCE=138
FORMAT=139
FREE=140
FROM=141
FRONTEND=142
FRONTENDS=143
FULL=144
FUNCTION=145
FUNCTIONS=146
GLOBAL=147
GRANT=148
GRANTS=149
GROUP=150
GROUPS=151
GROUPING=152
GROUPING_ID=153
GROUP_CONCAT=154
HASH=155
HAVING=156
HELP=157
HISTOGRAM=158
HLL=159
HLL_UNION=160
HOST=161
HOUR=162
HUB=163
IDENTIFIED=164
IF=165
IMPERSONATE=166
IMMEDIATE=167
IGNORE=168
IMAGE=169
IN=170
INACTIVE=171
INCREMENTAL=172
INDEX=173
INDEXES=174
INFILE=175
INNER=176
INSTALL=177
INSERT=178
INT=179
INTEGER=180
INTEGRATION=181
INTEGRATIONS=182
INTERMEDIATE=183
INTERSECT=184
INTERVAL=185
INTO=186
OVERWRITE=187
IS=188
ISOLATION=189
JOB=190
JOIN=191
JSON=192
KEY=193
KEYS=194
KILL=195
LABEL=196
LAG=197
LARGEINT=198
LAST=199
LAST_VALUE=200
LATERAL=201
LEAD=202
LEFT=203
LESS=204
LEVEL=205
LIKE=206
LIMIT=207
LIST=208
LOAD=209
LOCAL=210
LOCALTIME=211
LOCALTIMESTAMP=212
LOCATION=213
LOCATIONS=214
LOGICAL=215
MANUAL=216
MAP=217
MAPPING=218
MAPPINGS=219
MASKING=220
MATERIALIZED=221
MAX=222
MAXVALUE=223
MERGE=224
MIN=225
MINUTE=226
MINUS=227
META=228
MOD=229
MODE=230
MODIFY=231
MONTH=232
NAME=233
NAMES=234
NEGATIVE=235
NO=236
NODE=237
NODES=238
NONE=239
NOT=240
NULL=241
NULLS=242
NUMBER=243
NUMERIC=244
OBSERVER=245
OF=246
OFFSET=247
ON=248
ONLY=249
OPEN=250
OPERATE=251
OPTIMIZER=252
OPTION=253
OR=254
ORDER=255
OUTER=256
OUTFILE=257
OVER=258
PARTITION=259
PARTITIONS=260
PASSWORD=261
PATH=262
PAUSE=263
PENDING=264
PERCENT_RANK=265
PERCENTILE=266
PERCENTILE_UNION=267
PLUGIN=268
PLUGINS=269
POLICY=270
POLICIES=271
PRECEDING=272
PRIMARY=273
PRIVILEGES=274
PROC=275
PROCEDURE=276
PROCESSLIST=277
PROFILE=278
PROFILELIST=279
PROPERTIES=280
PROPERTY=281
QUALIFY=282
QUARTER=283
QUERY=284
QUERIES=285
QUEUE=286
QUOTA=287
RANDOM=288
RANGE=289
RANK=290
READ=291
RECOVER=292
REFRESH=293
REWRITE=294
REGEXP=295
RELEASE=296
REMOVE=297
RENAME=298
REPAIR=299
REPEATABLE=300
REPLACE=301
REPLACE_IF_NOT_NULL=302
REPLICA=303
REPOSITORY=304
REPOSITORIES=305
RESOURCE=306
RESOURCES=307
RESTORE=308
RESUME=309
RETURNS=310
REVOKE=311
REVERT=312
RIGHT=313
RLIKE=314
ROLE=315
ROLES=316
ROLLBACK=317
ROLLUP=318
ROUTINE=319
ROW=320
ROWS=321
ROW_NUMBER=322
RUNNING=323
SAMPLE=324
SCHEDULER=325
SCHEMA=326
SCHEMAS=327
SECOND=328
SECURITY=329
SELECT=330
SEMI=331
SEPARATOR=332
SERIALIZABLE=333
SESSION=334
SET=335
SETS=336
SET_VAR=337
SIGNED=338
SKIP_HEADER=339
SHOW=340
SMALLINT=341
SNAPSHOT=342
SQLBLACKLIST=343
START=344
STATS=345
STATUS=346
STOP=347
STORAGE=348
STREAM=349
STRING=350
TEXT=351
SUBMIT=352
SUM=353
SUSPEND=354
SYNC=355
SYSTEM=356
SYSTEM_TIME=357
SWAP=358
STRUCT=359
TABLE=360
TABLES=361
TABLET=362
TASK=363
TEMPORARY=364
TERMINATED=365
THAN=366
THEN=367
TIME=368
TIMESTAMP=369
TIMESTAMPADD=370
TIMESTAMPDIFF=371
TINYINT=372
TRANSACTION=373
TO=374
TRACE=375
TRIGGERS=376
TRIM_SPACE=377
TRUE=378
TRUNCATE=379
TYPE=380
TYPES=381
UNBOUNDED=382
UNCOMMITTED=383
UNION=384
UNIQUE=385
UNINSTALL=386
UNSET=387
UNSIGNED=388
UPDATE=389
USAGE=390
USE=391
USER=392
USERS=393
USING=394
VALUE=395
VALUES=396
VARBINARY=397
VARCHAR=398
VARIABLES=399
VERBOSE=400
VIEW=401
VIEWS=402
VOLUME=403
VOLUMES=404
WAREHOUSE=405
WAREHOUSES=406
WARNINGS=407
WEEK=408
WHEN=409
WHERE=410
WHITELIST=411
WITH=412
WORK=413
WRITE=414
YEAR=415
LOCK=416
UNLOCK=417
LOW_PRIORITY=418
EQ=419
NEQ=420
LT=421
LTE=422
GT=423
GTE=424
EQ_FOR_NULL=425
PLUS_SYMBOL=426
MINUS_SYMBOL=427
ASTERISK_SYMBOL=428
SLASH_SYMBOL=429
PERCENT_SYMBOL=430
LOGICAL_OR=431
LOGICAL_AND=432
LOGICAL_NOT=433
INT_DIV=434
BITAND=435
BITOR=436
BITXOR=437
BITNOT=438
BIT_SHIFT_LEFT=439
BIT_SHIFT_RIGHT=440
BIT_SHIFT_RIGHT_LOGICAL=441
ARROW=442
AT=443
INTEGER_VALUE=444
DECIMAL_VALUE=445
DOUBLE_VALUE=446
SINGLE_QUOTED_TEXT=447
DOUBLE_QUOTED_TEXT=448
BINARY_SINGLE_QUOTED_TEXT=449
BINARY_DOUBLE_QUOTED_TEXT=450
LETTER_IDENTIFIER=451
DIGIT_IDENTIFIER=452
BACKQUOTED_IDENTIFIER=453
DOT_IDENTIFIER=454
SIMPLE_COMMENT=455
BRACKETED_COMMENT=456
SEMICOLON=457
DOTDOTDOT=458
WS=459
'ACCESS'=2
'ACTIVE'=3
'ADD'=4
'ADMIN'=5
'AFTER'=6
'AGGREGATE'=7
'ALL'=8
'ALTER'=9
'ANALYZE'=10
'AND'=11
'ANTI'=12
'APPLY'=13
'ARRAY'=14
'ARRAY_AGG'=15
'AS'=16
'ASC'=17
'ASYNC'=18
'AUTHORS'=19
'AUTHENTICATION'=20
'AUTO_INCREMENT'=21
'AVG'=22
'BACKEND'=23
'BACKENDS'=24
'BACKUP'=25
'BASE'=26
'BEGIN'=27
'BETWEEN'=28
'BIGINT'=29
'BINARY'=30
'BITMAP'=31
'BITMAP_UNION'=32
'BLACKLIST'=33
'BODY'=34
'BOOLEAN'=35
'BOTH'=36
'BROKER'=37
'BUCKETS'=38
'BUILTIN'=39
'BY'=40
'CANCEL'=41
'CASE'=42
'CAST'=43
'CATALOG'=44
'CATALOGS'=45
'CEIL'=46
'CHAIN'=47
'CHAR'=48
'CHARACTER'=49
'CHARSET'=50
'CHECK'=51
'CLEAN'=52
'CLUSTER'=53
'CLUSTERS'=54
'COLLATE'=55
'COLLATION'=56
'COLUMN'=57
'COLUMNS'=58
'COMMENT'=59
'COMMIT'=60
'COMMITTED'=61
'COMPACT'=62
'COMPACTION'=63
'COMPUTE'=64
'CONFIG'=65
'CONNECTION'=66
'CONSISTENT'=67
'CONVERT'=68
'COSTS'=69
'COUNT'=70
'CREATE'=71
'CROSS'=72
'CUBE'=73
'CUME_DIST'=74
'CUMULATIVE'=75
'CURRENT'=76
'CURRENT_DATE'=77
'CURRENT_ROLE'=78
'CURRENT_TIME'=79
'CURRENT_TIMESTAMP'=80
'CURRENT_USER'=81
'DATA'=82
'DATABASE'=83
'DATABASES'=84
'DATE'=85
'DATETIME'=86
'DAY'=87
'DECIMAL'=88
'DECIMALV2'=89
'DECIMAL32'=90
'DECIMAL64'=91
'DECIMAL128'=92
'DECOMMISSION'=93
'DEFAULT'=94
'DELETE'=95
'DENSE_RANK'=96
'DEFERRED'=97
'NTILE'=98
'DESC'=99
'DESCRIBE'=100
'DISTINCT'=101
'DISTRIBUTED'=102
'DISTRIBUTION'=103
'DOUBLE'=104
'DROP'=105
'DUAL'=106
'DUPLICATE'=107
'DYNAMIC'=108
'ELSE'=109
'ENCLOSE'=110
'END'=111
'ENGINE'=112
'ENGINES'=113
'ERRORS'=114
'ESCAPE'=115
'EVENTS'=116
'EXCEPT'=117
'EXECUTE'=118
'EXISTS'=119
'EXPLAIN'=120
'EXPORT'=121
'EXTERNAL'=122
'EXTRACT'=123
'EVERY'=124
'FALSE'=125
'FIELDS'=126
'FILE'=127
'FILES'=128
'FILTER'=129
'FIRST'=130
'FIRST_VALUE'=131
'FLOAT'=132
'FLOOR'=133
'FN'=134
'FOLLOWING'=135
'FOLLOWER'=136
'FOR'=137
'FORCE'=138
'FORMAT'=139
'FREE'=140
'FROM'=141
'FRONTEND'=142
'FRONTENDS'=143
'FULL'=144
'FUNCTION'=145
'FUNCTIONS'=146
'GLOBAL'=147
'GRANT'=148
'GRANTS'=149
'GROUP'=150
'GROUPS'=151
'GROUPING'=152
'GROUPING_ID'=153
'GROUP_CONCAT'=154
'HASH'=155
'HAVING'=156
'HELP'=157
'HISTOGRAM'=158
'HLL'=159
'HLL_UNION'=160
'HOST'=161
'HOUR'=162
'HUB'=163
'IDENTIFIED'=164
'IF'=165
'IMPERSONATE'=166
'IMMEDIATE'=167
'IGNORE'=168
'IMAGE'=169
'IN'=170
'INACTIVE'=171
'INCREMENTAL'=172
'INDEX'=173
'INDEXES'=174
'INFILE'=175
'INNER'=176
'INSTALL'=177
'INSERT'=178
'INT'=179
'INTEGER'=180
'INTEGRATION'=181
'INTEGRATIONS'=182
'INTERMEDIATE'=183
'INTERSECT'=184
'INTERVAL'=185
'INTO'=186
'OVERWRITE'=187
'IS'=188
'ISOLATION'=189
'JOB'=190
'JOIN'=191
'JSON'=192
'KEY'=193
'KEYS'=194
'KILL'=195
'LABEL'=196
'LAG'=197
'LARGEINT'=198
'LAST'=199
'LAST_VALUE'=200
'LATERAL'=201
'LEAD'=202
'LEFT'=203
'LESS'=204
'LEVEL'=205
'LIKE'=206
'LIMIT'=207
'LIST'=208
'LOAD'=209
'LOCAL'=210
'LOCALTIME'=211
'LOCALTIMESTAMP'=212
'LOCATION'=213
'LOCATIONS'=214
'LOGICAL'=215
'MANUAL'=216
'MAP'=217
'MAPPING'=218
'MAPPINGS'=219
'MASKING'=220
'MATERIALIZED'=221
'MAX'=222
'MAXVALUE'=223
'MERGE'=224
'MIN'=225
'MINUTE'=226
'MINUS'=227
'META'=228
'MOD'=229
'MODE'=230
'MODIFY'=231
'MONTH'=232
'NAME'=233
'NAMES'=234
'NEGATIVE'=235
'NO'=236
'NODE'=237
'NODES'=238
'NONE'=239
'NOT'=240
'NULL'=241
'NULLS'=242
'NUMBER'=243
'NUMERIC'=244
'OBSERVER'=245
'OF'=246
'OFFSET'=247
'ON'=248
'ONLY'=249
'OPEN'=250
'OPERATE'=251
'OPTIMIZER'=252
'OPTION'=253
'OR'=254
'ORDER'=255
'OUTER'=256
'OUTFILE'=257
'OVER'=258
'PARTITION'=259
'PARTITIONS'=260
'PASSWORD'=261
'PATH'=262
'PAUSE'=263
'PENDING'=264
'PERCENT_RANK'=265
'PERCENTILE'=266
'PERCENTILE_UNION'=267
'PLUGIN'=268
'PLUGINS'=269
'POLICY'=270
'POLICIES'=271
'PRECEDING'=272
'PRIMARY'=273
'PRIVILEGES'=274
'PROC'=275
'PROCEDURE'=276
'PROCESSLIST'=277
'PROFILE'=278
'PROFILELIST'=279
'PROPERTIES'=280
'PROPERTY'=281
'QUALIFY'=282
'QUARTER'=283
'QUERY'=284
'QUERIES'=285
'QUEUE'=286
'QUOTA'=287
'RANDOM'=288
'RANGE'=289
'RANK'=290
'READ'=291
'RECOVER'=292
'REFRESH'=293
'REWRITE'=294
'REGEXP'=295
'RELEASE'=296
'REMOVE'=297
'RENAME'=298
'REPAIR'=299
'REPEATABLE'=300
'REPLACE'=301
'REPLACE_IF_NOT_NULL'=302
'REPLICA'=303
'REPOSITORY'=304
'REPOSITORIES'=305
'RESOURCE'=306
'RESOURCES'=307
'RESTORE'=308
'RESUME'=309
'RETURNS'=310
'REVOKE'=311
'REVERT'=312
'RIGHT'=313
'RLIKE'=314
'ROLE'=315
'ROLES'=316
'ROLLBACK'=317
'ROLLUP'=318
'ROUTINE'=319
'ROW'=320
'ROWS'=321
'ROW_NUMBER'=322
'RUNNING'=323
'SAMPLE'=324
'SCHEDULER'=325
'SCHEMA'=326
'SCHEMAS'=327
'SECOND'=328
'SECURITY'=329
'SELECT'=330
'SEMI'=331
'SEPARATOR'=332
'SERIALIZABLE'=333
'SESSION'=334
'SET'=335
'SETS'=336
'SET_VAR'=337
'SIGNED'=338
'SKIP_HEADER'=339
'SHOW'=340
'SMALLINT'=341
'SNAPSHOT'=342
'SQLBLACKLIST'=343
'START'=344
'STATS'=345
'STATUS'=346
'STOP'=347
'STORAGE'=348
'STREAM'=349
'STRING'=350
'TEXT'=351
'SUBMIT'=352
'SUM'=353
'SUSPEND'=354
'SYNC'=355
'SYSTEM'=356
'SYSTEM_TIME'=357
'SWAP'=358
'STRUCT'=359
'TABLE'=360
'TABLES'=361
'TABLET'=362
'TASK'=363
'TEMPORARY'=364
'TERMINATED'=365
'THAN'=366
'THEN'=367
'TIME'=368
'TIMESTAMP'=369
'TIMESTAMPADD'=370
'TIMESTAMPDIFF'=371
'TINYINT'=372
'TRANSACTION'=373
'TO'=374
'TRACE'=375
'TRIGGERS'=376
'TRIM_SPACE'=377
'TRUE'=378
'TRUNCATE'=379
'TYPE'=380
'TYPES'=381
'UNBOUNDED'=382
'UNCOMMITTED'=383
'UNION'=384
'UNIQUE'=385
'UNINSTALL'=386
'UNSET'=387
'UNSIGNED'=388
'UPDATE'=389
'USAGE'=390
'USE'=391
'USER'=392
'USERS'=393
'USING'=394
'VALUE'=395
'VALUES'=396
'VARBINARY'=397
'VARCHAR'=398
'VARIABLES'=399
'VERBOSE'=400
'VIEW'=401
'VIEWS'=402
'VOLUME'=403
'VOLUMES'=404
'WAREHOUSE'=405
'WAREHOUSES'=406
'WARNINGS'=407
'WEEK'=408
'WHEN'=409
'WHERE'=410
'WHITELIST'=411
'WITH'=412
'WORK'=413
'WRITE'=414
'YEAR'=415
'LOCK'=416
'UNLOCK'=417
'LOW_PRIORITY'=418
'='=419
'<'=421
'<='=422
'>'=423
'>='=424
'<=>'=425
'+'=426
'-'=427
'*'=428
'/'=429
'%'=430
'||'=431
'&&'=432
'!'=433
'DIV'=434
'&'=435
'|'=436
'^'=437
'~'=438
'BITSHIFTLEFT'=439
'BITSHIFTRIGHT'=440
'BITSHIFTRIGHTLOGICAL'=441
'->'=442
'@'=443
';'=457
'...'=458
