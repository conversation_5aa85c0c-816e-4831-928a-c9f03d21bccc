package controllers

import (
	"github.com/gin-gonic/gin"

	modelAggProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/model_agg/proto"
	modelAgg "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/model_agg/services"
)

func (ac *Controller) ListAllModelBriefs(c *gin.Context) {
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}

	req := modelAggProto.ListModelBriefReq{}
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}

	var modelAggService modelAgg.ModelAggService
	ac.container.MustExtract(&modelAggService)
	rsp, err := modelAggService.ListAllModelBriefs(c.Request.Context(), sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, rsp)
}

func (ac *Controller) AllModelBriefTree(c *gin.Context) {
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var req modelAggProto.QueryModelBriefTreeReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}

	var modelAggService modelAgg.ModelAggService
	ac.container.MustExtract(&modelAggService)
	rsp, err := modelAggService.QueryModelBriefTree(c.Request.Context(), sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, rsp)
}

func (ac *Controller) GetModelRelationMap(c *gin.Context) {
	req := modelAggProto.GetModelRelationMapReq{}
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var modelAggService modelAgg.ModelAggService
	ac.container.MustExtract(&modelAggService)
	rsp, err := modelAggService.GetModelRelationMap(c.Request.Context(), sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, rsp)
}

func (ac *Controller) ListAllModels(c *gin.Context) {
	req := modelAggProto.ListAllModelsReq{}
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var modelAggService modelAgg.ModelAggService
	ac.container.MustExtract(&modelAggService)
	rsp, err := modelAggService.ListAllModels(c.Request.Context(), sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, rsp)
}
