package controllers

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/dep_injection"
)

type Controller struct {
	util      *utils.Utility
	container dep_injection.Container
}

func NewController(container dep_injection.Container) (*Controller, error) {
	ctrl := &Controller{
		util:      &utils.Utility{},
		container: container,
	}
	return ctrl, nil
}
