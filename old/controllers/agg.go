package controllers

import (
	"github.com/gin-gonic/gin"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/agg/services"
)

func (ac *Controller) GetAssetStatistics(c *gin.Context) {
	var aggService *services.AggService
	err := ac.container.Extract(&aggService)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	rsp, err := aggService.GetMainPageAssetStatistics(c, sess.Code)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, rsp)
	return
}