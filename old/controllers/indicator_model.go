package controllers

import (
	"github.com/gin-gonic/gin"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/indicator_view/services"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic"
	modeling "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
)

// 同步预览汇总视图数据
func (ac *Controller) PreviewIndicatorModel(c *gin.Context) {
	req := proto.AsyncRunSQL{Env: cBase.ProjectEnvProd, IsNotRun: true}
	err := ac.util.ConvertBody(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}

	for i, _ := range req.ViewContent.CustomFieldDimensions {
		req.ViewContent.CustomFieldDimensions[i].Alias = req.ViewContent.CustomFieldDimensions[i].NameCn
	}

	var logicService *logic.ModelLogic
	ac.container.MustExtract(&logicService)
	var executeSQL string
	_, executeSQL, err = logicService.GenRunSql(c.Request.Context(), sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}

	var indicatorService services.IndicatorService
	err = ac.container.Extract(&indicatorService)
	if !ac.util.CheckError(c, err) {
		return
	}
	rsp, err := logicService.PreviewIndicatorModel(c.Request.Context(), sess.Code, req.TenantCode, executeSQL)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, rsp)
}

// AdsAsyncRunSQL: 应用表测试预览
func (ac *Controller) AdsAsyncRunSQL(c *gin.Context) {
	req := proto.AsyncRunSQL{Env: cBase.ProjectEnvDev}
	err := ac.util.ConvertBody(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}

	var logicService *logic.ModelLogic
	ac.container.MustExtract(&logicService)
	resp, err := logicService.AdsAsyncRunSQL(c.Request.Context(), sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// GetAdsAsyncRunSQLResult: 应用表获测试预览执行结果
func (ac *Controller) GetAdsAsyncRunSQLResult(c *gin.Context) {
	req := proto.AsyncRunSQLResult{
		Env: cBase.ProjectEnvDev,
	}
	err := ac.util.ConvertBody(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var logicService *logic.ModelLogic
	err = ac.container.Extract(&logicService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := logicService.GetAdsAsyncRunSQLResult(c.Request.Context(), sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// 获取内置函数
func (ac *Controller) GetBuiltinFunction(c *gin.Context) {
	_, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var modelService *modeling.ModelService
	err := ac.container.Extract(&modelService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := modelService.GetBuiltinFunctions()
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}


func (ac *Controller) ValidateIndicatorExpr(c *gin.Context) {
	var req proto.ValidateIndicatorExprReq
	err := ac.util.ConvertBody(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var indicatorViewService services.IndicatorService
	ac.container.MustExtract(&indicatorViewService)
	rsp, err := indicatorViewService.ValidateIndicatorExpr(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, rsp)
}

func (ac *Controller) GetAvailableDimAndFilters(c *gin.Context) {
	var req proto.GetAvailableDimAndFiltersReq
	err := ac.util.ConvertBody(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var indicatorViewService services.IndicatorService
	ac.container.MustExtract(&indicatorViewService)
	rsp, err := indicatorViewService.GetAvailableDimAndFiltersV2(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, rsp)
}

func (ac *Controller) GetIndicatorAvailableFilters(c *gin.Context) {
	var req proto.GetIndicatorAvailableFiltersReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var indicatorViewService services.IndicatorService
	ac.container.MustExtract(&indicatorViewService)
	rsp, err := indicatorViewService.GetIndicatorAvailableFilters(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, rsp)
}

func (ac *Controller) GetPublicDimension(c *gin.Context) {
	var req modelProto.GetPublicDimensionReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}

	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}

	var indicatorViewService services.IndicatorService
	err = ac.container.Extract(&indicatorViewService)
	if !ac.util.CheckError(c, err) {
		return
	}

	rsp, err := indicatorViewService.GetPublicAvailableDimAndFilters(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}

	ac.util.SendSuccess(c, rsp)
}
