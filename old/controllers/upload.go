package controllers

import (
	"github.com/gin-gonic/gin"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/upload/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/upload/services"
)

// 获取oss对象存储上传的url
func (ac *Controller) GenPreSignedPostObjectURL(c *gin.Context) {
	req := proto.GenPreSignedPutObjectURLReq{}
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}

	var uploadService *services.UploadService
	err = ac.container.Extract(&uploadService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := uploadService.GenPreSignedPutObjectURL(req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// 下载oss文件
func (ac *Controller) GetPreSignedGetObjectURL(c *gin.Context) {
	req := proto.GenPreSignedGetObjectURLReq{}
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}

	var uploadService *services.UploadService
	err = ac.container.Extract(&uploadService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := uploadService.GenPreSignedGetObjectURL(req)
	if !ac.util.CheckError(c, err) {
		return
	}

	c.Redirect(302, resp.SignedUrl)
}
