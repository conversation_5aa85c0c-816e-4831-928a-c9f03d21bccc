package controllers

import (
	"github.com/gin-gonic/gin"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/services"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	baseProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/server/dmp_server"
)

func (ac *Controller) UpgradeApiRelease(c *gin.Context) {
	var req proto.UpgradeApiReleaseReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}

	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = apiManageService.ApiReleaseByProjects(c, req.Projects)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

// GetParamMapByEngine 数据库类型和api参数的类型对应关系
func (ac *Controller) GetParamMapByEngine(c *gin.Context) {
	var req proto.GetParamMapReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	_, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := apiManageService.GetParamMapByEngine(c, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// GetAuthApiList 获取应用授权的Api列表
func (ac *Controller) GetAuthApiList(c *gin.Context) {
	req := proto.ApplicationAuthApiReq{QueryBaseRequest: base.QueryBaseDefaultRequest()}
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	req.ProjectCode = sess.Code
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := apiManageService.GetApplicationAuthApiList(c, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// UnbindAuthApi 应用解绑已授权Api
func (ac *Controller) UnbindAuthApi(c *gin.Context) {
	var req proto.UnbindAuthApiReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = apiManageService.UnbindAuthApi(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

// GetApiList 获取Api列表
func (ac *Controller) GetApiList(c *gin.Context) {
	req := proto.ApiListReq{QueryBaseRequest: base.QueryBaseDefaultRequest()}
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}

	CusResp, err := apiManageService.GetApiList(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}

	ac.util.SendSuccess(c, CusResp)
}

func (ac *Controller) GetApiGroupTree(c *gin.Context) {
	req := proto.ApiListReq{QueryBaseRequest: base.QueryBaseNoPageRequest()}
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}

	CusResp, err := apiManageService.GetApiGroupTree(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}

	ac.util.SendSuccess(c, CusResp)
}

// ApiAdd 新增Api
func (ac *Controller) ApiAdd(c *gin.Context) {
	var req proto.ApiAddReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := apiManageService.ApiAdd(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// ApiDelete 删除Api
func (ac *Controller) ApiDelete(c *gin.Context) {
	var req proto.ApiDeleteReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = apiManageService.ApiDelete(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

// ApiDetail 获取Api详情
func (ac *Controller) ApiDetail(c *gin.Context) {
	var req proto.ApiDetailReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := apiManageService.ApiDetail(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// ApiReleaseDetail 获取Api发布后详情
func (ac *Controller) ApiReleaseDetail(c *gin.Context) {
	var req proto.ApiDetailReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := apiManageService.ApiReleaseDetail(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// ApiUpdate 更新Api
func (ac *Controller) ApiUpdate(c *gin.Context) {
	var req proto.ApiUpdateReq
	err := ac.util.ConvertBody(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := apiManageService.ApiUpdate(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// ApiTestRun Api测试运行
func (ac *Controller) ApiTestRun(c *gin.Context) {
	var req proto.ApiTestRunReq
	err := ac.util.ConvertBody(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := apiManageService.ApiTestRun(c.Request.Context(), c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// ApiRollback 回滚api
func (ac *Controller) ApiRollback(c *gin.Context) {
	var req proto.ApiRollbackReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := apiManageService.ApiRollback(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// ApiRelease 发布Api
func (ac *Controller) ApiRelease(c *gin.Context) {
	var req proto.ApiReleaseReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := apiManageService.ApiRelease(c, sess.Code, sess.Account, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// ApiOffline 下线Api
func (ac *Controller) ApiOffline(c *gin.Context) {
	var req proto.ApiOfflineReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = apiManageService.ApiOffline(c, sess.Code, sess.Account, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

// ApiVersionList 获取Api版本信息
func (ac *Controller) ApiVersionList(c *gin.Context) {
	apiVersionRequest := proto.ApiVersionReq{QueryBaseRequest: base.QueryBaseDefaultRequest()}
	err := ac.util.Bind(c, &apiVersionRequest)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := apiManageService.ApiVersionList(c, sess.Code, apiVersionRequest)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)

}

// ApiUnbind 解绑应用
func (ac *Controller) ApiTestResult(c *gin.Context) {
	var req proto.ApiTestResultReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = apiManageService.ApiTestResult(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

// GetGroupApiList 获取分组下的树形api列表
func (ac *Controller) GetGroupApiListWithApis(c *gin.Context) {
	var req proto.ApiGroupList
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiGroupService *services.ApiGroupService
	err = ac.container.Extract(&apiGroupService)
	if !ac.util.CheckError(c, err) {
		return
	}

	resp := make([]*proto.GroupListResp, 0)
	customizeGroupResp, err := apiGroupService.GetGroupApiListWithApis(c, sess.Code, req.SubjectId)
	if !ac.util.CheckError(c, err) {
		return
	}
	if len(customizeGroupResp) != 0 {
		resp = append(resp, customizeGroupResp...)
	}

	ac.util.SendSuccess(c, resp)
}

// 获取api列表-对外
func (ac *Controller) GetApiListWithOpenApi(c *gin.Context) {
	var req proto.ApiListWithOpenApiReq
	err := ac.util.Bind(c, &req)
	if !dmp_server.CheckError(c, err) {
		return
	}

	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !dmp_server.CheckError(c, err) {
		return
	}

	resp, err := apiManageService.GetApiListWithOpenApi(c, req)
	if !dmp_server.CheckError(c, err) {
		return
	}
	dmp_server.SendSuccess(c, resp)
}

// AddGroup 新增分组
func (ac *Controller) AddGroup(c *gin.Context) {
	var req proto.GroupAddReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiGroupService *services.ApiGroupService
	err = ac.container.Extract(&apiGroupService)
	if !ac.util.CheckError(c, err) {
		return
	}
	groupID, err := apiGroupService.AddGroup(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, baseProto.PrimaryKey{ID: groupID})
}

// UpdateGroup 更新分组
func (ac *Controller) UpdateGroup(c *gin.Context) {
	var req proto.GroupUpdateReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiGroupService *services.ApiGroupService
	err = ac.container.Extract(&apiGroupService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = apiGroupService.UpdateGroup(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

// DeleteGroup 删除分组
func (ac *Controller) DeleteGroup(c *gin.Context) {
	var req baseProto.PrimaryKey
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiGroupService *services.ApiGroupService
	err = ac.container.Extract(&apiGroupService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = apiGroupService.DeleteGroup(c, sess.Code, req.ID)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, baseProto.PrimaryKey{ID: req.ID})
}

// GetGroupList 获取分组列表
func (ac *Controller) GetGroupList(c *gin.Context) {
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiGroupService *services.ApiGroupService
	err := ac.container.Extract(&apiGroupService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := apiGroupService.GetGroupList(c, sess.Code)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

// ApiAuthorize 授权应用
func (ac *Controller) ApiAuthorize(c *gin.Context) {
	var req proto.ApiAuthorizeReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = apiManageService.ApiAuthorize(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

// ApiUnbind 解绑应用
func (ac *Controller) ApiUnbind(c *gin.Context) {
	var req proto.ApiUnbindReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = apiManageService.ApiUnbind(c, sess.Code, req, true, nil)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

// ApiUpsetLimit 配置接口限流
func (ac *Controller) ApiUpsetLimit(c *gin.Context) {
	var req proto.ApiUpSetLimitReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var apiManageService *services.ApiManageService
	err = ac.container.Extract(&apiManageService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = apiManageService.ApiUpsetLimit(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

func (ac *Controller) GenAppJwtToken(c *gin.Context) {
	var req proto.GenAppJwtTokenReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var applicationService *services.ApiManageService
	err = ac.container.Extract(&applicationService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := applicationService.GenAppJwtToken(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}
