package controllers

import (
	"github.com/gin-gonic/gin"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/services"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	baseProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base/proto"
)

func (ac *Controller) ApplicationList(c *gin.Context) {
	req := proto.ApplicationListReq{QueryBaseRequest: base.QueryBaseDefaultRequest()}
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var applicationService *services.ApplicationService
	err = ac.container.Extract(&applicationService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := applicationService.ApplicationList(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

func (ac *Controller) ApplicationAdd(c *gin.Context) {
	var req proto.ApplicationAddReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var applicationService *services.ApplicationService
	err = ac.container.Extract(&applicationService)
	if !ac.util.CheckError(c, err) {
		return
	}
	appID, err := applicationService.ApplicationAdd(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, baseProto.PrimaryKey{ID: appID})
}

func (ac *Controller) ApplicationUpdate(c *gin.Context) {
	var req proto.ApplicationUpdateReq
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var applicationService *services.ApplicationService
	err = ac.container.Extract(&applicationService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = applicationService.ApplicationUpdate(c, sess.Code, req)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, baseProto.PrimaryKey{ID: req.ID})
}

func (ac *Controller) ApplicationDetail(c *gin.Context) {
	var req baseProto.PrimaryKey
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var applicationService *services.ApplicationService
	err = ac.container.Extract(&applicationService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := applicationService.ApplicationDetail(c, sess.Code, req.ID)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

func (ac *Controller) ApplicationAuthDetail(c *gin.Context) {
	var req baseProto.PrimaryKey
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var applicationService *services.ApplicationService
	err = ac.container.Extract(&applicationService)
	if !ac.util.CheckError(c, err) {
		return
	}
	resp, err := applicationService.ApplicationDetail(c, sess.Code, req.ID)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c, resp)
}

func (ac *Controller) ApplicationEnable(c *gin.Context) {
	var req baseProto.PrimaryKey
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var applicationService *services.ApplicationService
	err = ac.container.Extract(&applicationService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = applicationService.ApplicationEnable(c, sess.Code, sess.Account, req.ID)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

func (ac *Controller) ApplicationDisable(c *gin.Context) {
	var req baseProto.PrimaryKey
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var applicationService *services.ApplicationService
	err = ac.container.Extract(&applicationService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = applicationService.ApplicationDisable(c, sess.Code, sess.Account, req.ID)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

func (ac *Controller) ApplicationReset(c *gin.Context) {
	var req baseProto.PrimaryKey
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var applicationService *services.ApplicationService
	err = ac.container.Extract(&applicationService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = applicationService.ApplicationReset(c, sess.Code, req.ID)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}

func (ac *Controller) ApplicationDelete(c *gin.Context) {
	var req baseProto.PrimaryKey
	err := ac.util.Bind(c, &req)
	if !ac.util.CheckError(c, err) {
		return
	}
	sess, ok := ac.util.GetSession(c)
	if !ok {
		return
	}
	var applicationService *services.ApplicationService
	err = ac.container.Extract(&applicationService)
	if !ac.util.CheckError(c, err) {
		return
	}
	err = applicationService.ApplicationDelete(c, sess.Code, req.ID)
	if !ac.util.CheckError(c, err) {
		return
	}
	ac.util.SendSuccess(c)
}
