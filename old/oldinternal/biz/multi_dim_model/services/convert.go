package services

import (
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/proto"
)

func (s *MultiDimModelService) convertMultiDimModelToMultiDimModelBriefWithContent(model *proto.MultiDimModel) *proto.MultiDimModelBriefWithContent {
	viewContent := s.getViewContentStruct(model.ViewContent)
	return &proto.MultiDimModelBriefWithContent{
		MultiDimModelBrief: &proto.MultiDimModelBrief{
			ID:                 model.ID,
			Code:               model.Code,
			SubjectID:          model.SubjectID,
			Name:               model.Name,
			NameCn:             model.NameCn,
			ModifyStatus:       model.ModifyStatus,
			ReleaseStatus:      modelBase.ReleaseStatus(model.ReleaseStatus),
			CreatedOn:          model.CreatedOn,
			ModifiedOn:         model.ModifiedOn,
			SourceProjectLevel: int(model.SourceProjectLevel),
			EditedStandardFlag: model.EditedStandardFlag,
		},
		ViewContent: &viewContent,
	}
}
