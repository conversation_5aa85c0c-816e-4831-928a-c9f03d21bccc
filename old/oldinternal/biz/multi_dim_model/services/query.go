package services

import (
	"context"
	"database/sql"
	"encoding/json"
	newModelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

// 统计
func (s *MultiDimModelService) GetMultiDimCount(ctx context.Context, project string, env entities.ProjectEnvType) (int64, error) {
	return s.repo.GetMultiDimModelCount(ctx, project, env)
}

// 列表
func (s *MultiDimModelService) GetMultiDimModelBriefList(ctx context.Context, project string, req proto.GetMultiDimModelListRequest) ([]*proto.MultiDimModelBrief, error) {
	return s.repo.GetMultiDimModelBriefList(ctx, project, req)
}

func (s *MultiDimModelService) GetMultiDimModelList(ctx context.Context, project string,
	req proto.GetMultiDimModelListRequest) ([]proto.MultiDimModelResp, error) {
	// 获取开发环境的多维模型列表
	if req.Environment == "" {
		req.Environment = entities.ProjectEnvDev
	}
	list, err := s.repo.GetMultiDimModelList(ctx, project, req)
	if err != nil {
		return nil, err
	}
	codes := make([]string, 0, len(list))
	var results []proto.MultiDimModelResp
	for i := 0; i < len(list); i++ {
		result := proto.MultiDimModelResp{
			ID:             list[i].ID,
			Code:           list[i].Code,
			SubjectID:      list[i].SubjectID,
			TableName:      list[i].Name,
			Name:           list[i].NameCn,
			Description:    list[i].Description.String,
			Environment:    list[i].Environment,
			ModifiedBy:     list[i].ModifiedBy.String,
			ModifiedOn:     list[i].ModifiedOn,
			CreatedBy:      list[i].CreatedBy.String,
			CreatedOn:      list[i].CreatedOn,
			Category:       MultiDimCategory,
			ViewContent:    s.getViewContentStruct(list[i].ViewContent),
			Resource:       list[i].Resource,
			ModifyStatus:   list[i].ModifyStatus,
			ReleaseStatus:  modelBase.ReleaseStatus(list[i].ReleaseStatus),
			IndividualType: newModelBase.ConvertSourceProjectLevelToInIndividualType(int(list[i].SourceProjectLevel), list[i].EditedStandardFlag),
			ApplyStatus:    list[i].ApplyStatus,
		}
		codes = append(codes, list[i].Code)
		result.IsSave = false
		if list[i].ViewContent != "" {
			result.IsSave = true
		}
		results = append(results, result)
	}
	multiCodes, err := s.repo.HasMultiModelRelease(ctx, project, codes)
	if err != nil && err != sql.ErrNoRows {
		return nil, err
	}
	for idx, result := range results {
		if id, ok := multiCodes[result.Code]; ok {
			results[idx].ReleaseID = id
			results[idx].IsRelease = true
		}
	}
	return results, nil
}

func (s *MultiDimModelService) getViewContentStruct(viewContentStr string) proto.ViewContent {
	var viewContent proto.ViewContent
	err := json.Unmarshal([]byte(viewContentStr), &viewContent)
	if err != nil {
		return viewContent
	}
	return viewContent
}

func (s *MultiDimModelService) GetMultiDimModelBriefWithContent(ctx context.Context, project string, code string, env entities.ProjectEnvType) (*proto.MultiDimModelBriefWithContent, error) {
	model, err := s.repo.GetMultiDimModelByCode(ctx, project, code, env)
	if err == sql.ErrNoRows {
		err = utils.NewUserError("多维模型[%s]不存在", code)
		return nil, err
	}
	if err != nil {
		return nil, err
	}
	return s.convertMultiDimModelToMultiDimModelBriefWithContent(&model), nil
}

func (s *MultiDimModelService) GetMultiDimRelationsByUpstreamPhysicalModelCode(ctx context.Context, project string, code string) ([]*modelProto.ModelRefRelation, error) {
	return s.repo.GetMultiDimRelationsByUpstreamPhysicalModelCode(ctx, project, code)
}
