package services

import (
	"context"
	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/repo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewMultiDimModelInternalService))
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewMultiDimModelService, inject.As(new(MultiDimService))))
}

type MultiDimService interface {
	// query
	GetMultiDimCount(ctx context.Context, project string, env entities.ProjectEnvType) (int64, error)
	GetMultiDimModelList(ctx context.Context, project string, req proto.GetMultiDimModelListRequest) ([]proto.MultiDimModelResp, error)
	GetMultiDimModelBriefList(ctx context.Context, project string, req proto.GetMultiDimModelListRequest) ([]*proto.MultiDimModelBrief, error)
	GetMultiDimModelBriefWithContent(ctx context.Context, project string, code string, env entities.ProjectEnvType) (*proto.MultiDimModelBriefWithContent, error)
	GetMultiDimRelationsByUpstreamPhysicalModelCode(ctx context.Context, project string, code string) ([]*modelProto.ModelRefRelation, error)
}

var _ MultiDimService = (*MultiDimModelService)(nil)

const MultiDimCategory = "multi_dim"

type MultiDimInternalService struct {
	logger       *logger.Logger // 日志打印
	modelService *model.ModelService
}

func NewMultiDimModelInternalService(logger *logger.Logger,
	modelService *model.ModelService) (*MultiDimInternalService, error) {
	return &MultiDimInternalService{
		logger:       logger,
		modelService: modelService,
	}, nil
}

type MultiDimModelService struct {
	repo repo.MultiDimModelRepo // 多维模型仓库
	*MultiDimInternalService
}

func NewMultiDimModelService(multiDimInternalService *MultiDimInternalService, repo repo.MultiDimModelRepo) *MultiDimModelService {
	ms := &MultiDimModelService{
		repo:                    repo,
		MultiDimInternalService: multiDimInternalService,
	}
	return ms
}
