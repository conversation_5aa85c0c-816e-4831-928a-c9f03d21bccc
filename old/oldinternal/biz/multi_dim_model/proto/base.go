package proto

import (
	"encoding/json"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/model_distribute"

	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	viewLogic "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type AddMultiDimModelRequest struct {
	SubjectID   string                       `form:"subject_id" json:"subject_id" binding:"required"`
	Name        string                       `form:"name" json:"name" binding:"required"`
	TableName   string                       `form:"table_name" json:"table_name" binding:"required"`
	Resource    entities.ProjectResourceType `form:"resource" json:"resource"`
	Description string                       `form:"description" json:"description"`
	ViewContent string                       `form:"view_content" json:"view_content"`
}

type AddMultiDimModelResponse struct {
	Code string `form:"code" json:"code"`
}

type GetMultiDimModelListRequest struct {
	SubjectID           string                       `form:"subject_id" json:"subject_id" binding:"required"`
	Sorts               string                       `form:"sorts" json:"sorts" binding:"validateQuerySorts"`
	Environment         entities.ProjectEnvType      `form:"environment" json:"environment"`
	ModelCodes          []string                     `form:"model_codes" json:"model_codes"`
	Resource            entities.ProjectResourceType `form:"resource" json:"resource"`
	IncludeUnsavedModel int                          `form:"include_unsaved_model" json:"include_unsaved_model"`
	CodeGTE             string
	MergeStandard       bool `form:"merge_standard" json:"merge_standard"`
	base.QueryBaseRequest
}

type BatchGetMultiDimTableCountReq struct {
	ModelIds []string
}

type BatchGetMultiDimTableCountRspItem struct {
	ID    string `json:"id" db:"id"`
	Count int    `json:"count" db:"count"`
}

type BatchGetMultiDimTableCountRsp []*BatchGetMultiDimTableCountRspItem

type GetMultiDimModelVersionListRequest struct {
	Sorts string `form:"sorts" json:"sorts" binding:"validateQuerySorts"`
	Code  string `form:"code" json:"code" binding:"required"`
	base.QueryBaseRequest
}

type EntityLinkType string

const (
	LeftJoin    EntityLinkType = "left join"    // 以左表为主表关联
	RightJoin   EntityLinkType = "right join"   // 以右表为主表关联
	InnerJoin   EntityLinkType = "inner join"   // 按照对应行关联
	FullJoin    EntityLinkType = "full join"    // 所有行关联
	LeftParent  EntityLinkType = "left parent"  // 以左表为父维度关联
	RightParent EntityLinkType = "right parent" // 以右表为父维度关联
)

type EntityLinkQuantityType string

const (
	OneToOne  EntityLinkQuantityType = "one_to_one"  // 一对一
	OneToMany EntityLinkQuantityType = "one_to_many" // 一对多
	ManyToOne EntityLinkQuantityType = "many_to_one" // 多对一
)

// FieldRelation 复用之前汇总模型的关联格式，减少变动
type FieldRelation struct {
	Left            string `json:"left"`
	Right           string `json:"right"`
	LogicalRelation string `json:"logical_relation"`
	Operator        string `json:"operator"`
}

// EntityLink 定义view_content的格式
type EntityLink struct {
	FromID           string                 `json:"from_id"`            // 左表code
	ToID             string                 `json:"to_id"`              // 右表code
	JoinType         EntityLinkType         `json:"join_type"`          // 关联类型
	JoinQuantityType EntityLinkQuantityType `json:"join_quantity_type"` // 关联数量关系类型
	JoinFields       []FieldRelation        `json:"join_fields"`        // 关联字段
	JoinWheres       interface{}            `json:"join_wheres"`        // 关联where
}

func UnmarshalMultiDimViewContent(viewContentStr string) (viewContent *ViewContent, err error) {
	err = json.Unmarshal([]byte(viewContentStr), &viewContent)
	return
}

type ViewContent struct {
	Link []EntityLink     `json:"link"`
	Node []viewLogic.Node `json:"node"`
}

func (s *ViewContent) FindNodeByTableName(tableName string) (node *viewLogic.Node) {
	for idx := range s.Node {
		item := s.Node[idx]
		if item.TableName == tableName {
			return &item
		}
	}
	return
}

func (s *ViewContent) FindNodeByID(id string) (node *viewLogic.Node) {
	for idx := range s.Node {
		item := &s.Node[idx]
		if item.Id == id {
			return item
		}
	}
	return nil
}

type MultiDimModel struct {
	ID                 string                       `json:"id" db:"id"`
	Code               string                       `json:"code" db:"code"`
	SubjectID          string                       `json:"subject_id" db:"subject_id"`
	SubjectName        string                       `json:"subject_name" db:"subject_name"`
	Name               string                       `json:"name" db:"name"`
	NameCn             string                       `json:"name_cn" db:"name_cn"`
	Description        db.NullString                `json:"description" db:"description"`
	Environment        entities.ProjectEnvType      `json:"environment" db:"environment"`
	ViewContent        string                       `json:"view_content" db:"view_content"`
	ReleaseComment     string                       `json:"release_comment" db:"release_comment"`
	Version            int32                        `json:"version" db:"version"`
	Resource           entities.ProjectResourceType `json:"resource" db:"resource"`
	ModifyStatus       modelBase.ModifyStatus       `json:"modify_status" db:"modify_status"`
	ModifiedBy         db.NullString                `json:"modified_by" db:"modified_by"`
	ModifiedOn         db.NullTime                  `json:"modified_on" db:"modified_on"`
	CreatedBy          db.NullString                `json:"created_by" db:"created_by"`
	CreatedOn          db.NullTime                  `json:"created_on" db:"created_on"`
	ReleaseStatus      string                       `json:"release_status" db:"release_status"`
	SourceProjectLevel model_distribute.ProjectType `json:"source_project_level" db:"source_project_level"`
	SourceProject      db.NullString                `json:"source_project" db:"source_project"`
	EditedStandardFlag int                          `json:"edited_standard_flag" db:"edited_standard_flag"`
	RefType            string                       `json:"ref_type" db:"ref_type"`
	ApplyStatus        int                          `json:"apply_status" db:"apply_status"`
}

type RelationOperator string

const (
	EQ RelationOperator = "="
	GT RelationOperator = ">"
	GE RelationOperator = ">="
	LT RelationOperator = "<"
	LE RelationOperator = "<="
)

type MultiDimModelBrief struct {
	ID                 string                  `json:"id" db:"id"`
	Code               string                  `json:"code" db:"code"`
	SubjectID          string                  `json:"subject_id" db:"subject_id"`
	Name               string                  `json:"name" db:"name"`
	NameCn             string                  `json:"name_cn" db:"name_cn"`
	ModifyStatus       modelBase.ModifyStatus  `json:"modify_status" db:"modify_status"`
	ReleaseStatus      modelBase.ReleaseStatus `json:"release_status" db:"release_status"`
	CreatedOn          db.NullTime             `json:"created_on" db:"created_on"`
	ModifiedOn         db.NullTime             `json:"modified_on" db:"modified_on"`
	SourceProjectLevel int                     `json:"source_project_level" db:"source_project_level"`
	EditedStandardFlag int                     `json:"edited_standard_flag" db:"edited_standard_flag"`
	CreatedBy          db.NullString           `json:"created_by" db:"created_by"`
	ModifiedBy         db.NullString           `json:"modified_by" db:"modified_by"`
	Description        string                  `json:"description" db:"description"`
}

type MultiDimModelBriefWithContent struct {
	*MultiDimModelBrief
	ViewContent *ViewContent `json:"view_content"`
}

type MultiDimModelResp struct {
	ID             string                       `json:"id"`
	Code           string                       `json:"code"`
	SubjectID      string                       `json:"subject_id"`
	SubjectName    string                       `json:"subject_name"`
	Name           string                       `json:"name"`
	TableName      string                       `json:"table_name"`
	Description    string                       `json:"description"`
	Environment    entities.ProjectEnvType      `json:"environment"`
	ModifiedBy     string                       `json:"modified_by"`
	ModifiedOn     db.NullTime                  `json:"modified_on"`
	CreatedBy      string                       `json:"created_by"`
	CreatedOn      db.NullTime                  `json:"created_on"`
	IsRelease      bool                         `json:"is_release"`
	Category       string                       `json:"category"` // 该字段写死为multi_dim，主要适应前端
	IsSave         bool                         `json:"is_save"`
	ReleaseID      string                       `json:"release_id"`
	ViewContent    ViewContent                  `json:"view_content"`
	Resource       entities.ProjectResourceType `json:"resource"`
	ModifyStatus   modelBase.ModifyStatus       `json:"modify_status"`
	ReleaseStatus  modelBase.ReleaseStatus      `json:"release_status"`
	IndividualType string                       `json:"individual_type"`
	ApplyStatus    int                          `json:"apply_status"`
}
