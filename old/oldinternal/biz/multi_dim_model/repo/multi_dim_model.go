package repo

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"

	"github.com/jmoiron/sqlx"

	baseStore "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/base/store"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"

	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewMultiDimModelStore, inject.As(new(MultiDimModelRepo))))
}

var _ MultiDimModelRepo = (*MultiDimModelStore)(nil)

type MultiDimModelRepo interface {
	GetTx(ctx context.Context, project string) (tx *sql.Tx, err error)
	EndTx(tx *sql.Tx, err error)

	// Query
	GetMultiDimModelList(ctx context.Context, project string, request proto.GetMultiDimModelListRequest) ([]proto.MultiDimModel, error)
	GetMultiDimModelBriefList(ctx context.Context, project string, request proto.GetMultiDimModelListRequest) ([]*proto.MultiDimModelBrief, error)
	GetMultiDimModelByCode(ctx context.Context, project, code string, environment entities.ProjectEnvType) (detail proto.MultiDimModel, err error)
	HasMultiModelRelease(ctx context.Context, project string, codes []string) (map[string]string, error)
	GetMultiDimRelationsByUpstreamPhysicalModelCode(ctx context.Context, project, code string) (relations []*modelProto.ModelRefRelation, err error)
	GetMultiDimModelCount(ctx context.Context, project string, environment entities.ProjectEnvType) (resp int64, err error)
}

type MultiDimModelStore struct {
	*baseStore.Repo
}

func NewMultiDimModelStore(saas db.SaaS) *MultiDimModelStore {
	return &MultiDimModelStore{
		Repo: baseStore.NewRepo(saas),
	}
}

// GetTx 获取Tx，支持外层做事务
func (s *MultiDimModelStore) GetTx(ctx context.Context, project string) (tx *sql.Tx, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	tx, err = sess.Begin()
	return
}

func (s *MultiDimModelStore) EndTx(tx *sql.Tx, err error) {
	if nil != err {
		if rollErr := tx.Rollback(); nil != rollErr {
			log.Print("rollback err:", rollErr)
		}
	} else {
		if comErr := tx.Commit(); nil != comErr {
			log.Print("rollback err:", comErr)
		}
	}

}

func (s *MultiDimModelStore) execSqlByTx(ctx context.Context, tx *sqlx.Tx, execSql string, args ...interface{}) (err error) {
	stmt, err := tx.Prepare(execSql)
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, args...)
	if err != nil {
		return err
	}
	return
}

func (s *MultiDimModelStore) execNamedSqlByTx(ctx context.Context, tx *sqlx.Tx, sqlText string, args interface{}) (err error) {
	update, updateArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	stmt, err := tx.Prepare(update)
	if err != nil {
		return
	}
	if _, err = stmt.ExecContext(ctx, updateArgs...); err != nil {
		return
	}
	return
}

// 获取下游依赖的ads模型
func (s *MultiDimModelStore) GetDependentPhysicalModelName(ctx context.Context, project string, modelId string, env cBase.ProjectEnvType) (names []string, err error) {
	names = []string{}

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	// 获取关联的生产环境的物理指标的code
	sqlText := "select t2.name from dap_m_physical_model_relation t1 " +
		"left join dap_m_physical_model t2 on t1.physical_model_id=t2.id " +
		"where t1.dep_physical_model_id=:multi_model_id and t1.category='multi_dim' and t2.environment=:environment and t2.category='ads'"
	args := map[string]interface{}{
		"multi_model_id": modelId,
		"environment":    env,
	}

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	err = sess.SelectContext(ctx, &names, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return
	}
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

func (s *MultiDimModelStore) GetMultiDimRelationsByUpstreamPhysicalModelCode(ctx context.Context, project, code string) (relations []*modelProto.ModelRefRelation, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	// 获取关联的生产环境的物理指标的code
	sqlText := fmt.Sprintf("select '%s' as from_code, code as to_code from dap_m_multi_dim_model where environment in ('开发', '生产') and view_content like '%%\"code_id\":\"%s\"%%'", code, code)
	if err = sess.SelectContext(ctx, &relations, sqlText); err != nil && err != sql.ErrNoRows {
		return nil, err
	}
	return relations, nil
}

// 获取下游依赖的指标视图模型
func (s *MultiDimModelStore) GetDependentIndicatorModelName(ctx context.Context, project string, modelCode string, env cBase.ProjectEnvType) (names []string, err error) {
	names = []string{}

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	// 获取关联的生产环境的物理指标的code
	sqlText := "select name from dap_m_indicator " +
		"where multi_dim_model_code=:multi_dim_model_code and environment=:environment"
	args := map[string]interface{}{
		"multi_dim_model_code": modelCode,
		"environment":          env,
	}

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	err = sess.SelectContext(ctx, &names, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return
	}
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

func (s *MultiDimModelStore) GetNextVersion(ctx context.Context, tx *sql.Tx, code string) (version int32, err error) {
	if rows, errTmp := tx.QueryContext(ctx, "select max(version)+1 from dap_m_multi_dim_model where code=? and environment=?", code, entities.ProjectEnvHistory); nil == errTmp {
		defer func() {
			errCountClose := rows.Close()
			if errCountClose != nil {
				fmt.Print(errCountClose)
			}
		}()

		for rows.Next() {
			scanErr := rows.Scan(&version)
			if nil != scanErr {
				if strings.Contains(scanErr.Error(), "converting NULL to int") {
					return 1, nil
				}
			}

			err = scanErr
		}
	} else {
		err = errTmp
	}

	return
}

func (s *MultiDimModelStore) GetMultiDimModelBriefList(ctx context.Context, project string, req proto.GetMultiDimModelListRequest) ([]*proto.MultiDimModelBrief, error) {
	var modelList []*proto.MultiDimModelBrief
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	sqlStatement := "select id,code,subject_id,name,name_cn,modify_status,release_status,created_on,modified_on,source_project_level,edited_standard_flag,created_by,modified_by,description from dap_m_multi_dim_model "

	// 添加limit或order by
	args := map[string]interface{}{
		"skip": req.GetSkip(),
		"size": req.PageSize,
	}

	// Limit
	limitStatement := " limit :skip,:size"

	// Where
	var whereSQLs []string
	if req.SubjectID != "" {
		args["subject_id"] = req.SubjectID
		whereSQLs = append(whereSQLs, "(subject_id=:subject_id)")
	}
	if req.Environment != "" {
		args["environment"] = req.Environment
		whereSQLs = append(whereSQLs, "(environment=:environment)")
	}
	if len(req.ModelCodes) > 0 {
		args["codes"] = req.ModelCodes
		whereSQLs = append(whereSQLs, "(code in (:codes))")
	}
	if req.Keyword != "" {
		args["kw"] = req.GetEscapeKeyword()
		whereSQLs = append(whereSQLs, "(CONCAT(`name`,`name_cn`) like :kw )")
	}
	if req.Resource != "" {
		args["resource"] = req.Resource
		whereSQLs = append(whereSQLs, "(resource = :resource)")
	}
	if req.Environment == entities.ProjectEnvDev && req.IncludeUnsavedModel == 0 {
		whereSQLs = append(whereSQLs, "(view_content != \"\" and view_content is not null)")
	}
	whereSQL := "where " + strings.Join(whereSQLs, " and ")

	// 默认created_on
	orderBy := " order by created_on DESC"
	if req.Sorts != "" {
		orderBy = fmt.Sprintf(" order by %s ", req.Sorts)
	}

	sqlStatement += whereSQL + orderBy + limitStatement

	query, queryArgs, err := sqlx.Named(sqlStatement, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	if err = sqlStore.SelectContext(ctx, &modelList, query, queryArgs...); err != nil {
		return nil, err
	}
	return modelList, nil
}

func (s *MultiDimModelStore) GetMultiDimModelList(ctx context.Context, project string, req proto.GetMultiDimModelListRequest) ([]proto.MultiDimModel, error) {
	var modelList []proto.MultiDimModel
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	sqlStatement := "select id,code,subject_id,name,name_cn,description,environment,view_content,created_on,modified_on," +
		"created_by,modified_by,release_comment,`version`,resource,modify_status,release_status,source_project_level,source_project,edited_standard_flag,apply_status from dap_m_multi_dim_model "
	// 添加limit或order by

	args := map[string]interface{}{
		"skip": req.GetSkip(),
		"size": req.PageSize,
	}

	// Limit
	limitStatement := " limit :skip,:size"

	// Where
	var whereSQLs []string
	if req.SubjectID != "" {
		args["subject_id"] = req.SubjectID
		whereSQLs = append(whereSQLs, "(subject_id=:subject_id)")
	}
	if req.Environment != "" {
		args["environment"] = req.Environment
		whereSQLs = append(whereSQLs, "(environment=:environment)")
	}
	if len(req.ModelCodes) > 0 {
		args["codes"] = req.ModelCodes
		whereSQLs = append(whereSQLs, "(code in (:codes))")
	}
	if req.Keyword != "" {
		args["kw"] = req.GetEscapeKeyword()
		whereSQLs = append(whereSQLs, "(name like ? or description like ? )")
	}
	if req.Resource != "" {
		args["resource"] = req.Resource
		whereSQLs = append(whereSQLs, "(resource = :resource)")
	}
	if req.Environment == entities.ProjectEnvDev && req.IncludeUnsavedModel == 0 {
		whereSQLs = append(whereSQLs, "(view_content != \"\" and view_content is not null)")
	}
	whereSQL := "where " + strings.Join(whereSQLs, " and ")

	// 默认created_on
	orderBy := " order by created_on DESC"
	if req.Sorts != "" {
		orderBy = fmt.Sprintf(" order by %s ", req.Sorts)
	}

	sqlStatement += whereSQL + orderBy + limitStatement

	query, queryArgs, err := sqlx.Named(sqlStatement, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	if err = sqlStore.SelectContext(ctx, &modelList, query, queryArgs...); err != nil {
		return nil, err
	}
	return modelList, nil
}

func (s *MultiDimModelStore) HasMultiModelRelease(ctx context.Context, project string, codes []string) (map[string]string, error) {
	if len(codes) == 0 {
		return map[string]string{}, nil
	}
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	args := map[string]interface{}{
		"environment": entities.ProjectEnvProd,
		"code":        codes,
	}
	var sqlCmd = `select id, code from dap_m_multi_dim_model where environment=:environment and code in (:code)`
	type ReleaseCode struct {
		ID   string `json:"id" db:"id"`
		Code string `json:"code" db:"code"`
	}
	releaseCodes := make([]ReleaseCode, 0)
	query, queryArgs, err := sqlx.Named(sqlCmd, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	if err = sess.SelectContext(ctx, &releaseCodes, query, queryArgs...); err != nil {
		return nil, err
	}
	result := make(map[string]string, len(releaseCodes))
	for _, multiModel := range releaseCodes {
		result[multiModel.Code] = multiModel.ID
	}
	return result, nil
}

func (s *MultiDimModelStore) GetMultiDimModelByCode(ctx context.Context, project, code string, environment entities.ProjectEnvType) (detail proto.MultiDimModel, err error) {
	return s.getMultiDimModelByCode(ctx, project, code, environment)
}

func (s *MultiDimModelStore) getMultiDimModelByCode(ctx context.Context, project, code string, environment entities.ProjectEnvType) (detail proto.MultiDimModel, err error) {
	var sqlStore *sqlx.DB
	sqlStore, err = s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	sqlText := `SELECT mdm.id,mdm.code,mdm.subject_id,sb.name as subject_name,mdm.name,mdm.name_cn,mdm.description,mdm.environment,mdm.view_content,mdm.created_on,mdm.modified_on,
				mdm.created_by,mdm.modified_by,mdm.modify_status,mdm.resource,mdm.release_status,mdm.source_project_level,mdm.source_project,mdm.edited_standard_flag,mdm.ref_type
				FROM dap_m_multi_dim_model mdm LEFT JOIN dap_m_subject sb ON mdm.subject_id = sb.id
				WHERE CODE = ? AND environment = ?`
	err = sqlStore.GetContext(ctx, &detail, sqlText, code, environment)
	if err != nil {
		return
	}
	return
}

func (s *MultiDimModelStore) GetMultiDimModelByName(ctx context.Context, project, name string) (detail proto.MultiDimModel, err error) {
	var sqlStore *sqlx.DB
	sqlStore, err = s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	err = sqlStore.GetContext(ctx, &detail,
		"select id,code,subject_id,name,name_cn,description,environment,view_content,created_on,modified_on,"+
			"created_by,modified_by from dap_m_multi_dim_model where name=? and environment != '历史'", name)
	if err != nil {
		return
	}
	return
}

func (s *MultiDimModelStore) GetMultiDimModelByNameCn(ctx context.Context, project, name string, excludeCode string) (detail proto.MultiDimModel, err error) {
	var sqlStore *sqlx.DB
	sqlStore, err = s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	err = sqlStore.GetContext(ctx, &detail,
		"select id,code,subject_id,name,name_cn,description,environment,view_content,created_on,modified_on,"+
			"created_by,modified_by from dap_m_multi_dim_model where name_cn=? and code != ? and environment != '历史' ", name, excludeCode)
	if err != nil {
		return
	}
	return
}

func (s *MultiDimModelStore) GetMultiDimModelCount(ctx context.Context, project string, environment entities.ProjectEnvType) (count int64, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	sqlText := "select count(*) as count from dap_m_multi_dim_model where environment=?"
	err = sess.Get(&count, sqlText, environment)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}
