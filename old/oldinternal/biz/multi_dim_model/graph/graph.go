package graph

import (
	"fmt"

	viewLogic "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	multiDimProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"
)

// 顶点定义
type vertex struct {
	nodeCtx *viewLogic.Node
	id      string
}

type direction int

const (
	both = iota
	in
	out
)

// 边定义
type edge struct {
	linkCtx   *multiDimProto.EntityLink
	id        string
	v1        string
	v2        string
	direction direction
}

// 图定义
type MultiDimModelGraph struct {
	viewContent   *multiDimProto.ViewContent
	vertexes      []*vertex
	vertexMap     map[string]*vertex
	edges         []*edge
	vertexEdgeMap map[string][]*edge
}

func NewMultiDimModelGraph(viewContent *multiDimProto.ViewContent) *MultiDimModelGraph {
	return &MultiDimModelGraph{
		viewContent: viewContent,
	}
}

type DirOption int

const (
	NoDir     DirOption = iota // 无方向模式，忽略left/right join和父子维度方向
	ParentDir                  // 父子维方向模式，忽略left/right join方向
	JoinDir                    // Join方向模式，根据left、right join以及父子维度生成方向
)

func (s *MultiDimModelGraph) Init(dirOption DirOption) error {
	// 初始化 顶点
	s.vertexes = []*vertex{}
	s.vertexMap = map[string]*vertex{}
	for idx := range s.viewContent.Node {
		node := &s.viewContent.Node[idx]
		v := &vertex{
			id:      node.Id,
			nodeCtx: node,
		}
		s.vertexes = append(s.vertexes, v)
		s.vertexMap[v.id] = v
	}
	// 初始化 边 及 节点出边列表
	s.edges = []*edge{}
	s.vertexEdgeMap = map[string][]*edge{}
	for idx := range s.viewContent.Link {
		link := &s.viewContent.Link[idx]
		var dir direction
		dir = both
		if dirOption == ParentDir {
			if link.JoinType == multiDimProto.LeftParent {
				dir = out
			} else if link.JoinType == multiDimProto.RightParent {
				dir = in
			}
		} else if dirOption == JoinDir {
			if link.JoinType == multiDimProto.LeftJoin || link.JoinType == multiDimProto.LeftParent {
				dir = out
			} else if link.JoinType == multiDimProto.RightJoin || link.JoinType == multiDimProto.RightParent {
				dir = in
			}
		}
		e := &edge{
			linkCtx:   link,
			id:        fmt.Sprintf("%s-%s", link.FromID, link.ToID),
			v1:        link.FromID,
			v2:        link.ToID,
			direction: dir,
		}

		v1Edges := s.vertexEdgeMap[e.v1]
		v1Edges = append(v1Edges, e)
		s.vertexEdgeMap[e.v1] = v1Edges
		v2Edges := s.vertexEdgeMap[e.v2]
		v2Edges = append(v2Edges, e)
		s.vertexEdgeMap[e.v2] = v2Edges
		s.edges = append(s.edges, e)
	}
	return s.checkValid()
}

func (s *MultiDimModelGraph) checkValid() (err error) {
	for _, e := range s.edges {
		if _, ok := s.vertexMap[e.v1]; !ok {
			err = utils.NewUserError("关系【%s - %s】中节点%s不存在", e.v1, e.v2, e.v1)
			return
		}
		if _, ok := s.vertexMap[e.v2]; !ok {
			err = utils.NewUserError("关系【%s - %s】中节点%s不存在", e.v1, e.v2, e.v1)
			return
		}
	}
	return
}
