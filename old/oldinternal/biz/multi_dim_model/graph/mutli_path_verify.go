package graph

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"
)

func (s *MultiDimModelGraph) VerifyNoMultiPathToOtherNode(nodeId string) (err error) {
	// 顶点 是否可以访问，在遍历图时，设置其他事实表节点为不可访问
	visibleMap := map[string]bool{}
	for _, v := range s.vertexes {
		if (v.nodeCtx.Category == base.DwdModelCate || v.nodeCtx.Category == base.DwsModelCate) && v.id != nodeId {
			// 除当前表外的事实表在遍历中不可见（成环和多路径校验只针对路径上不包含事实表）
			visibleMap[v.id] = false
		} else {
			visibleMap[v.id] = true
		}
	}
	// 顶点 是否已经访问过
	visitedVertexMap := map[string]struct{}{}
	visitedEdgeMap := map[string]struct{}{}
	v := s.vertexMap[nodeId]
	visitedVertexMap[v.id] = struct{}{}
	// 从v节点开始遍历，看是否有多条路径
	return s.dfsVertexVerifyMultiPath(v, v, visibleMap, visitedVertexMap, visitedEdgeMap)
}

func (s *MultiDimModelGraph) dfsVertexVerifyMultiPath(root *vertex, v *vertex, visibleMap map[string]bool, visitedVertexMap map[string]struct{}, visitedEdgeMap map[string]struct{}) (err error) {
	// 如果目标节点是不可达节点, 返回
	if visible := visibleMap[v.id]; !visible {
		return
	}
	// 如果目标节点已经被访问过，说明有多条路径可达或成环，结束遍历
	if _, ok := visitedVertexMap[v.id]; ok && len(visitedVertexMap) != 1 {
		if root == v {
			err = utils.NewUserError("结点【%s】路径成环，请修改模型", root.nodeCtx.TableName, v.nodeCtx.TableName)
		} else {
			err = utils.NewUserError("结点【%s】到结点【%s】存在多条路径或成环，请修改模型", root.nodeCtx.TableName, v.nodeCtx.TableName)
		}
		return
	}

	// 标记v已经被访问过
	visitedVertexMap[v.id] = struct{}{}
	edges := s.vertexEdgeMap[v.id]
	for _, e := range edges {
		// 如果路径已经遍历过，跳过路径，用来处理双向路径
		if _, ok := visitedEdgeMap[e.id]; ok {
			continue
		}
		var targetId string
		if e.v1 == v.id && (e.direction == out || e.direction == both) {
			targetId = e.v2
		} else if e.v2 == v.id && (e.direction == in || e.direction == both) {
			targetId = e.v1
		} else {
			continue
		}
		visitedEdgeMap[e.id] = struct{}{}
		target := s.vertexMap[targetId]
		err = s.dfsVertexVerifyMultiPath(root, target, visibleMap, visitedVertexMap, visitedEdgeMap)
		if err != nil {
			return
		}
	}
	return
}
