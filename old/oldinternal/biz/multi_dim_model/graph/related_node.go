package graph

import (
	viewLogic "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
)

func (s *MultiDimModelGraph) FindSingleDwTableRelatedNode(nodeId string) (nodes []*viewLogic.Node) {
	// 顶点 是否可以访问，在遍历图时，设置其他事实表节点为不可访问
	visibleMap := map[string]bool{}
	for _, v := range s.vertexes {
		if (v.nodeCtx.Category == base.DwdModelCate || v.nodeCtx.Category == base.DwsModelCate) && v.id != nodeId {
			// 除当前表外的事实表在遍历中不可见（成环和多路径校验只针对路径上不包含事实表）
			visibleMap[v.id] = false
		} else {
			visibleMap[v.id] = true
		}
	}

	root := s.vertexMap[nodeId]
	visitedVertexMap := map[string]*vertex{}
	s.dfsArriveableNode(root, visitedVertexMap, visibleMap)

	for _, v := range visitedVertexMap {
		nodes = append(nodes, v.nodeCtx)
	}
	return
}

func (s *MultiDimModelGraph) dfsArriveableNode(v *vertex, visitedVertexMap map[string]*vertex, visibleMap map[string]bool) {
	if visibleMap[v.id] == false {
		return
	}
	if _, ok := visitedVertexMap[v.id]; ok {
		return
	}
	visitedVertexMap[v.id] = v
	edges := s.vertexEdgeMap[v.id]
	for _, e := range edges {
		var targetId string
		if e.v1 == v.id && (e.direction == out || e.direction == both) {
			targetId = e.v2
		} else if e.v2 == v.id && (e.direction == in || e.direction == both) {
			targetId = e.v1
		} else {
			continue
		}
		target := s.vertexMap[targetId]
		s.dfsArriveableNode(target, visitedVertexMap, visibleMap)
	}
}
