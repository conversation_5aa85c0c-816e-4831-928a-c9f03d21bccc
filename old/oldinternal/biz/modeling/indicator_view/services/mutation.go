package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/injection"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/moql"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_sqlce/store"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/ads_common"

	"github.com/gin-gonic/gin"
	"github.com/go-sql-driver/mysql"
	"github.com/jinzhu/copier"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/pkg/migrate"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/id_helper"
	pub_sub "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pub-sub"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/context_helper"

	wrapErr "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors/biz"

	models "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/indicator_model"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/services/indicator"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"

	eventfast "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/fast"
	pkgFast "gitlab.mypaas.com.cn/dmp/gopkg/bigdata/fast"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"

	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"

	"github.com/emirpasic/gods/sets/hashset"
	indicatorCache "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/cache/indicator"
	newBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	multiDimModelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/multi_dim_model"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/base"
	modelErr "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/errors"
	ivBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/indicator_view/base"
	logicBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"

	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	pkg "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"
)

// 新增
func (s *IndicatorViewService) AddIndicator(ctx context.Context, project string, req modelProto.AddBusinessOrPhysicsModel) (code string, err error) {
	if err = s.validateAdd(ctx, project, req); err != nil {
		return
	}

	newIndicatorReq := &ivBase.NewIndicatorReq{
		ID:                "",
		Code:              "",
		PhysicalModelCode: "",
		MultiDimModelCode: "",
		Environment:       cBase.ProjectEnvDev,
		Name:              req.Name,
		ViewName:          req.TableName,
		Description:       req.Description,
		SubjectID:         req.SubjectID,
		Content:           "",
		ViewContent:       "",
		Param:             "",
		Resource:          "",
		RefType:           ivBase.AllSelfRefType,
		DevMode:           req.DevMode,
		TableName:         req.TableName,
	}

	newIndicator := ivBase.NewIndicator(newIndicatorReq)
	account, err := context_helper.GetSessionAccount(ctx)
	if err != nil {
		return "", err
	}
	newIndicator.CreatedBy = account
	newIndicator.ModifiedBy = account

	tx, err := s.repo.GetTx(ctx, project)
	if nil != err {
		s.logger.Error("gettx fail:", err, " project:", project)
		return
	}

	defer func() {
		if nil != err {
			if rollErr := tx.Rollback(); nil != rollErr {
				s.logger.Error("rollback err:", rollErr)
			}
		} else {
			if comErr := tx.Commit(); nil != comErr {
				s.logger.Error("Commit err:", comErr)
			}
		}
	}()

	code, err = s.repo.AddIndicator(ctx, project, tx, newIndicator)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewService) getStandardModelEditFlag(ctx context.Context, sourceProjectLevel int, updateDevFields []*ivBase.IndicatorField, oriDevFields []*ivBase.IndicatorField) (editedStandardFlag int) {
	editedStandardFlag = newBase.EditedStandardFlagNo

	oriDevFieldMap := make(map[string]*ivBase.IndicatorField)
	updateDevFieldMap := make(map[string]*ivBase.IndicatorField)
	for _, oriDevField := range oriDevFields {
		oriDevFieldMap[oriDevField.Name] = oriDevField
	}
	for _, updateDevField := range updateDevFields {
		updateDevFieldMap[updateDevField.Name] = updateDevField
	}

	//新增字段
	for _, updateDevField := range updateDevFields {
		_, ok := oriDevFieldMap[updateDevField.Name]
		if !ok {
			editedStandardFlag = newBase.EditedStandardFlagYes
			return
		}
	}

	//删除字段
	for _, oriDevField := range oriDevFields {
		_, ok := updateDevFieldMap[oriDevField.Name]
		if !ok {
			editedStandardFlag = newBase.EditedStandardFlagYes
			return
		}
	}

	//修改字段
	for _, oriDevField := range oriDevFields {
		updateDevField, ok := updateDevFieldMap[oriDevField.Name]
		if !ok {
			continue
		}
		if oriDevField.DimViewData.String != updateDevField.DimViewData.String {
			editedStandardFlag = newBase.EditedStandardFlagYes
			return
		}
		if oriDevField.NameCn != updateDevField.NameCn {
			editedStandardFlag = newBase.EditedStandardFlagYes
			return
		}
		if oriDevField.Description != updateDevField.Description {
			editedStandardFlag = newBase.EditedStandardFlagYes
			return
		}
	}
	return
}

func (s *IndicatorViewService) checkAndUpdateRepeatFieldNameCn(fields []modelProto.UpdateModelDefinitionField) []modelProto.UpdateModelDefinitionField {
	nameCount := make(map[string]int)

	// 遍历字段中文名列表
	for i, field := range fields {
		// 如果字段中文名已经在 map 中存在，则增加计数并给字段中文名添加数字后缀
		if count, ok := nameCount[field.NameCN]; ok {
			newFieldNameCn := fmt.Sprintf("%s(%d)", field.NameCN, count)
			fields[i].NameCN = newFieldNameCn
			count++
			nameCount[field.NameCN] = count
		} else {
			// 如果字段中文名尚未在 map 中出现，则将其添加到 map 中
			nameCount[field.NameCN] = 1
		}
	}
	return fields
}

// 更新
func (s *IndicatorViewService) UpdateIndicator(ctx context.Context, project string, req modelProto.UpdateModelDefinitionReq) (rsp modelProto.UpdateModelDefinitionRsp, err error) {
	// 校验字段
	if err = s.checkUpdateFields(req.Fields); err != nil {
		return
	}

	//检查字段中文名是否重复,是的话把字段中文名加上数字后缀
	req.Fields = s.checkAndUpdateRepeatFieldNameCn(req.Fields)

	devIndicator, err := s.repo.GetIndicatorByCodeAndEnv(ctx, project, req.Code, cBase.ProjectEnvDev)
	if err != nil {
		return
	}
	if devIndicator == nil || devIndicator.Code == "" {
		err = modelErr.NewIndicatorCodeNotExistsErr(req.Code)
		return
	}
	prodIndicator, err := s.repo.GetIndicatorByCodeAndEnv(ctx, project, req.Code, cBase.ProjectEnvProd)
	if err == nil && prodIndicator != nil && prodIndicator.Code != "" {
		if prodIndicator.ViewName != req.TableName {
			err = modelErr.NewReleaseIndicatorCannotModifyName()
			return
		}
	}

	// 暂时兼容
	req.DefinitionID = devIndicator.ID

	// 校验中文名是否重复
	if req.Name != devIndicator.Name {
		if err = s.validateNameCnExist(ctx, project, req.Name, devIndicator.Code); err != nil {
			return
		}
	}
	// 校验英文名是否重复
	if req.Name != devIndicator.Name {
		if err = s.validateNameExist(ctx, project, req.TableName, devIndicator.Code); err != nil {
			return
		}
	}

	// 解析得到viewContent结构体
	viewContent, err := s.getViewContentStruct(req.ViewContent)
	if err != nil {
		return
	}

	//根据方言翻译对应的指标表达式
	err = s.DialectTranslate(ctx, project, viewContent)
	if err != nil {
		return
	}

	// 校验ViewContent内容
	if err = viewContent.ValidViewContent(); err != nil {
		return
	}

	// 填充ViewContent
	req.Resource = entities.StarRocksSaaSResourceType
	if err = viewContent.FillUpExtraSaveInfo(req.Resource); err != nil {
		return
	}

	// 填充维度视图数据
	var dict = make(map[string]*newBase.FieldViewModel)
	for _, field := range req.Fields {
		dict[field.Name] = field.ViewModel
	}
	if err = viewContent.FillUpDimensionViewData(dict); err != nil {
		return
	}

	// 更新关系模型
	if err = s.addOrUpdateMultiDimModel(ctx, project, viewContent, devIndicator); err != nil {
		err = errors.Wrapf(err, "更新关系模型时异常: %v", err)
		return
	}

	// 获取指标视图的字段列表
	indicatorFields, err := s.genIndicatorFields(req.DefinitionID, req.TableName, viewContent, req.Fields)
	if err != nil {
		return
	}

	tx, err := s.repo.GetTx(ctx, project)
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	// 批量更新指标视图字段
	err = s.repo.UpdateIndicatorFieldsUseTx(ctx, tx, req.DefinitionID, req.GroupFieldsOrder, indicatorFields)
	if err != nil {
		return
	}

	if devIndicator.SubjectID != req.SubjectId {
		if err = s.repo.UpdateSubjectByCode(ctx, tx, devIndicator.Code, req.SubjectId); err != nil {
			return
		}
	}

	// 更新开发态指标视图定义
	updateIndicator, err := s.genUpdateIndicator(ctx, req, viewContent)
	if err != nil {
		return
	}
	//获取开发态指标字段
	devIndicatorFields, err := s.repo.GetIndicatorFields(ctx, project, devIndicator.ID)
	if err != nil {
		return
	}

	//获取标准模型的使用标识
	if devIndicator.EditedStandardFlag != newBase.EditedStandardFlagYes {
		if len(updateIndicator.GroupFieldOrder) != len(req.GroupFieldsOrder) {
			updateIndicator.EditedStandardFlag = newBase.EditedStandardFlagYes
		}
		if updateIndicator.Name != devIndicator.Name {
			updateIndicator.EditedStandardFlag = newBase.EditedStandardFlagYes
		}
		if updateIndicator.EditedStandardFlag == newBase.EditedStandardFlagNo {
			updateIndicator.EditedStandardFlag = s.getStandardModelEditFlag(ctx, int(devIndicator.SourceProjectLevel), indicatorFields, devIndicatorFields)
		}
		if updateIndicator.EditedStandardFlag == newBase.EditedStandardFlagNo {
			viewContentByte, _ := json.Marshal(viewContent)
			viewContentStr := string(viewContentByte)
			if devIndicator.ViewContent != viewContentStr {
				updateIndicator.EditedStandardFlag = newBase.EditedStandardFlagYes
			}
		}
	}

	//归档环境不处理该标识
	if global.AppConfig.App.IsArchivingEnv == 1 {
		updateIndicator.EditedStandardFlag = newBase.EditedStandardFlagNo
	}

	err = s.repo.UpdateIndicatorUseTx(ctx, tx, updateIndicator)
	if err != nil {
		return
	}
	if err = s.repo.UpdateModifyStatus(ctx, tx, updateIndicator.Code, modelBase.ModifiedModifyStatus); nil != err {
		return
	}

	err = tx.Commit()
	if err != nil {
		return
	}
	return
}

// 更新物理模型时: 检查
func (s *IndicatorViewService) checkUpdateFields(fields []modelProto.UpdateModelDefinitionField) error {
	// 检查字段名称是否重复、是否有主键字段
	fieldNameMap := make(map[string]bool)
	fieldNameCnMap := make(map[string]bool)

	for _, field := range fields {
		// 中文名、英文名、字段类型、类别为空校验  field.FieldType == "" ||
		if field.Name == "" || field.NameCN == "" {
			return wrapErr.New(wrapErr.INVALID_PARAMS).WithMessage("中文名、英文名或字段类别为空")
		}
		if fieldNameMap[field.Name] {
			return wrapErr.New(biz.MODEL_FIELD_NAME_DUPLICATE, field.Name)
		}
		// if fieldNameCnMap[field.NameCN] {
		// 	return wrapErr.New(biz.MODEL_FIELD_NAME_CN_DUPLICATE, field.NameCN)
		// }
		fieldNameMap[field.Name] = true
		fieldNameCnMap[field.NameCN] = true
	}
	return nil
}

// 发布
func (s *IndicatorViewService) ReleaseIndicator(ctx context.Context, project, account string, req modelProto.ReleaseDefinitionReq) (err error) {
	// 上报天眼事件
	event := eventfast.StartEvent(pkgFast.ReleaseModel, project, account)
	defer func() {
		event.EndEvent(ctx, err)
	}()
	// 获取开发态指标定义
	devIndicator, err := s.repo.GetIndicatorByCodeAndEnv(ctx, project, req.Code, cBase.ProjectEnvDev)
	if err != nil {
		return
	}
	if devIndicator == nil || devIndicator.Code == "" {
		return modelErr.NewIndicatorCodeNotExistsErr(req.Code)
	}
	// 暂时兼容
	req.ID = devIndicator.ID

	// 没有配置信息则无法发布
	if devIndicator.ViewContent == "" {
		return modelErr.NewIndicatorReleaseNoContentErr(req.ID)
	}

	// 获取view_content
	viewContent, err := logicBase.UnmarshalAdsViewContent(devIndicator.ViewContent)
	if err != nil {
		return
	}

	// 获取开发态指标字段
	devIndicatorFields, err := s.repo.GetIndicatorFields(ctx, project, req.ID)
	if err != nil {
		return
	}
	devIndicator.ReleaseComment = req.Comment

	//查询指标字段分组信息
	groupFieldOrder, err := s.repo.GetIndicatorFieldsGroup(ctx, project, req.ID)
	if err != nil {
		return
	}
	devIndicator.GroupFieldOrder = groupFieldOrder

	// 根据度量的可分析维度，检查是不是所有的维度定义都可以分析
	arePublic, err := s.allDimensionArePublic(ctx, project, viewContent, devIndicatorFields)
	if err != nil {
		return
	}
	devIndicator.AllDimensionArePublic = 0
	if arePublic {
		devIndicator.AllDimensionArePublic = 1
	}

	// 发布依赖的模型
	if err = s.releaseDependence(ctx, project, viewContent); err != nil {
		return
	}

	// 记录天眼上报数据
	event.RecordBizParams(s.recorder.GenerateReleaseModelFastBizContent(ctx, project, devIndicator, devIndicatorFields))
	if err = s.ReleaseDevIndicator(ctx, project, devIndicator, devIndicatorFields, req.IsImport); nil != err {
		return err
	}

	// 如果全部都可以分析，才创建视图
	if arePublic {
		// 创建mysql视图
		err = s.createMySqlView(ctx, project, devIndicator.TableName, devIndicatorFields, viewContent, devIndicator.DevMode)
	} else {
		s.logger.Infof("所有的维度定义不全是可分析的，需要检查是否已有视图[%s], 存在则删除", devIndicator.TableName)
		var migrateEngine migrate.Migrate
		migrateEngine, err = s.getMigrateEngine(ctx, project)
		if err != nil {
			return err
		}
		err = migrateEngine.DropViewIfExists(ctx, devIndicator.TableName)
		if err != nil {
			return err
		}
	}

	// 删除缓存
	c := indicatorCache.NewCacheImpl(project)
	c.DelProdAdsViewByCode(ctx, devIndicator.Code)
	s.tableCache.DelTableMeta(ctx, project, devIndicator.ViewName)
	s.tableCache.DelTableSubject(ctx, project, devIndicator.ViewName)
	return
}

func (s *IndicatorViewService) OfflineIndicator(ctx context.Context, project string, req modelProto.OfflineDefinitionReq) (err error) {
	// 首先获取发布态指标
	prodIndicator, err := s.repo.GetIndicatorByCodeAndEnv(ctx, project, req.Code, cBase.ProjectEnvProd)
	if err != nil {
		return
	}
	if prodIndicator == nil || prodIndicator.Code == "" {
		return modelErr.NewIndicatorCodeNotReleaseErr(req.Code)
	}
	// 获取view_content
	viewContent, err := logicBase.UnmarshalAdsViewContent(prodIndicator.ViewContent)
	if err != nil {
		return
	}

	// 先下线汇总视图
	if err = s.offlineIndicator(ctx, project, prodIndicator); err != nil {
		return
	}

	// 再下线多维模型
	if viewContent != nil && viewContent.Dependence.Category == newBase.MultiDimCate {
		offlineReq := &multiDimModelProto.OfflineMultiDimModelParams{
			Code: viewContent.Dependence.CodeId,
		}
		if err = s.multiDimModelS.OfflineMultiDimModel(ctx, project, offlineReq); err != nil {
			return
		}
	}
	return
}

func (s *IndicatorViewService) offlineIndicator(ctx context.Context, project string, prodIndicator *ivBase.Indicator) (err error) {
	// 开启事务, 写入数据
	tx, err := s.repo.GetTx(ctx, project)
	if err != nil {
		return
	}

	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				s.logger.Error("Rollback err:", errRollback)
			}
		} else {
			if comErr := tx.Commit(); nil != comErr {
				s.logger.Error("Commit err:", comErr)
			}
		}
	}()

	// 删除发布态字段
	if err = s.repo.DeleteIndicatorFieldUseTx(ctx, tx, prodIndicator.ID); err != nil {
		return
	}
	// 删除发布态指标
	if err = s.repo.DeleteIndicatorUseTx(ctx, tx, prodIndicator.ID); err != nil {
		return
	}
	// 更新发布状态
	if err = s.repo.UpdateReleaseStatus(ctx, tx, prodIndicator.Code, string(modelBase.Offline)); nil != err {
		return
	}
	// 更新编辑状态
	if err = s.repo.UpdateModifyStatus(ctx, tx, prodIndicator.Code, modelBase.InitModifyStatus); nil != err {
		return
	}
	return
}

func (s *IndicatorViewService) allDimensionArePublic(ctx context.Context, project string,
	viewData *logicBase.AdsViewContent, devIndicatorFields []*ivBase.IndicatorField) (flag bool, err error) {
	var viewContent = &logicBase.AdsViewContent{}
	err = copier.Copy(viewContent, viewData)
	if err != nil {
		return
	}

	var dimensionSet = hashset.New()
	viewContent.Dimensions = make([]rpc_call.Prop, 0)
	for _, v := range devIndicatorFields {
		// 排除度量的干扰
		if v.DimType == string(modelBase.IndicatorDimType) {
			continue
		}
		dimensionSet.Add(v.FieldCode) // 将维度定义code添加到集合中
		var viewModel = newBase.FieldViewModel{}
		err = json.Unmarshal([]byte(v.DimViewData.String), &viewModel)
		if err != nil {
			return
		}
		viewContent.Dimensions = append(viewContent.Dimensions, rpc_call.Prop{
			FieldCode: v.FieldCode,
			FieldType: v.FieldType,
			Alias:     v.Name,
			PropName:  v.Name,
			NameCn:    v.NameCn,
			ViewModel: &viewModel,
		})
	}
	var viewContentStr []byte
	viewContentStr, err = json.Marshal(viewContent)
	if err != nil {
		return
	}
	var analyticMetaInfo *ads_common.GetPublicDimTableRspData
	analyticMetaInfo, err = s.indicatorModelingService.GetPublicDimTable(ctx, &ads_common.GetPublicDimTableReq{
		ViewContent: string(viewContentStr),
		ProjectCode: project,
	})
	if err != nil {
		return
	}
	if analyticMetaInfo == nil {
		flag = false
		return
	}
	var analyticDimensionSet = hashset.New()
	for _, dimension := range analyticMetaInfo.Dimensions {
		analyticDimensionSet.Add(dimension.Code)
	}
	if dimensionSet.Difference(analyticDimensionSet).Empty() {
		flag = true
	}
	return
}

func (s *IndicatorViewService) releaseDependence(ctx context.Context, project string, viewContent *logicBase.AdsViewContent) error {
	if viewContent.Dependence.Category != newBase.MultiDimCate {
		return nil
	}
	releaseReq := &multiDimModelProto.ReleaseMultiDimModelParams{
		Code: viewContent.Dependence.CodeId,
	}
	return s.multiDimModelS.ReleaseMultiDimModel(ctx, project, releaseReq)
}

func (s *IndicatorViewService) ReleaseDevIndicator(ctx context.Context, project string, devIndicator *ivBase.Indicator, devIndicatorFields []*ivBase.IndicatorField, isImport bool) (err error) {

	account, err := context_helper.GetSessionAccount(ctx)
	if err != nil {
		return
	}

	// 获取可能存在的旧发布态指标定义
	dbReleaseIndicator, err := s.repo.GetIndicatorByCodeAndEnv(ctx, project, devIndicator.Code, cBase.ProjectEnvProd)
	if err != nil {
		return
	}

	//操作日志埋点上报
	defer func() {
		ip := ""
		account, _ := context_helper.GetSessionAccount(ctx)
		if ginCtx, ok := ctx.(*gin.Context); ok {
			ip = ginCtx.ClientIP()
		}
		opLogContent := newBase.OpLogContent{
			ProjectCode: project,
			UserAccount: account,
			ClientIp:    ip,
		}
		action := ""
		if dbReleaseIndicator != nil && dbReleaseIndicator.ID != "" {
			opLogContent.OpEventType = newBase.OpEventTypeModelUpdate
			action = "更新"
		} else {
			opLogContent.OpEventType = newBase.OpEventTypeModelAdd
			action = "新增"
		}
		if err != nil {
			opLogContent.Description = fmt.Sprintf("[%s]模型表【%s】失败", action, devIndicator.ViewName)
		} else {
			opLogContent.Description = fmt.Sprintf("[%s]模型表【%s】成功", action, devIndicator.ViewName)
		}
		if !isImport {
			pub_sub.TriggerEvent(ctx, models.OpLogPhysicalModel, opLogContent)
		}
	}()

	// 更改发布态的ID生产规则
	var dbReleaseIndicatorID = indicator.GenIndicatorIDByCode(devIndicator.Code, global.Prod)
	if dbReleaseIndicator != nil && dbReleaseIndicator.ID != "" {
		dbReleaseIndicatorID = dbReleaseIndicator.ID
	}

	// 创建发布版本
	// 组装新的发布态指标定义
	newReleaseIndicator, err := s.genReleaseIndicator(devIndicator, dbReleaseIndicatorID, devIndicator.ReleaseComment)
	if err != nil {
		return
	}
	newReleaseIndicator.CreatedBy = account
	newReleaseIndicator.ModifiedBy = account

	if dbReleaseIndicator != nil && dbReleaseIndicator.ID != "" {
		newReleaseIndicator.CreatedOn = dbReleaseIndicator.CreatedOn
	}

	// 组装新的发布态指标字段
	newReleaseIndicatorFields, err := s.genReleaseIndicatorFields(newReleaseIndicator.ID, devIndicatorFields, devIndicator)
	if err != nil {
		return
	}

	defer func() {
		if err != nil {
			return
		}
		pub_sub.TriggerEvent(ctx, models.ReleaseIndicatorModel, models.ReleaseIndicatorModelEvent{
			Project:               project,
			DevModel:              devIndicator,
			DevModelFields:        devIndicatorFields,
			NewReleaseModel:       newReleaseIndicator,
			NewReleaseModelFields: newReleaseIndicatorFields,
			IsImport:              isImport,
		})
	}()

	// 开启事务, 写入数据
	tx, err := s.repo.GetTx(ctx, project)
	if err != nil {
		return
	}

	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				s.logger.Error("Rollback err:", errRollback)
			}
		} else {
			if comErr := tx.Commit(); nil != comErr {
				s.logger.Error("Commit err:", comErr)
			}
		}
	}()

	// 创建新的发布态指标定义
	_, err = s.repo.AddIndicatorUseTx(ctx, tx, newReleaseIndicator)
	if err != nil {
		return
	}

	// 批量创建发布态指标字段
	err = s.repo.UpdateIndicatorFieldsUseTx(ctx, tx, newReleaseIndicator.ID, devIndicator.GroupFieldOrder, newReleaseIndicatorFields)
	if err != nil {
		return
	}

	var releaseStatus = base.Release
	var version int32
	// 导入优化, 导入时不生成历史版本
	if !isImport {
		// 创建历史版本
		// 组装历史版本指标定义
		version, err = s.repo.GetNextVersion(ctx, tx, devIndicator.Code)
		if err != nil {
			return
		}
		if version <= 1 {
			releaseStatus = base.FirstRelease
		}

		var newHistoryIndicator *ivBase.Indicator
		var newHistoryIndicatorFields []*ivBase.IndicatorField

		newHistoryIndicator, err = s.genHistoryIndicator(newReleaseIndicator, version)
		if err != nil {
			return
		}

		// 创建历史版本指标定义
		_, err = s.repo.AddIndicatorUseTx(ctx, tx, newHistoryIndicator)
		if err != nil {
			return
		}

		// 组装历史版本指标字段
		newHistoryIndicatorFields, err = s.genHistoryIndicatorFields(newHistoryIndicator.ID, newReleaseIndicatorFields)
		if err != nil {
			return
		}

		// 创建历史版本指标字段
		err = s.repo.UpdateIndicatorFieldsUseTx(ctx, tx, newHistoryIndicator.ID, devIndicator.GroupFieldOrder, newHistoryIndicatorFields)
		if err != nil {
			return
		}
	}
	if err = s.repo.BatchUpdateStatus(ctx, tx, devIndicator.Code, releaseStatus, modelBase.InitModifyStatus, int(modelBase.OnShelfApplyStatus), devIndicator.AllDimensionArePublic); err != nil {
		return
	}

	return nil
}

// 删除
func (s *IndicatorViewService) DeleteIndicator(ctx context.Context, project string, req modelProto.DeleteDefinitionReq) (err error) {
	// 获取开发态指标定义
	devIndicator, err := s.repo.GetIndicatorByCodeAndEnv(ctx, project, req.Code, cBase.ProjectEnvDev)
	if err != nil {
		return
	}
	if devIndicator == nil || devIndicator.Code == "" {
		return modelErr.NewIndicatorCodeNotExistsErr(req.Code)
	}
	// 暂时兼容
	req.ID = devIndicator.ID

	// 操作日志埋点上报
	defer func() {
		ip := ""
		account, _ := context_helper.GetSessionAccount(ctx)
		if ginCtx, ok := ctx.(*gin.Context); ok {
			ip = ginCtx.ClientIP()
		}
		opLogContent := newBase.OpLogContent{
			ProjectCode: project,
			UserAccount: account,
			OpEventType: newBase.OpEventTypeModelDel,
			ClientIp:    ip,
		}
		if err != nil {
			opLogContent.Description = fmt.Sprintf("删除模型表【%s】失败", devIndicator.ViewName)
		} else {
			opLogContent.Description = fmt.Sprintf("删除模型表【%s】成功", devIndicator.ViewName)
		}
		pub_sub.TriggerEvent(ctx, models.OpLogPhysicalModel, opLogContent)
	}()

	if devIndicator.ViewContent != "" {
		var viewContent *logicBase.AdsViewContent
		// 删除多维模型
		viewContent, err = logicBase.UnmarshalAdsViewContent(devIndicator.ViewContent)
		if err != nil {
			return
		}
		if viewContent != nil && viewContent.Dependence.Category == newBase.MultiDimCate {
			if err = s.multiDimModelS.DeleteMultiDimModel(ctx, project, viewContent.Dependence.CodeId); err != nil {
				return
			}
		}
	}

	// 先删除删除视图，成功了再清理元数据库
	err = s.dropViewTable(ctx, project, devIndicator.TableName)
	if err != nil {
		return
	}

	tx, err := s.repo.GetTx(ctx, project)
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	// 删除开发/历史态数据
	err = s.repo.DeleteIndicatorFieldUseTxByCode(ctx, tx, devIndicator.Code)
	if err != nil {
		return
	}
	err = s.repo.DeleteIndicatorUseTxByCode(ctx, tx, devIndicator.Code)
	if err != nil {
		return
	}
	err = s.repo.DeleteModelCollect(ctx, tx, devIndicator.Code)
	if err != nil {
		return
	}
	err = tx.Commit()
	if err != nil {
		return
	}
	s.tableCache.DelTableMeta(ctx, project, devIndicator.ViewName)
	s.tableCache.DelTableSubject(ctx, project, devIndicator.ViewName)
	pub_sub.TriggerEvent(ctx, models.DeleteIndicatorModel, models.DeleteIndicatorModelEvent{
		Project:  project,
		DevModel: devIndicator,
	})
	return
}

func (s *IndicatorViewService) dropViewTable(ctx context.Context, project string, tableName string) error {
	migrateEngine, err := s.getMigrateEngine(ctx, project)
	if err != nil {
		return err
	}
	return migrateEngine.DropViewIfExists(ctx, tableName)
}

func (s *IndicatorViewService) validateAdd(ctx context.Context, project string, req modelProto.AddBusinessOrPhysicsModel) error {
	// 名称校验
	if req.Name == "" || req.TableName == "" {
		return modelErr.NewIndicatorNameNotExistsErr()
	}
	// 名称重复校验
	if err := s.validateNameExist(ctx, project, req.TableName, ""); err != nil {
		return err
	}

	// 中文名称重复校验
	if err := s.validateNameCnExist(ctx, project, req.Name, ""); err != nil {
		return err
	}

	//判断视图或者表是否存在
	if err := s.validateViewTableExist(ctx, project, req.TableName); err != nil {
		return err
	}
	return nil
}

func (s *IndicatorViewService) validateViewTableExist(ctx context.Context, project string, viewName string) error {
	migrateEngine, err := s.getMigrateEngine(ctx, project)
	if err != nil {
		return err
	}

	isExist, err := migrateEngine.HasView(ctx, viewName)
	if err != nil {
		return err
	}
	if isExist {
		return modelErr.NewIndicatorNameExistsErr(viewName)
	}
	isExist, err = migrateEngine.HasTable(ctx, viewName)
	if err != nil {
		return err
	}
	if isExist {
		return modelErr.NewIndicatorNameExistsErr(viewName)
	}

	return nil
}

func (s *IndicatorViewService) validateNameExist(ctx context.Context, project, name string, excludeCode string) error {
	isExist, err := s.repo.ExistName(ctx, project, name, excludeCode)
	if err != nil {
		return err
	}
	if isExist {
		return modelErr.NewIndicatorNameExistsErr(name)
	}
	isExist, err = s.repo.ExistPhysicalModelName(ctx, project, name, excludeCode)
	if err != nil {
		return err
	}
	if isExist {
		return modelErr.NewIndicatorNameExistsErr(name)
	}
	return nil
}

func (s *IndicatorViewService) validateNameCnExist(ctx context.Context, project, name string, excludeCode string) error {
	isExist, err := s.repo.ExistNameCn(ctx, project, name, excludeCode)
	if err != nil {
		return err
	}
	if isExist {
		return modelErr.NewIndicatorNameExistsErr(name)
	}
	return nil
}

func (s *IndicatorViewService) getViewContentStruct(viewContent interface{}) (vc *logicBase.AdsViewContent, err error) {
	viewContentByte, err := json.Marshal(viewContent)
	if err != nil {
		return nil, err
	}
	viewContentStr := string(viewContentByte)
	adsViewContent, err := logicBase.UnmarshalAdsViewContent(viewContentStr)
	if err != nil {
		return nil, err
	}

	for i, f := range adsViewContent.CustomFieldDimensions {
		if f.Mode != rpc_call.AdvanceProp {
			continue
		}
		err = json.Unmarshal([]byte(f.DialectSettingContentStr), &adsViewContent.CustomFieldDimensions[i].DialectSettingContent)
		if err != nil {
			return nil, err
		}
	}
	return adsViewContent, nil
}

func (s *IndicatorViewService) genIndicatorFields(indicatorID string, tableName string, viewContent *logicBase.AdsViewContent, definitionFields []modelProto.UpdateModelDefinitionField) ([]*ivBase.IndicatorField, error) {
	// 获取查询字段的返回顺序
	//以field为准，组装field对应的指标配置信息
	definitionFieldMap := make(map[string]modelProto.UpdateModelDefinitionField)
	for _, f := range definitionFields {
		definitionFieldMap[f.NameCN] = f
	}
	var indicatorFieldSlice []*ivBase.IndicatorField

	indicatorDefineMap := map[string]rpc_call.Prop{}

	for _, indicatorDefine := range viewContent.CustomFieldDimensions {
		indicatorDefineMap[indicatorDefine.FieldCode] = indicatorDefine
		if indicatorDefine.Mode == rpc_call.AdvanceProp && s.regCheckExistAggFunc(indicatorDefine.PropRaw) == ivBase.FieldAggOff {
			return nil, utils.NewUserError("指标<%s>未设置聚合函数", indicatorDefine.NameCn)
		}
	}
	for _, f := range definitionFields {
		if f.Code == "" && f.DimType == modelBase.IndicatorDimType { //code字段为指标对外唯一标识，如果为空则生成一个
			f.Code = id_helper.GenIndicatorCode(tableName, f.Name)
		}

		newFieldReq := &ivBase.NewIndicatorFieldReq{
			ID:                    f.ID,
			IndicatorID:           indicatorID,
			Name:                  f.Name,
			NameCn:                f.NameCN,
			Description:           f.Description,
			ValueComment:          f.ValueComment,
			Expression:            "",
			FieldSource:           ivBase.Custom,
			AggOn:                 ivBase.FieldAggOn,
			Rank:                  f.Rank,
			FieldType:             f.FieldType,
			IndicatorBusinessCode: f.IndicatorBusinessCode,
			Code:                  f.Code,
			FieldCode:             f.FieldCode,
			DimType:               string(f.DimType),
			FieldBusinessType:     f.FieldBusinessType,
			IsPrimary:             int(f.IsPrimary),
			PrimaryRank:           int(f.PrimaryRank),
			Scale:                 f.Scale,
			Length:                int(f.Length),
			BusinessModelFieldID:  f.BusinessModelFieldID,
			GroupName:             f.GroupName,
			ParentId:              f.ParentId,
		}

		if d, ok := indicatorDefineMap[f.FieldCode]; ok {
			newFieldReq.Expression = d.PropRaw
			newFieldReq.IndicatorBusinessCode = d.IndicatorBusinessCode
			// 将字段的计算公式刷新为moql翻译后的内容
			if f.ViewModel.FormulaCalculation != nil {
				f.ViewModel.FormulaCalculation.OriginMoSql = d.DialectSettingContent.OriginMoSql
				f.ViewModel.FormulaCalculation.OriginMySql = d.DialectSettingContent.OriginMySql
				f.ViewModel.FormulaCalculation.OriginDmSql = d.DialectSettingContent.OriginDmSql
				f.ViewModel.FormulaCalculation.MySql = d.DialectSettingContent.MySql
				f.ViewModel.FormulaCalculation.DmSql = d.DialectSettingContent.DmSql
				f.ViewModel.FormulaCalculation.DialectSetting = d.DialectSettingContent.DialectSetting
			}
		}

		dimViewDataJson, err := json.Marshal(f.ViewModel)
		if err != nil {
			return nil, err
		}
		newFieldReq.DimViewData = string(dimViewDataJson)

		newField := ivBase.NewIndicatorField(newFieldReq)
		indicatorFieldSlice = append(indicatorFieldSlice, newField)
	}
	return indicatorFieldSlice, nil
}

func (s *IndicatorViewService) genUpdateIndicator(ctx context.Context, req modelProto.UpdateModelDefinitionReq, viewContent *logicBase.AdsViewContent) (*ivBase.Indicator, error) {
	// 指标视图依赖的模型code
	dependence := viewContent.Dependence
	if dependence == nil {
		err := modelErr.NewIndicatorReleaseNoContentErr(req.Code)
		return nil, err
	}
	var multiDimCode string
	if strings.ToLower(string(dependence.Category)) == strings.ToLower(string(modelBase.MultiDimCate)) {
		multiDimCode = dependence.CodeId
	}

	viewContentStr := ""
	if req.ViewContent != nil {
		viewContentByte, err := json.Marshal(viewContent)
		if err != nil {
			return nil, err
		}
		viewContentStr = string(viewContentByte)
	}

	content, err := json.Marshal(req.Content)
	if err != nil {
		return nil, errors.New("json序列化content出错")
	}

	physicalModelCode := dependence.CodeId
	if multiDimCode != "" {
		physicalModelCode = ""
	}

	newIndicatorReq := &ivBase.NewIndicatorReq{
		ID:                req.DefinitionID,
		Code:              "",
		PhysicalModelCode: physicalModelCode,
		MultiDimModelCode: multiDimCode,
		Environment:       cBase.ProjectEnvDev,
		Name:              req.Name,
		Description:       req.Description,
		SubjectID:         req.SubjectId,
		Content:           string(content),
		ViewContent:       viewContentStr,
		Param:             "",
		TableName:         req.TableName,
		ViewName:          req.TableName,
	}
	updateIndicator := ivBase.NewIndicator(newIndicatorReq)
	if req.TableName != "" {
		updateIndicator.ViewName = req.TableName
	}

	updateIndicator.ModifyStatus = modelBase.ModifiedModifyStatus

	disableDimensional, err := json.Marshal(req.DisableDimensional)
	if err != nil {
		return nil, errors.New("json序列化禁用维度出错")
	}

	updateIndicator.DisableDimensional = string(disableDimensional)

	account, err := context_helper.GetSessionAccount(ctx)
	if err != nil {
		return nil, err
	}
	updateIndicator.ModifiedBy = account

	return updateIndicator, nil
}

func (s *IndicatorViewService) genReleaseIndicator(devIndicator *ivBase.Indicator, dbReleaseIndicatorID string, comment string) (*ivBase.Indicator, error) {
	// 保持和之前已发布的指标ID一致
	var releaseIndicatorID string
	if dbReleaseIndicatorID != "" {
		releaseIndicatorID = dbReleaseIndicatorID
	}

	newIndicatorReq := &ivBase.NewIndicatorReq{
		ID:                 releaseIndicatorID,
		Code:               devIndicator.Code,
		PhysicalModelCode:  devIndicator.PhysicalModelCode,
		MultiDimModelCode:  devIndicator.MultiDimModelCode,
		Environment:        cBase.ProjectEnvProd,
		Name:               devIndicator.Name,
		ViewName:           devIndicator.ViewName,
		TableName:          devIndicator.TableName,
		DevMode:            devIndicator.DevMode,
		Description:        devIndicator.Description,
		SubjectID:          devIndicator.SubjectID,
		Content:            devIndicator.Content,
		ViewContent:        devIndicator.ViewContent,
		Param:              devIndicator.Param,
		ReleaseComment:     comment,
		Resource:           devIndicator.Resource,
		RefType:            devIndicator.RefType,
		DisableDimensional: devIndicator.DisableDimensional,
		SourceProjectLevel: devIndicator.SourceProjectLevel,
		EditedStandardFlag: devIndicator.EditedStandardFlag,
	}
	releaseIndicator := ivBase.NewIndicator(newIndicatorReq)
	releaseIndicator.CreatedBy = devIndicator.CreatedBy
	releaseIndicator.CreatedOn = db.NullTime{
		NullTime: mysql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
	}
	return releaseIndicator, nil
}

func (s *IndicatorViewService) genHistoryIndicator(releaseIndicator *ivBase.Indicator, version int32) (*ivBase.Indicator, error) {
	newIndicatorReq := &ivBase.NewIndicatorReq{
		ID:                 pkg.NewMysqlID(),
		Code:               releaseIndicator.Code,
		PhysicalModelCode:  releaseIndicator.PhysicalModelCode,
		MultiDimModelCode:  releaseIndicator.MultiDimModelCode,
		Environment:        cBase.ProjectEnvHistory,
		Name:               releaseIndicator.Name,
		ViewName:           releaseIndicator.ViewName,
		Description:        releaseIndicator.Description,
		SubjectID:          releaseIndicator.SubjectID,
		Content:            releaseIndicator.Content,
		ViewContent:        releaseIndicator.ViewContent,
		Param:              releaseIndicator.Param,
		Version:            version,
		ReleaseComment:     releaseIndicator.ReleaseComment,
		Resource:           releaseIndicator.Resource,
		RefType:            releaseIndicator.RefType,
		DisableDimensional: releaseIndicator.DisableDimensional,
	}
	historyIndicator := ivBase.NewIndicator(newIndicatorReq)
	return historyIndicator, nil
}

func (s *IndicatorViewService) genReleaseIndicatorFields(releaseIndicatorID string, devIndicatorFields []*ivBase.IndicatorField, devIndicator *ivBase.Indicator) ([]*ivBase.IndicatorField, error) {
	var result []*ivBase.IndicatorField

	filedIdMap := map[string]string{}
	for _, devIndicatorField := range devIndicatorFields {
		newFieldReq := &ivBase.NewIndicatorFieldReq{
			ID:                    pkg.NewMysqlID(),
			IndicatorID:           releaseIndicatorID,
			Name:                  devIndicatorField.Name,
			NameCn:                devIndicatorField.NameCn,
			Description:           devIndicatorField.Description,
			ValueComment:          devIndicatorField.ValueComment,
			Expression:            devIndicatorField.Expression,
			FieldSource:           devIndicatorField.FieldSource,
			AggOn:                 devIndicatorField.AggOn,
			Rank:                  devIndicatorField.Rank,
			FieldType:             devIndicatorField.FieldType,
			IndicatorBusinessCode: devIndicatorField.IndicatorBusinessCode,
			Code:                  devIndicatorField.Code,
			FieldCode:             devIndicatorField.FieldCode,
			DimViewData:           devIndicatorField.DimViewData.String,
			DimType:               devIndicatorField.DimType,
			FieldBusinessType:     devIndicatorField.FieldBusinessType,
			IsPrimary:             devIndicatorField.IsPrimary,
			PrimaryRank:           devIndicatorField.PrimaryRank,
			Scale:                 devIndicatorField.Scale,
			Length:                devIndicatorField.Length,
			BusinessModelFieldID:  devIndicatorField.BusinessModelFieldId,
			GroupName:             devIndicatorField.GroupName,
			ParentId:              devIndicatorField.ParentId,
		}
		filedIdMap[devIndicatorField.ID] = newFieldReq.ID
		newField := ivBase.NewIndicatorField(newFieldReq)
		result = append(result, newField)
	}

	//调整发布字段排序分组里的id
	for _, group := range devIndicator.GroupFieldOrder {
		for i, devFieldId := range group.FieldIDs {
			group.FieldIDs[i] = filedIdMap[devFieldId]
		}
	}

	return result, nil
}

func (s *IndicatorViewService) genHistoryIndicatorFields(historyIndicatorID string, releaseIndicatorFields []*ivBase.IndicatorField) ([]*ivBase.IndicatorField, error) {
	var result []*ivBase.IndicatorField
	for _, field := range releaseIndicatorFields {
		newFieldReq := &ivBase.NewIndicatorFieldReq{
			ID:                    pkg.NewMysqlID(),
			IndicatorID:           historyIndicatorID,
			Name:                  field.Name,
			NameCn:                field.NameCn,
			Description:           field.Description,
			ValueComment:          field.ValueComment,
			Expression:            field.Expression,
			FieldSource:           field.FieldSource,
			AggOn:                 field.AggOn,
			Rank:                  field.Rank,
			FieldType:             field.FieldType,
			IndicatorBusinessCode: field.IndicatorBusinessCode,
			Code:                  field.Code,
			DimViewData:           field.DimViewData.String,
			DimType:               field.DimType,
			FieldBusinessType:     field.FieldBusinessType,
			IsPrimary:             field.IsPrimary,
			PrimaryRank:           field.PrimaryRank,
			Scale:                 field.Scale,
			Length:                field.Length,
			BusinessModelFieldID:  field.BusinessModelFieldId,
			GroupName:             field.GroupName,
			ParentId:              field.ParentId,
		}
		newField := ivBase.NewIndicatorField(newFieldReq)
		result = append(result, newField)
	}
	return result, nil
}

// 检查是否使用聚合函数
func (s *IndicatorViewService) regCheckExistAggFunc(propRaw string) (isAggOn ivBase.IndicatorFieldAggStatus) {
	isAggOn = ivBase.FieldAggOff
	var matchFuncNameRes []string
	result := modelProto.CustomExprKwPattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range result {
		if len(matchItem) > 1 {
			matchFuncNameRes = append(matchFuncNameRes, matchItem[1])
		}
	}
	for _, funcName := range matchFuncNameRes {
		upperFuncName := strings.ToUpper(funcName)
		_, ok := modelProto.AggFuncMap[upperFuncName]
		if ok {
			isAggOn = ivBase.FieldAggOn
			break
		}
	}
	return
}

func (s *IndicatorViewService) DialectTranslate(ctx context.Context, project string, viewContent *logicBase.AdsViewContent) (err error) {
	//根据方言 翻译对应的指标表达式
	resourceInfo, err := s.dapCommon.GetStorageResources(ctx, project)
	if err != nil {
		return
	}

	for i, customerFieldInfo := range viewContent.CustomFieldDimensions {
		if customerFieldInfo.Mode != rpc_call.AdvanceProp {
			continue
		}
		if customerFieldInfo.DialectSettingContent.DialectSetting == global.MoSqlDialectSetting {

			translatedSql, err1 := s.GetIndicatorTranslateExpress(ctx, customerFieldInfo.DialectSettingContent.OriginMoSql, moql.DmDialect, entities.RDSResourceType, viewContent.Variables)
			if err1 != nil {
				err = err1
				return
			}
			viewContent.CustomFieldDimensions[i].DialectSettingContent.DmSql = translatedSql
			//todo 暂时没有mysql
			viewContent.CustomFieldDimensions[i].DialectSettingContent.MySql = customerFieldInfo.DialectSettingContent.OriginMoSql
		} else {
			viewContent.CustomFieldDimensions[i].DialectSettingContent.MySql = customerFieldInfo.DialectSettingContent.OriginMySql
			viewContent.CustomFieldDimensions[i].DialectSettingContent.DmSql = customerFieldInfo.DialectSettingContent.OriginDmSql
		}
		if resourceInfo.ResourceType == string(store.ResourceDM) || resourceInfo.ResourceType == string(store.ResourceDMSaas) {
			viewContent.CustomFieldDimensions[i].PropRaw = viewContent.CustomFieldDimensions[i].DialectSettingContent.DmSql
		} else {
			viewContent.CustomFieldDimensions[i].PropRaw = viewContent.CustomFieldDimensions[i].DialectSettingContent.MySql
		}
		dialectSetting, _ := json.Marshal(viewContent.CustomFieldDimensions[i].DialectSettingContent)
		viewContent.CustomFieldDimensions[i].DialectSettingContentStr = string(dialectSetting)
	}
	return nil
}

func (s *IndicatorViewService) transPropRawToCommonSql(propRaw string, resourceType entities.ProjectResourceType) string {
	return "SELECT " + injection.RegMatchAndReplaceTableAndField(propRaw, resourceType)
}

func (s *IndicatorViewService) GetIndicatorTranslateExpress(ctx context.Context, propRaw string, dialect moql.MoqlDialect, resourceType entities.ProjectResourceType, vars []rpc_call.Variable) (translatedExpress string, err error) {
	//拿到变量的默认值Map集合
	var replacer injection.VariableReplacerI = injection.NewVariableReplacer(true, vars, nil)
	varValueMap, err := replacer.GetExprVariableAllValueMap(propRaw)
	if err != nil {
		return
	}

	//替换变量的默认值
	propRaw, err = s.replaceExprWithDefaultValue(ctx, propRaw, vars)

	//将propRaw转成能进行翻译的语句
	translateSql := s.transPropRawToCommonSql(propRaw, resourceType)
	//调用接口分别翻译成 dm 和 mysql语法
	result, err := s.moqlService.Translate(ctx, dialect, translateSql)
	if err != nil {
		return
	}
	if result.Success {
		result.Sql = injection.RegReplaceTableAndFieldToRawExpress(result.Sql, propRaw, dialect, varValueMap)
		result.Sql, _ = strings.CutPrefix(result.Sql, "SELECT")
		result.Sql, _ = strings.CutPrefix(result.Sql, "select")
	} else {
		err = errors.New(strings.Join(result.Errors, ","))
		return
	}
	return result.Sql, nil
}

func (s *IndicatorViewService) addOrUpdateMultiDimModel(ctx context.Context, project string,
	viewContent *logicBase.AdsViewContent, indicator *ivBase.Indicator) error {
	if viewContent.Dependence.Category != newBase.MultiDimCate {
		return nil
	}
	if strings.TrimSpace(viewContent.Dependence.Name) == "" {
		viewContent.Dependence.Name = fmt.Sprintf("%s-relational-model", indicator.Name)
	}
	if strings.TrimSpace(viewContent.Dependence.TableName) == "" {
		viewContent.Dependence.TableName = indicator.ViewName
	}
	var modelCode = viewContent.Dependence.CodeId
	_, err := s.multiDimModelS.GetMultiDimModelByCode(ctx, project, modelCode, global.Dev)
	if wrapErr.IsCode(err, biz.MODEL_RELATION_NOT_FOUND) {
		var req = &multiDimModelProto.AddMultiDimModelParams{
			SubjectID:   indicator.SubjectID,
			Name:        viewContent.Dependence.TableName,
			NameCn:      viewContent.Dependence.Name,
			Code:        viewContent.Dependence.CodeId,
			ViewContent: viewContent.Dependence.RelationalModel,
		}
		_, addErr := s.multiDimModelS.AddMultiDimModel(ctx, project, req)
		return addErr
	} else if err == nil {
		var req = &multiDimModelProto.UpdateMultiDimModelParams{
			SubjectID:   indicator.SubjectID,
			Code:        viewContent.Dependence.CodeId,
			NameCn:      viewContent.Dependence.Name,
			ViewContent: viewContent.Dependence.RelationalModel,
		}
		return s.multiDimModelS.UpdateMultiDimModel(ctx, project, req)
	} else {
		return err
	}
}
