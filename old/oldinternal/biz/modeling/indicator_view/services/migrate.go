package services

import (
	"context"
	"encoding/json"
	"fmt"
	dap_common "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	pkg "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/pkg/log_entry"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/pkg/migrate"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	ivBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/indicator_view/base"
	logicBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"
)

func (s *IndicatorViewService) createMySqlView(ctx context.Context, project string, tableName string,
	devIndicatorFields []*ivBase.IndicatorField, viewContent *logicBase.AdsViewContent, devMode string) error {
	devIndicatorFieldsMap := make(map[string]*ivBase.IndicatorField)
	for _, v := range devIndicatorFields {
		devIndicatorFieldsMap[v.FieldCode] = v
	}

	for i, v := range viewContent.CustomFieldDimensions {
		viewContent.CustomFieldDimensions[i].Alias = devIndicatorFieldsMap[v.FieldCode].Name
	}
	viewContent.Dimensions = []rpc_call.Prop{}
	for _, v := range devIndicatorFields {
		if v.DimType == string(modelBase.DimensionDimType) {
			viewModel := base.FieldViewModel{}
			json.Unmarshal([]byte(v.DimViewData.String), &viewModel)
			viewContent.Dimensions = append(viewContent.Dimensions, rpc_call.Prop{
				FieldCode: v.FieldCode,
				Alias:     v.Name,
				PropName:  v.Name,
				NameCn:    v.NameCn,
				ObjName:   tableName,
				ViewModel: &viewModel,
			})
		}
	}

	req := modelProto.AsyncRunSQL{Env: cBase.ProjectEnvProd, IsNotRun: true, ViewContent: viewContent, IsNotLimit: true, DevMode: devMode}
	_, executeSQL, err := s.logicService.GenRunSql(ctx, project, req)
	if err != nil {
		return err
	}

	s.logger.Infof("viewContent %s sql: %s", tableName, executeSQL)
	createViewSql := fmt.Sprintf("CREATE OR REPLACE VIEW %s AS (\n%s\n) ", tableName, executeSQL)

	var migrateEngine migrate.Migrate
	migrateEngine, err = s.getMigrateEngine(ctx, project)
	if err != nil {
		return err
	}
	err = migrateEngine.MigrateByStatement(ctx, createViewSql)
	if err != nil {
		return err
	}
	return nil
}

func (s *IndicatorViewService) getMigrateEngine(ctx context.Context, project string) (
	migrateEngine migrate.Migrate, err error) {

	var resourceInfo *dap_common.ResourceInfo
	resourceInfo, err = s.dapCommon.GetStorageResources(ctx, project)
	if err != nil {
		return
	}
	var resourceType = global.ResourceType(resourceInfo.ResourceType)
	// 获取查询资源的连接信息
	err = resourceInfo.Content.Decrypt(global.AppConfig.Security.DmpCryptKey)
	if err != nil {
		return
	}
	logInfo := &log_entry.DistributeLogInfo{ProjectCode: project, InstanceId: pkg.NewMysqlID()}
	switch resourceType {
	case global.RDSResourceType:
		migrateEngine = migrate.NewMysqlImpl(resourceInfo.Content, logInfo)
	case global.RDSSaasResourceType:
		// 获取租户列表
		var tenantList []*dap_common.TenantInfo
		tenantList, err = s.dapCommon.ExecutableList(ctx, project, false)
		if err != nil {
			return
		}
		migrateEngine = migrate.NewMysqlSaasImpl(resourceInfo.Content, tenantList, logger.NewLogger(global.AppConfig), logInfo, nil)
	case global.StarRocksResourceType:
		migrateEngine = migrate.NewStarRocksImpl(resourceInfo.Content, logInfo)
	case global.StarRocksSaaSResourceType:
		// 获取租户列表
		var tenantList []*dap_common.TenantInfo
		tenantList, err = s.dapCommon.ExecutableList(ctx, project, false)
		if err != nil {
			return
		}
		migrateEngine = migrate.NewStarRocksSaasImpl(resourceInfo.Content, tenantList, logger.NewLogger(global.AppConfig), logInfo, nil)
	case global.DamengResourceType:
		migrateEngine = migrate.NewDamengImpl(resourceInfo.Content, logInfo)
	case global.DamengSaaSResourceType:
		// 获取租户列表
		var tenantList []*dap_common.TenantInfo
		tenantList, err = s.dapCommon.ExecutableList(ctx, project, false)
		if err != nil {
			return
		}
		migrateEngine = migrate.NewDamengSaasImpl(resourceInfo.Content, tenantList, logger.NewLogger(global.AppConfig), logInfo, nil)
	default:
		err = fmt.Errorf("不支持的资源类型:%s", resourceType)
	}
	return
}
