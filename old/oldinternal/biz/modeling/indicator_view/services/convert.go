package services

import (
	ivBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/indicator_view/base"
	logicBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	ivProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
)

func convertIndicatorToIndicatorViewBriefWithContent(indicator *ivBase.Indicator) *ivProto.IndicatorViewBriefWithContent {
	viewContent, err := logicBase.UnmarshalAdsViewContent(indicator.ViewContent)
	if err != nil {
		viewContent = nil
	}

	return &ivProto.IndicatorViewBriefWithContent{
		IndicatorViewBrief: &ivProto.IndicatorViewBrief{
			ID:                indicator.ID,
			Code:              indicator.Code,
			PhysicalModelCode: indicator.PhysicalModelCode,
			MultiDimModelCode: indicator.MultiDimModelCode,
			Name:              indicator.Name,
			ViewName:          indicator.ViewName,
			SubjectID:         indicator.SubjectID,
			CreatedOn:         indicator.CreatedOn,
			ModifiedOn:        indicator.ModifiedOn,
			ReleaseStatus:     modelBase.ReleaseStatus(indicator.ReleaseStatus),
			ModifyStatus:      indicator.ModifyStatus,
		},
		ViewContent: viewContent,
	}
}
