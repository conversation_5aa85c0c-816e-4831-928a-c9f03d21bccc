package services

import (
	"context"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/iface"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"

	dap_common "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common"
	dap_indicator_modeling "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-indicator-modeling"
	dap_sqlparser_java "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-sqlparser-java"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/cache/table_cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"

	"github.com/defval/inject/v2"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/moql"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	apiManageService "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/services"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/indicator_view"
	logic "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model"
	ivProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/recorder"
	multiDimRepo "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/repo"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewIndicatorViewInternalService))
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewIndicatorViewService, inject.As(new(IndicatorService))))
}

type IndicatorService interface {
	// query
	GetIndicatorViewCount(ctx context.Context, project string, env cBase.ProjectEnvType) (int64, error)
	ListIndicatorBriefs(ctx context.Context, project string, req modelProto.ListIndicatorViewReq) (result []*ivProto.IndicatorViewBrief, err error)
	BatchGetIndicatorFieldCount(ctx context.Context, project string, req modelProto.BatchGetModelFieldCountReq) (result map[string]*modelProto.BatchGetModelFieldCountRspItem, err error)
	GetAdsViewRelationsByUpstreamPhysicalModelCodes(ctx context.Context, project string, codes []string) (relations []*modelProto.ModelRefRelation, err error)
	GetAdsViewRelationsByUpstreamMultiDimModelCodes(ctx context.Context, project string, codes []string) (relations []*modelProto.ModelRefRelation, err error)
	GetIndicatorBriefWithContent(ctx context.Context, project string, code string, env cBase.ProjectEnvType) (result *ivProto.IndicatorViewBriefWithContent, err error)

	// mutation
	AddIndicator(ctx context.Context, project string, req modelProto.AddBusinessOrPhysicsModel) (code string, err error)
	UpdateIndicator(ctx context.Context, project string, req modelProto.UpdateModelDefinitionReq) (rsp modelProto.UpdateModelDefinitionRsp, err error)
	ReleaseIndicator(ctx context.Context, project, account string, req modelProto.ReleaseDefinitionReq) (err error)
	OfflineIndicator(ctx context.Context, project string, req modelProto.OfflineDefinitionReq) (err error)
	DeleteIndicator(ctx context.Context, project string, req modelProto.DeleteDefinitionReq) (err error)
	// validate
	ValidateIndicatorExpr(ctx context.Context, project string, req ivProto.ValidateIndicatorExprReq) (rsp ivProto.ValidateIndicatorExprRsp, err error)
	GetIndicatorAvailableFilters(ctx context.Context, project string, req ivProto.GetIndicatorAvailableFiltersReq) (rsp ivProto.GetIndicatorAvailableFiltersRsp, err error)

	// others
	GetAvailableDimAndFiltersV2(ctx context.Context, project string, req ivProto.GetAvailableDimAndFiltersReq) (rsp *ivProto.GetAvailableDimAndFiltersRsp, err error)
	GetPublicAvailableDimAndFilters(ctx context.Context, project string, req ivProto.GetPublicDimensionReq) (rsp *ivProto.GetAvailableDimAndFiltersRsp, err error)
	GetIndicatorTranslateExpress(ctx context.Context, propRaw string, dialect moql.MoqlDialect, resourceType entities.ProjectResourceType, vars []rpc_call.Variable) (translatedExpress string, err error)
}

var _ IndicatorService = (*IndicatorViewService)(nil)

type IndicatorViewInternalService struct {
	modelService      *model.ModelService
	multiDimModelRepo multiDimRepo.MultiDimModelRepo
	apiManageService  *apiManageService.ApiManageService
	tableCache        *table_cache.TableCache
}

func NewIndicatorViewInternalService(modelService *model.ModelService,
	multiDimModelRepo multiDimRepo.MultiDimModelRepo,
	apiManageService *apiManageService.ApiManageService) *IndicatorViewInternalService {
	return &IndicatorViewInternalService{
		modelService:      modelService,
		multiDimModelRepo: multiDimModelRepo,
		apiManageService:  apiManageService,
		tableCache:        table_cache.NewTableCache(),
	}
}

type IndicatorViewService struct {
	*IndicatorViewInternalService
	recorder                 *recorder.IndicatorViewFastRecorder
	logger                   *logger.Logger
	repo                     indicator_view.IndicatorViewRepo
	modelRepo                model.ModelRepo
	logicService             *logic.ModelLogic
	dapCommon                dap_common.DapCommonService
	indicatorModelingService dap_indicator_modeling.IndicatorModeling
	dapSqlparser             dap_sqlparser_java.DapSparserJavaService
	moqlService              moql.MoqlService
	multiDimModelS           iface.MultiDimService
}

func NewIndicatorViewService(repo indicator_view.IndicatorViewRepo,
	internalService *IndicatorViewInternalService, recorder *recorder.IndicatorViewFastRecorder,
	modelRepo model.ModelRepo, logicService *logic.ModelLogic) *IndicatorViewService {
	i := &IndicatorViewService{
		recorder:                     recorder,
		logger:                       global.Logger,
		repo:                         repo,
		IndicatorViewInternalService: internalService,
		modelRepo:                    modelRepo,
		logicService:                 logicService,
		dapCommon:                    dap_common.NewDapCommonServiceS(),
		indicatorModelingService:     dap_indicator_modeling.NewIndicatorModelingServiceS(),
		dapSqlparser:                 dap_sqlparser_java.NewDapSparserJavaServiceS(),
		moqlService:                  moql.NewMoqlServiceS(),
	}
	global.Container.MustExtract(&i.multiDimModelS)
	return i
}
