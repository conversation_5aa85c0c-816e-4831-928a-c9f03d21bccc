package services

import (
	"context"
	"database/sql"
	"github.com/pkg/errors"
	baseErr "github.com/pkg/errors"

	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	ivProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
)

// 查询统计
func (s *IndicatorViewService) GetIndicatorViewCount(ctx context.Context, project string, env cBase.ProjectEnvType) (int64, error) {
	return s.repo.GetIndicatorViewCount(ctx, project, env)
}

func (s *IndicatorViewService) ListIndicatorBriefs(ctx context.Context, project string, req modelProto.ListIndicatorViewReq) (result []*ivProto.IndicatorViewBrief, err error) {
	return s.repo.ListIndicatorBriefs(ctx, project, req)
}

func (s *IndicatorViewService) BatchGetIndicatorFieldCount(ctx context.Context, project string, req modelProto.BatchGetModelFieldCountReq) (resultMap map[string]*modelProto.BatchGetModelFieldCountRspItem, err error) {
	resultMap, err = s.repo.BatchGetModelFieldCount(ctx, project, req)
	if err != nil && baseErr.Is(err, sql.ErrNoRows) {
		err = nil
		return
	}
	return
}

func (s *IndicatorViewService) GetAdsViewRelationsByUpstreamPhysicalModelCodes(ctx context.Context, project string, codes []string) (relations []*modelProto.ModelRefRelation, err error) {
	return s.repo.GetAdsViewRelationsByUpstreamPhysicalModelCodes(ctx, project, codes)
}

func (s *IndicatorViewService) GetAdsViewRelationsByUpstreamMultiDimModelCodes(ctx context.Context, project string, codes []string) (relations []*modelProto.ModelRefRelation, err error) {
	return s.repo.GetAdsViewRelationsByUpstreamMultiDimModelCodes(ctx, project, codes)
}

func (s *IndicatorViewService) GetIndicatorBriefWithContent(ctx context.Context, project string, code string, env cBase.ProjectEnvType) (result *ivProto.IndicatorViewBriefWithContent, err error) {
	model, err := s.repo.GetIndicatorByCodeAndEnv(ctx, project, code, env)
	if err != nil {
		return
	}
	if model.Code == "" {
		err = errors.Errorf("应用视图[%s]不存在", code)
		return nil, err
	}
	return convertIndicatorToIndicatorViewBriefWithContent(model), nil
}
