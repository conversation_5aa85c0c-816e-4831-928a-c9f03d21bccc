package base

import (
	"database/sql"
	"time"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/model_distribute"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	pkg "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"
	physicalModelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/server/physical_model/proto"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type Indicator struct {
	ID                    string                                 `json:"id" db:"id"`
	ReleaseID             string                                 `json:"release_id" db:"-"`
	Code                  string                                 `json:"code" db:"code"`
	PhysicalModelCode     string                                 `json:"physical_model_code" db:"physical_model_code"`
	MultiDimModelCode     string                                 `json:"multi_dim_model_code" db:"multi_dim_model_code"`
	Environment           cBase.ProjectEnvType                   `json:"environment" db:"environment"`
	Name                  string                                 `json:"name" db:"name"`
	ViewName              string                                 `json:"view_name" db:"view_name"`
	Description           string                                 `json:"description" db:"description"`
	SubjectID             string                                 `json:"subject_id" db:"subject_id"`
	Content               string                                 `json:"content" db:"content"`
	ViewContent           string                                 `json:"view_content" db:"view_content"`
	Param                 string                                 `json:"param" db:"param"`
	CreatedOn             db.NullTime                            `json:"created_on" db:"created_on"`
	ModifiedOn            db.NullTime                            `json:"modified_on" db:"modified_on"`
	CreatedBy             string                                 `json:"created_by" db:"created_by"`
	ModifiedBy            string                                 `json:"modified_by" db:"modified_by"`
	IsRelease             bool                                   `json:"is_release"`
	ReleaseComment        string                                 `json:"release_comment" db:"release_comment"`
	Version               int32                                  `json:"version" db:"version"`
	Resource              entities.ProjectResourceType           `json:"resource" db:"resource"`
	SubjectName           string                                 `json:"subject_name" db:"subject_name"`
	ReleaseStatus         string                                 `json:"release_status" db:"release_status"`
	ModifyStatus          modelBase.ModifyStatus                 `json:"modify_status" db:"modify_status"`
	RefType               RefType                                `json:"ref_type;" db:"ref_type"` // 模型引用类型:all_self-全部自己空间的,all_std-全部标准的,had_one_std-有引用标准的
	DisableDimensional    string                                 `json:"disable_dimensional;" db:"disable_dimensional"`
	SourceProjectLevel    model_distribute.ProjectType           `json:"source_project_level" db:"source_project_level"`
	SourceProject         db.NullString                          `json:"source_project" db:"source_project"`
	EditedStandardFlag    int                                    `json:"edited_standard_flag" db:"edited_standard_flag"` // 是否编辑过标准模型
	DevMode               string                                 `form:"dev_mode" json:"dev_mode" db:"dev_mode"`
	TableName             string                                 `form:"table_name" json:"table_name" db:"table_name"`
	GroupFieldOrder       []*physicalModelProto.GroupFieldsOrder `form:"field_order" json:"field_order"`
	AllDimensionArePublic int32                                  `json:"all_dimension_are_public" db:"all_dimension_are_public"`
}

func NewIndicator(req *NewIndicatorReq) *Indicator {
	if req.ID == "" {
		req.ID = pkg.NewMysqlID()
	}
	if req.Code == "" {
		req.Code = pkg.NewMysqlID()
	}
	return &Indicator{
		ID:                 req.ID,
		Code:               req.Code,
		PhysicalModelCode:  req.PhysicalModelCode,
		MultiDimModelCode:  req.MultiDimModelCode,
		Environment:        req.Environment,
		Name:               req.Name,
		ViewName:           req.ViewName,
		Description:        req.Description,
		SubjectID:          req.SubjectID,
		Content:            req.Content,
		ViewContent:        req.ViewContent,
		Param:              req.Param,
		Version:            req.Version,
		ReleaseComment:     req.ReleaseComment,
		Resource:           req.Resource,
		RefType:            req.RefType,
		DisableDimensional: req.DisableDimensional,
		DevMode:            req.DevMode,
		TableName:          req.TableName,
		SourceProjectLevel: req.SourceProjectLevel,
		EditedStandardFlag: req.EditedStandardFlag,
	}
}

type IndicatorField struct {
	ID                    string                  `json:"id" db:"id"`
	IndicatorID           string                  `json:"indicator_id" db:"indicator_id"`
	Name                  string                  `json:"name" db:"name"`
	NameCn                string                  `json:"name_cn" db:"name_cn"`
	Description           string                  `json:"description" db:"description"`
	Expression            string                  `json:"expression" db:"expression"`
	ValueComment          string                  `json:"value_comment" db:"value_comment"`
	FieldSource           IndicatorFieldSource    `json:"field_source" db:"field_source"`
	AggOn                 IndicatorFieldAggStatus `json:"agg_on" db:"agg_on"`
	Rank                  int64                   `json:"rank" db:"rank"`
	FieldType             string                  `json:"field_type" db:"field_type"`
	CreatedOn             db.NullTime             `json:"created_on" db:"created_on"`
	ModifiedOn            db.NullTime             `json:"modified_on" db:"modified_on"`
	CreatedBy             string                  `json:"created_by" db:"created_by"`
	ModifiedBy            string                  `json:"modified_by" db:"modified_by"`
	IndicatorBusinessCode string                  `json:"indicator_business_code" db:"indicator_business_code"`
	Code                  string                  `json:"code" db:"code"`
	FieldCode             string                  `form:"field_code" json:"field_code" db:"field_code"`
	DimViewData           sql.NullString          `form:"dim_view_data" json:"dim_view_data" db:"dim_view_data"`
	DimType               string                  `form:"dim_type" json:"dim_type" db:"dim_type"`
	FieldBusinessType     int                     `form:"field_business_type" json:"field_business_type" db:"field_business_type"`
	IsPrimary             int                     `form:"is_primary" json:"is_primary" db:"is_primary"`
	PrimaryRank           int                     `json:"primary_rank"  db:"primary_rank"`
	Scale                 int                     `form:"scale" json:"scale" db:"scale"`
	Length                int                     `form:"length" json:"length" db:"length"`
	BusinessModelFieldId  string                  `form:"business_model_field_id" json:"business_model_field_id" db:"business_model_field_id"`
	GroupName             string                  `form:"group_name" json:"group_name" db:"group_name"`
	ParentId              string                  `form:"parent_id" json:"parent_id" db:"parent_id"`
}

func NewIndicatorField(req *NewIndicatorFieldReq) *IndicatorField {
	if req.ID == "" {
		req.ID = pkg.NewMysqlID()
	}
	return &IndicatorField{
		ID:                    req.ID,
		IndicatorID:           req.IndicatorID,
		Name:                  req.Name,
		NameCn:                req.NameCn,
		Description:           req.Description,
		Expression:            req.Expression,
		ValueComment:          req.ValueComment,
		FieldSource:           req.FieldSource,
		AggOn:                 req.AggOn,
		Rank:                  req.Rank,
		FieldType:             req.FieldType,
		IndicatorBusinessCode: req.IndicatorBusinessCode,
		Code:                  req.Code,
		FieldCode:             req.FieldCode,
		DimViewData:           sql.NullString{String: req.DimViewData},
		DimType:               req.DimType,
		FieldBusinessType:     req.FieldBusinessType,
		IsPrimary:             req.IsPrimary,
		PrimaryRank:           req.PrimaryRank,
		Scale:                 req.Scale,
		Length:                req.Length,
		BusinessModelFieldId:  req.BusinessModelFieldID,
		GroupName:             req.GroupName,
		ParentId:              req.ParentId,
	}
}

type IndicatorParam struct {
	Name         string        `form:"name" json:"name"`
	DataType     ParamDataType `form:"data_type" json:"data_type"`
	DefaultValue string        `form:"default_value" json:"default_value"`
}

type ParamDataType string

// 目前参数只区分数值和字符串两种类型
const (
	IntegerParam  ParamDataType = "INT"
	StringParam   ParamDataType = "STRING"
	DatetimeParam ParamDataType = "DATETIME"
)

type IndicatorFieldSource string

const (
	Default IndicatorFieldSource = ""
	Custom  IndicatorFieldSource = "custom"
)

// 为保持和派生指标模型的流程状态一致
type IndicatorStatus string

const (
	Available IndicatorStatus = "启动"
	Forbidden IndicatorStatus = "禁用"
)

type NewIndicatorReq struct {
	ID                 string                       `json:"id" db:"id"`
	Code               string                       `json:"code" db:"code"`
	PhysicalModelCode  string                       `json:"physical_model_code" db:"physical_model_code"`
	MultiDimModelCode  string                       `json:"multi_dim_model_code" db:"multi_dim_model_code"`
	Environment        cBase.ProjectEnvType         `json:"environment" db:"environment"`
	Name               string                       `json:"name" db:"name"`
	ViewName           string                       `json:"view_name" db:"view_name"`
	Description        string                       `json:"description" db:"description"`
	SubjectID          string                       `json:"subject_id" db:"subject_id"`
	Content            string                       `json:"content" db:"content"`
	ViewContent        string                       `json:"view_content" db:"view_content"`
	Param              string                       `json:"param" db:"param"`
	ValueComment       string                       `json:"value_comment" db:"value_comment"`
	ReleaseComment     string                       `json:"release_comment" db:"release_comment"`
	Version            int32                        `json:"version" db:"version"`
	Resource           entities.ProjectResourceType `form:"resource" json:"resource" binding:"required"`
	RefType            RefType                      `json:"ref_type;" db:"ref_type"` // 模型引用类型:all_self-全部自己空间的,all_std-全部标准的,had_one_std-有引用标准的
	DisableDimensional string                       `json:"disable_dimensional;" db:"disable_dimensional"`
	TableName          string                       `form:"table_name" json:"table_name"`
	DevMode            string                       `form:"dev_mode" json:"dev_mode"`
	CreatedBy          string                       `gorm:"column:created_by;type:char(36);comment:记录创建者Id" json:"created_by"`
	ModifiedBy         string                       `gorm:"column:modified_by;type:char(36);comment:记录修改者Id" json:"modified_by"`
	SourceProjectLevel model_distribute.ProjectType `json:"source_project_level" db:"source_project_level"`
	SourceProject      db.NullString                `json:"source_project" db:"source_project"`
	EditedStandardFlag int                          `json:"edited_standard_flag" db:"edited_standard_flag"` // 是否编辑过标准模型
}

type NewIndicatorFieldReq struct {
	ID                    string                  `json:"id" db:"id"`
	IndicatorID           string                  `json:"indicator_id" db:"indicator_id"`
	Name                  string                  `json:"name" db:"name"`
	NameCn                string                  `json:"name_cn" db:"name_cn"`
	Description           string                  `json:"description" db:"description"`
	Expression            string                  `json:"expression" db:"expression"`
	ValueComment          string                  `json:"value_comment" db:"value_comment"`
	FieldSource           IndicatorFieldSource    `json:"field_source" db:"field_source"`
	AggOn                 IndicatorFieldAggStatus `json:"agg_on" db:"agg_on"`
	Rank                  int64                   `json:"rank" db:"rank"`
	FieldType             string                  `json:"field_type" db:"field_type"`
	IndicatorBusinessCode string                  `json:"indicator_business_code" db:"indicator_business_code"`
	Code                  string                  `json:"code" db:"code"`
	FieldCode             string                  `form:"field_code" json:"field_code"`
	BusinessModelFieldID  string                  `json:"business_model_field_id"  gorm:"column:business_model_field_id;type:char(36)"`
	TableFieldName        string                  `json:"table_field_name"  gorm:"column:table_field_name"`
	Length                int                     `json:"length"  gorm:"column:length"`
	Scale                 int                     `json:"scale"  gorm:"column:scale"`
	IsPrimary             int                     `json:"is_primary"  gorm:"column:is_primary"`
	PrimaryRank           int                     `json:"primary_rank"  gorm:"column:primary_rank"`
	DimType               string                  `json:"dim_type"  gorm:"column:dim_type"`
	DimViewData           string                  `json:"dim_view_data"  gorm:"column:dim_view_data"`
	FieldBusinessType     int                     `json:"field_business_type"  gorm:"column:field_business_type"`
	GroupName             string                  `gorm:"column:group_name" json:"group_name"` // 字段分组名称
	ParentId              string                  `json:"parent_id"  gorm:"column:parent_id"`
}

type IndicatorFieldAggStatus int

const (
	FieldAggOff IndicatorFieldAggStatus = 0
	FieldAggOn  IndicatorFieldAggStatus = 1
)

type RefType string

const (
	AllSelfRefType RefType = "all_self"
	AllStdRefType  RefType = "all_std"
	DadOneRefType  RefType = "had_one_std"
)

type BriefAnalysisDimension struct {
	Category   modelBase.ModelCategory `form:"category" json:"category"`
	Code       string                  `form:"code" json:"code"`
	Dimensions []Dimension             `form:"dimensions" json:"dimensions"`
}

type Dimension struct {
	Name string `form:"name" json:"name"`
}

// 物理模型依赖表
type PhysicalModelDependence struct {
	Id                string    `gorm:"column:id;type:char(36);comment:主键id;primary_key" json:"id"`
	Code              string    `gorm:"column:code;type:char(36);comment: 汇总表code;NOT NULL" json:"code"`
	PhysicalModelCode string    `gorm:"column:physical_model_code;type:char(36);comment:物理模型code;NOT NULL" json:"physical_model_code"`
	DepModelCode      string    `gorm:"column:dep_model_code;type:char(36);comment:依赖的物理模型code;NOT NULL" json:"dep_model_code"`
	Environment       string    `gorm:"column:environment;type:enum('开发','生产','历史');default:开发;comment:环境;NOT NULL" json:"environment"`
	CreatedOn         time.Time `gorm:"column:created_on;type:datetime;default:CURRENT_TIMESTAMP;comment:记录创建时间;NOT NULL" json:"created_on"`
	ModifiedOn        time.Time `gorm:"column:modified_on;type:datetime;comment:记录修改时间" json:"modified_on"`
	CreatedBy         string    `gorm:"column:created_by;type:char(36);comment:记录创建者Id" json:"created_by"`
	ModifiedBy        string    `gorm:"column:modified_by;type:char(36);comment:记录修改者Id" json:"modified_by"`
}

func (m *PhysicalModelDependence) TableName() string {
	return "dap_m_physical_model_dependence"
}
