package utils

import (
	"fmt"
	"regexp"
	"strings"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
)

var customTableExprPattern = regexp.MustCompile(`(^|[^$])\{(.*?)}`)
var customFieldExprPattern = regexp.MustCompile(`\[(.*?)]`)
var customTableAndFieldExprPattern = regexp.MustCompile(`\{([A-Za-z0-9_]+?)}.\[([A-Za-z0-9_]+?)]`)
var variablePattern = regexp.MustCompile(`\$\{(.*?)}`)
var aggFuncPatterns = []*regexp.Regexp{
	regexp.MustCompile(`count\(`),
	regexp.MustCompile(`sum\(`),
	regexp.MustCompile(`avg\(`),
	regexp.MustCompile(`min\(`),
	regexp.MustCompile(`max\(`),
}

var mapAllAggFuncPatterns = regexp.MustCompile(`count|sum|avg|min|max\((.*?)\)`)

var blankPattern = regexp.MustCompile(`[\s]{1,}`)

// RegReplaceVariableExpr 替换表达式变量
func RegReplaceVariableExpr(propRaw string, varMap map[string]string) (newPropRaw string) {
	matches := RegGetVariableExpr(propRaw)
	newPropRaw = propRaw
	for _, matchItem := range matches {
		varValue, ok := varMap[matchItem]
		if !ok {
			continue
		}
		newPropRaw = strings.ReplaceAll(newPropRaw, fmt.Sprintf("${%s}", matchItem), varValue)
	}
	return
}

// RegGetVariableExpr 解析表达式获取变量
func RegGetVariableExpr(propRaw string) []string {
	var matchTableRes []string
	result := variablePattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range result {
		if len(matchItem) > 1 {
			matchTableRes = append(matchTableRes, matchItem[1])
		}
	}
	return matchTableRes
}

// RegGetTableExpr 解析表达式获取引用表
func RegGetTableExpr(propRaw string) []string {
	// 结果是[][]string结构，[]string中第2个为真实匹配到的元素
	var matchTableRes []string
	result := customTableExprPattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range result {
		if len(matchItem) > 1 {
			matchTableRes = append(matchTableRes, matchItem[2])
		}
	}
	return matchTableRes
}

// RegMatchAndReplaceTableAndField 解析表达式并替换为真实字段名
func RegMatchAndReplaceTableAndField(propRaw string) (newPropRaw string) {
	matchRes := RegGetTableExpr(propRaw)
	// 去除花括号，获取到真实表名
	newPropRaw = propRaw
	for _, matchItem := range matchRes {
		patternItem := fmt.Sprintf(`\{(%s)\}`, matchItem)
		reItem := regexp.MustCompile(patternItem)
		newPropRaw = reItem.ReplaceAllString(newPropRaw, matchItem)
	}
	matchRes = RegGetFieldExpr(propRaw)
	// 去除中括号，获取到真实字段名
	for _, matchItem := range matchRes {
		patternItem := fmt.Sprintf(`\[(%s)\]`, matchItem)
		reItem := regexp.MustCompile(patternItem)
		newPropRaw = reItem.ReplaceAllString(newPropRaw, matchItem)
	}
	return
}

// RegGetFieldExpr 解析表达式获取引用字段
func RegGetFieldExpr(propRaw string) []string {
	// 结果是[][]string结构，[]string中第2个为真实匹配到的元素
	var matchFieldRes []string
	result := customFieldExprPattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range result {
		if len(matchItem) > 1 {
			matchFieldRes = append(matchFieldRes, matchItem[1])
		}
	}
	return matchFieldRes
}

// RegGetTableAndFieldProp 解析表达式获取表明加字段这一个完整单元
func RegGetTableAndFieldProp(propRaw string) []rpc_call.PropField {
	// 结果是[][]string结构，[]string中第2个为真实匹配到的元素
	var result []rpc_call.PropField
	matchResult := customTableAndFieldExprPattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range matchResult {
		if len(matchItem) > 2 {
			result = append(result, rpc_call.PropField{
				TableName: matchItem[1],
				FieldName: matchItem[2],
			})
		}
	}
	return result
}

// RegCheckHasAggFunc 通过正则校验是否包含聚合函数（sum, count, min, max, avg）
func RegCheckHasAggFunc(propRaw string) bool {
	// 处理空白字符
	processedString := blankPattern.ReplaceAllString(strings.ToLower(propRaw), "")
	for _, aggFuncPattern := range aggFuncPatterns {
		if aggFuncPattern.FindStringIndex(processedString) != nil {
			return true
		}
	}
	return false
}

// RegGetFieldExprProp 解析表达式获取引用字段
func RegGetFieldExprProp(tableName string, propRaw string) []rpc_call.PropField {
	// 结果是[][]string结构，[]string中第2个为真实匹配到的元素
	var result []rpc_call.PropField
	matchResult := customFieldExprPattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range matchResult {
		if len(matchItem) > 1 {
			result = append(result, rpc_call.PropField{
				TableName: tableName,
				FieldName: matchItem[1],
			})
		}
	}
	return result
}

// RegGetFieldExpr 解析表达式获取引用字段
func ParseAllAggFunc(propRaw string) []string {
	// 结果是[][]string结构，[]string中第2个为真实匹配到的元素
	var matchFieldRes []string
	result := mapAllAggFuncPatterns.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range result {
		if len(matchItem) > 1 {
			matchFieldRes = append(matchFieldRes, matchItem[1])
		}
	}
	return matchFieldRes
}
