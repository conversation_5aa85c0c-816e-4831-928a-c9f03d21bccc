package indicator_view

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/context_helper"

	physicalModelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/server/physical_model/proto"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"

	commonUtils "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"

	modelErr "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/errors"

	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"

	"github.com/jmoiron/sqlx"

	ivProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"

	ivBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/indicator_view/base"
	pkg "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	baseStore "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/base/store"
	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewIndicatorViewStore, inject.As(new(IndicatorViewRepo))))
}

type IndicatorViewRepo interface {
	// 获取Tx，支持外层做事务
	GetTx(ctx context.Context, project string) (tx *sql.Tx, err error)
	GetNextVersion(ctx context.Context, sess pkg.DBContextQueryer, code string) (version int32, err error)
	// 新增派生指标视图
	AddIndicator(ctx context.Context, project string, tx *sql.Tx, indicator *ivBase.Indicator) (string, error)
	// 指标是否存在发布态
	HasIndicatorRelease(ctx context.Context, project string, codes []string) (map[string]string, error)
	// 获取指标定义列表
	ListIndicatorBriefs(ctx context.Context, project string, req ivProto.ListIndicatorViewReq) ([]*ivProto.IndicatorViewBrief, error)
	ListIndicatorBriefsWithContent(ctx context.Context, project string, req ivProto.ListIndicatorViewReq) ([]*ivProto.IndicatorViewBriefWithContent, error)
	// 更新参数列表
	UpdateIndicatorParam(ctx context.Context, project string, req ivProto.UpdateIndicatorViewParamReq) (err error)
	// 获取指标视图
	GetIndicator(ctx context.Context, project, indicatorID string) (indicator *ivBase.Indicator, err error)
	// 根据code和环境获取指标视图
	GetIndicatorByCodeAndEnv(ctx context.Context, project, code string, env cBase.ProjectEnvType) (indicator *ivBase.Indicator, err error)
	// 获取指标视图字段列表
	GetIndicatorFields(ctx context.Context, project, indicatorID string) (fields []*ivBase.IndicatorField, err error)
	// 根据code和环境获取指标视图字段列表
	GetIndicatorFieldsByCodeAndEnv(ctx context.Context, project, code string, env cBase.ProjectEnvType) (fields []*ivBase.IndicatorField, err error)
	// 新增派生指标视图
	AddIndicatorUseTx(ctx context.Context, tx *sql.Tx, indicator *ivBase.Indicator) (string, error)
	// 更新指标字段
	UpdateIndicatorFieldsUseTx(ctx context.Context, tx *sql.Tx, indicatorID string, fieldOrder []*physicalModelProto.GroupFieldsOrder, fields []*ivBase.IndicatorField) (err error)
	// 更新指标定义
	UpdateIndicatorUseTx(ctx context.Context, tx *sql.Tx, indicator *ivBase.Indicator) (err error)
	// 删除指标定义
	DeleteIndicatorUseTx(ctx context.Context, tx *sql.Tx, indicatorID string) (err error)
	// 删除指标字段
	DeleteIndicatorFieldUseTx(ctx context.Context, tx *sql.Tx, indicatorID string) (err error)
	// 删除指标定义
	DeleteIndicatorUseTxByCode(ctx context.Context, tx *sql.Tx, code string) (err error)
	//删除收藏模型
	DeleteModelCollect(ctx context.Context, tx *sql.Tx, code string) (err error)
	// 删除指标字段
	DeleteIndicatorFieldUseTxByCode(ctx context.Context, tx *sql.Tx, code string) (err error)
	// 获取指标模型数量
	GetIndicatorViewCount(ctx context.Context, project string, env cBase.ProjectEnvType) (count int64, err error)
	GetIndicatorViewCountBySubject(ctx context.Context, project string, env cBase.ProjectEnvType) (ivProto.GetIndicatorCountBySubjectRsp, error)
	// 判断中文名是否存在
	ExistNameCn(ctx context.Context, project string, name string, excludeCode string) (exist bool, err error)
	ExistName(ctx context.Context, project string, name string, excludeCode string) (exist bool, err error)
	ExistPhysicalModelName(ctx context.Context, project string, name string, excludeCode string) (exist bool, err error)
	// 批量获取模型字段数量
	BatchGetModelFieldCount(ctx context.Context, project string, req ivProto.BatchGetModelFieldCountReq) (map[string]*ivProto.BatchGetModelFieldCountRspItem, error)
	// 获取物理模型所在资源
	GetPhysicalModelResource(ctx context.Context, project string, codes []string, env cBase.ProjectEnvType) (resources []entities.ProjectResourceType, err error)
	//获取所有生产态视图应用表
	GetAllProdViewAds(ctx context.Context, project string) (indicators []*ivBase.Indicator, err error)
	//更新开发态指标发布状态字段
	BatchUpdateStatus(ctx context.Context, tx *sql.Tx, code, releaseStatus string, modifyStatus modelBase.ModifyStatus, ApplyStatus int, flag int32) (err error)
	UpdateReleaseStatus(ctx context.Context, tx *sql.Tx, code, releaseStatus string) (err error)
	UpdateApplyStatus(ctx context.Context, tx *sql.Tx, code string, ApplyStatus int) (err error)
	UpdateAllDimensionArePublic(ctx context.Context, tx *sql.Tx, code string, flag int32) (err error)
	UpdateSubjectByCode(ctx context.Context, tx *sql.Tx, code, subjectId string) error
	ListIndicatorByCodesEnv(ctx context.Context, project string, codes []string, envs []cBase.ProjectEnvType) (indicators []ivBase.Indicator, err error)
	//更新开发态指标修改状态字段
	UpdateModifyStatus(ctx context.Context, tx *sql.Tx, code string, modifyStatus modelBase.ModifyStatus) (err error)
	GetAdsViewRelationsByUpstreamPhysicalModelCodes(ctx context.Context, project string, codes []string) ([]*modelProto.ModelRefRelation, error)
	GetAdsViewRelationsByUpstreamMultiDimModelCodes(ctx context.Context, project string, codes []string) ([]*modelProto.ModelRefRelation, error)
	GetDepCodes(ctx context.Context, project, code string, category modelBase.ModelRelationCategory, env cBase.ProjectEnvType) (codes []string, err error)
	GetAllCodes(ctx context.Context, project string) (codes []string, err error)
	UpdateIndicatorSourceInfo(ctx context.Context, project string, codes []string, sourceProject string, sourceProjectLevel int) (err error)
	UpdateIndicatorRefTypeInfoById(ctx context.Context, project string, id string, refType string) (err error)
	UpdateIndicatorFieldsSourceInfo(ctx context.Context, project string, indicatorIds []string, sourceProject string, sourceProjectLevel int) (err error)
	GetIndicatorFieldsByIndicatorCodes(ctx context.Context, project string, codes []string) (fields []*ivBase.IndicatorField, err error)
	GetIndicatorByCodes(ctx context.Context, project string, codes []string) (indicatorList []*ivBase.Indicator, err error)

	GetIndicatorFieldsGroup(ctx context.Context, project, indicatorID string) (fields []*physicalModelProto.GroupFieldsOrder, err error)
	AddPhysicalModelDep(ctx context.Context, tx *sql.Tx, env string, modelCode string, depModels []*ivBase.PhysicalModelDependence) (err error)
	GetPhysicalModelDepByCode(ctx context.Context, project, env, modelCode string) ([]*ivBase.PhysicalModelDependence, error)
}

var _ IndicatorViewRepo = (*IndicatorViewStore)(nil)

type IndicatorViewStore struct {
	*baseStore.Repo
}

func NewIndicatorViewStore(saas db.SaaS) *IndicatorViewStore {
	return &IndicatorViewStore{
		Repo: baseStore.NewRepo(saas),
	}
}

// GetTx 获取Tx，支持外层做事务
func (s *IndicatorViewStore) GetTx(ctx context.Context, project string) (tx *sql.Tx, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	tx, err = sess.Begin()
	return
}

func (s *IndicatorViewStore) GetNextVersion(ctx context.Context, sess pkg.DBContextQueryer, code string) (version int32, err error) {
	rows, err := sess.QueryContext(ctx, "select max(version)+1 from dap_m_indicator where code=? and environment=?", code, entities.ProjectEnvHistory)
	if err != nil {
		return
	}

	defer func() {
		_ = rows.Close()
	}()
	for rows.Next() {
		if err = rows.Scan(&version); err != nil {
			if strings.Contains(err.Error(), "converting NULL to int") {
				return 1, nil
			}
			return
		}
		return
	}
	return
}

func (s *IndicatorViewStore) AddIndicator(ctx context.Context, project string, tx *sql.Tx, indicator *ivBase.Indicator) (string, error) {
	if indicator.ID == "" {
		indicator.ID = pkg.NewMysqlID()
	}

	insertSQL := "insert into dap_m_indicator (id,code,physical_model_code,multi_dim_model_code,environment,name," +
		"view_name,description,subject_id," +
		"content,view_content,param,created_by,modified_by,resource,ref_type,dev_mode,table_name) " +
		"values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	stmt, err := tx.Prepare(insertSQL)
	if err != nil {
		return "", err
	}
	_, err = stmt.ExecContext(ctx, indicator.ID, indicator.Code, indicator.PhysicalModelCode,
		indicator.MultiDimModelCode, indicator.Environment,
		indicator.Name, indicator.ViewName, indicator.Description, indicator.SubjectID, indicator.Content,
		indicator.ViewContent, indicator.Param, indicator.CreatedBy, indicator.ModifiedBy, indicator.Resource, indicator.RefType, indicator.DevMode, indicator.TableName)
	if err != nil {
		return "", err
	}

	return indicator.Code, nil
}

func (s *IndicatorViewStore) HasIndicatorRelease(ctx context.Context, project string, codes []string) (map[string]string, error) {
	if len(codes) == 0 {
		return map[string]string{}, nil
	}
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	args := map[string]interface{}{
		"environment": entities.ProjectEnvProd,
		"code":        codes,
	}
	var sqlCmd = `select id,code from dap_m_indicator where environment=:environment and code in (:code)`
	type ReleaseCode struct {
		ID   string `json:"id" db:"id"`
		Code string `json:"code" db:"code"`
	}
	releaseCodes := make([]ReleaseCode, 0)
	query, queryArgs, err := sqlx.Named(sqlCmd, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	if err = sess.SelectContext(ctx, &releaseCodes, query, queryArgs...); err != nil {
		return nil, err
	}
	result := make(map[string]string, len(releaseCodes))
	for _, indicator := range releaseCodes {
		result[indicator.Code] = indicator.ID
	}
	return result, nil
}

func (s *IndicatorViewStore) genWhere(args map[string]interface{}, req ivProto.ListIndicatorViewReq) (sqlText string) {
	var conditions []string
	if req.SubjectID != "" {
		conditions = append(conditions, "subject_id = :subjectID")
		args["subjectID"] = req.SubjectID
	}
	if string(req.Environment) != "" {
		conditions = append(conditions, "environment = :environment")
		args["environment"] = req.Environment
	}
	if req.Code != "" {
		conditions = append(conditions, "code = :code")
		args["code"] = req.Code
	}
	if len(req.Codes) > 0 {
		conditions = append(conditions, "code in (:codes)")
		args["codes"] = req.Codes
	}
	if req.Keyword != "" {
		kw := req.GetEscapeKeyword()
		conditions = append(conditions, "(name like :keyword or description like :keyword or view_name like :keyword)")
		args["keyword"] = kw
	}

	if len(conditions) > 0 {
		strConditions := strings.Join(conditions, " and ")
		sqlText = "where " + strConditions
	}
	return
}

type indicatorViewBriefWithJsonContent struct {
	*modelProto.IndicatorViewBrief
	ViewContent string `db:"view_content"`
}

func (s *IndicatorViewStore) ListIndicatorBriefsWithContent(ctx context.Context, project string, req ivProto.ListIndicatorViewReq) ([]*ivProto.IndicatorViewBriefWithContent, error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	var indicators []*indicatorViewBriefWithJsonContent

	sqlText := "select id,code,physical_model_code,multi_dim_model_code,name,view_name,subject_id,modify_status,release_status,created_on,created_by,modified_on,modified_by,view_content from dap_m_indicator "
	args := map[string]interface{}{
		"skip": req.GetSkip(),
		"size": req.PageSize,
	}
	whereText := s.genWhere(args, req)
	sqlText += whereText

	// order by
	var orderBy string
	orderBy = " order by created_on "
	if req.Sorts != "" {
		if strings.Contains(req.Sorts, "name") {
			sort := strings.Split(req.Sorts, " ")
			if len(sort) == 2 {
				orderBy = fmt.Sprintf(" order by CONVERT(%s using gbk) %s ", sort[0], sort[1])
			}
		} else {
			orderBy = fmt.Sprintf(" order by %s ", req.Sorts)
		}
	}
	// limit
	limitSQL := " limit :skip, :size "

	sqlText += orderBy + limitSQL
	_, err = commonUtils.GetSelectResp(ctx, sess, sqlText, &indicators, args)
	if err != nil && err != sql.ErrNoRows {
		return nil, err
	}
	var rv []*ivProto.IndicatorViewBriefWithContent
	for _, i := range indicators {
		var viewContent *base.AdsViewContent
		if viewContent, err = base.UnmarshalAdsViewContent(i.ViewContent); err != nil {
			viewContent = nil
		}
		rv = append(rv, &ivProto.IndicatorViewBriefWithContent{
			IndicatorViewBrief: i.IndicatorViewBrief,
			ViewContent:        viewContent,
		})
	}

	return rv, nil
}

func (s *IndicatorViewStore) ListIndicatorBriefs(ctx context.Context, project string, req ivProto.ListIndicatorViewReq) ([]*ivProto.IndicatorViewBrief, error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	var indicators []*ivProto.IndicatorViewBrief

	sqlText := "select id,code,physical_model_code,multi_dim_model_code,name,view_name,subject_id,modify_status,release_status,created_on,created_by,modified_on,modified_by,source_project_level,dev_mode,description from dap_m_indicator "
	args := map[string]interface{}{
		"skip": req.GetSkip(),
		"size": req.PageSize,
	}
	whereText := s.genWhere(args, req)
	sqlText += whereText

	// order by
	var orderBy string
	orderBy = " order by created_on "
	if req.Sorts != "" {
		if strings.Contains(req.Sorts, "name") {
			sort := strings.Split(req.Sorts, " ")
			if len(sort) == 2 {
				orderBy = fmt.Sprintf(" order by CONVERT(%s using gbk) %s ", sort[0], sort[1])
			}
		} else {
			orderBy = fmt.Sprintf(" order by %s ", req.Sorts)
		}
	}
	// limit
	limitSQL := " limit :skip, :size "

	sqlText += orderBy + limitSQL
	_, err = commonUtils.GetSelectResp(ctx, sess, sqlText, &indicators, args)
	if err != nil && err != sql.ErrNoRows {
		return nil, err
	}

	return indicators, nil
}

func (s *IndicatorViewStore) UpdateIndicatorParam(ctx context.Context, project string, req ivProto.UpdateIndicatorViewParamReq) (err error) {
	param, err := json.Marshal(req.Param)
	if err != nil {
		return
	}
	account := context_helper.MustGetSessionAccount(ctx)
	updates := make(map[string]interface{})
	updates["param"] = param
	updates["modified_by"] = account
	err = s.Update(ctx, project, req.ID, "indicator", updates)
	return
}

func (s *IndicatorViewStore) GetIndicator(ctx context.Context, project, indicatorID string) (indicator *ivBase.Indicator, err error) {
	var resp ivBase.Indicator
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	args := map[string]interface{}{"indicatorID": indicatorID}
	sqlText := "select id,code,environment,physical_model_code,multi_dim_model_code,name,view_name,description,subject_id,content," +
		"view_content,param,created_on,modified_on,created_by,modified_by,release_comment,version,resource,modify_status,release_status,disable_dimensional,source_project_level,source_project from dap_m_indicator where id = :indicatorID "
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	err = sess.GetContext(ctx, &resp, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return
	}
	if err == sql.ErrNoRows {
		err = modelErr.NewIndicatorNotExistsErr(indicatorID)
		return
	}
	return &resp, nil
}

func (s *IndicatorViewStore) GetIndicatorByCodeAndEnv(ctx context.Context, project, code string, env cBase.ProjectEnvType) (indicator *ivBase.Indicator, err error) {
	var resp ivBase.Indicator
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	args := map[string]interface{}{"code": code, "environment": env}
	sqlText := "select id,code,environment,physical_model_code,multi_dim_model_code,name,view_name,description," +
		"subject_id,`content`,view_content,param,created_on,release_comment,`version`,resource," +
		"modified_on,created_by,modified_by,release_status,modify_status,ref_type,disable_dimensional,source_project_level,source_project, edited_standard_flag,table_name,dev_mode from dap_m_indicator where code = :code and environment = :environment "
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	err = sess.GetContext(ctx, &resp, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return
	}
	return &resp, nil
}

func (s *IndicatorViewStore) GetIndicatorFields(ctx context.Context, project, indicatorID string) (fields []*ivBase.IndicatorField, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	args := map[string]interface{}{"indicatorID": indicatorID}
	sqlText := "select id,indicator_id,name,name_cn,description,expression,field_source,agg_on,`rank`,field_type,created_on," +
		"value_comment,modified_on,created_by,modified_by,indicator_business_code,code,field_code,business_model_field_id,length,scale," +
		"is_primary,primary_rank,dim_type,dim_view_data,field_business_type,group_name,parent_id" +
		" from dap_m_indicator_field where indicator_id = :indicatorID order by `rank` "
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	err = sess.SelectContext(ctx, &fields, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) GetIndicatorFieldsByCodeAndEnv(ctx context.Context, project, code string, env cBase.ProjectEnvType) (fields []*ivBase.IndicatorField, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	args := map[string]interface{}{"code": code, "environment": env}
	sqlText := "select id,indicator_id,name,name_cn,description,expression,field_source,agg_on,`rank`,field_type,value_comment,created_on," +
		"modified_on,created_by,modified_by,indicator_business_code,code from dap_m_indicator_field where indicator_id = (select id from dap_m_indicator where " +
		"code = :code and environment = :environment limit 1) order by `rank` "
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	err = sess.SelectContext(ctx, &fields, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) AddIndicatorUseTx(ctx context.Context, tx *sql.Tx, indicator *ivBase.Indicator) (string, error) {
	account, err := context_helper.GetSessionAccount(ctx)
	if err != nil {
		return "", err
	}
	indicator.CreatedBy = account
	indicator.ModifiedBy = account
	if indicator.ID == "" {
		indicator.ID = pkg.NewMysqlID()
	}
	deleteSql := fmt.Sprintf("delete from dap_m_indicator where id = '%s'", indicator.ID)
	_, err = tx.ExecContext(ctx, deleteSql)
	if err != nil {
		return "", err
	}

	replaceSQL := "insert into dap_m_indicator (id,code,physical_model_code,multi_dim_model_code,environment,name," +
		"view_name,description,subject_id,version,release_comment,resource," +
		"content,view_content,param,created_by,modified_by,ref_type,disable_dimensional,table_name,dev_mode,source_project_level, edited_standard_flag,created_on) " +
		"values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	_, err = tx.ExecContext(ctx, replaceSQL, indicator.ID, indicator.Code, indicator.PhysicalModelCode,
		indicator.MultiDimModelCode, indicator.Environment,
		indicator.Name, indicator.ViewName, indicator.Description, indicator.SubjectID, indicator.Version,
		indicator.ReleaseComment, indicator.Resource, indicator.Content,
		indicator.ViewContent, indicator.Param, indicator.CreatedBy, indicator.ModifiedBy, indicator.RefType, indicator.DisableDimensional, indicator.TableName, indicator.DevMode, indicator.SourceProjectLevel, indicator.EditedStandardFlag, indicator.CreatedOn.Time.Format("2006-01-02 15:04:05"))
	if err != nil {
		return "", err
	}
	return indicator.Code, nil
}

func (s *IndicatorViewStore) UpdateIndicatorFieldsUseTx(ctx context.Context, tx *sql.Tx, indicatorID string, fieldOrder []*physicalModelProto.GroupFieldsOrder, fields []*ivBase.IndicatorField) (err error) {
	account, err := context_helper.GetSessionAccount(ctx)
	if err != nil {
		return err
	}
	args := map[string]interface{}{"indicatorID": indicatorID}
	deleteFieldSQL := "delete from dap_m_indicator_field where indicator_id = :indicatorID "
	query, queryArgs, err := sqlx.Named(deleteFieldSQL, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	stmt, err1 := tx.Prepare(query)
	if err1 != nil {
		err = err1
		return
	}
	_, err = stmt.ExecContext(ctx, queryArgs...)
	if err != nil {
		return
	}
	// newIndicatorId := ""
	for _, field := range fields {
		// newIndicatorId = field.IndicatorID
		fieldArgs := map[string]interface{}{
			"id":                      field.ID,
			"indicatorID":             field.IndicatorID,
			"name":                    field.Name,
			"nameCn":                  field.NameCn,
			"description":             field.Description,
			"expression":              field.Expression,
			"fieldSource":             field.FieldSource,
			"aggOn":                   field.AggOn,
			"rank":                    field.Rank,
			"fieldType":               field.FieldType,
			"valueComment":            field.ValueComment,
			"createdBy":               account,
			"modifiedBy":              account,
			"indicator_business_code": field.IndicatorBusinessCode,
			"code":                    field.Code,
			"field_code":              field.FieldCode,
			"business_model_field_id": field.BusinessModelFieldId,
			"length":                  field.Length,
			"scale":                   field.Scale,
			"is_primary":              field.IsPrimary,
			"primary_rank":            field.PrimaryRank,
			"dim_type":                field.DimType,
			"dim_view_data":           field.DimViewData.String,
			"field_business_type":     field.FieldBusinessType,
			"group_name":              field.GroupName,
			"parent_id":               field.ParentId,
		}
		sqlText := "insert into dap_m_indicator_field (id,indicator_id,name,name_cn,description,expression,field_source," +
			"agg_on,`rank`,field_type,value_comment,created_by,modified_by,indicator_business_code,code,field_code," +
			"business_model_field_id,length,scale,is_primary,primary_rank,dim_type,dim_view_data,field_business_type,group_name,parent_id) " +
			" values (:id,:indicatorID,:name,:nameCn,:description,:expression,:fieldSource," +
			":aggOn,:rank,:fieldType,:valueComment,:createdBy,:modifiedBy,:indicator_business_code,:code,:field_code," +
			":business_model_field_id,:length,:scale,:is_primary,:primary_rank,:dim_type,:dim_view_data,:field_business_type,:group_name,:parent_id) "
		query, queryArgs, err1 := sqlx.Named(sqlText, fieldArgs)
		if err1 != nil {
			err = err1
			return
		}
		query, queryArgs, err1 = sqlx.In(query, queryArgs...)
		if err1 != nil {
			err = err1
			return
		}
		stmt, err1 := tx.Prepare(query)
		if err1 != nil {
			err = err1
			return
		}
		_, err1 = stmt.ExecContext(ctx, queryArgs...)
		if err1 != nil {
			err = err1
			return
		}
	}

	//删除分组字段
	args["physical_model_id"] = indicatorID
	deleteFieldGroupSQL := "delete from dap_m_physical_model_field_group where physical_model_id = :physical_model_id"
	query, queryArgs, err = sqlx.Named(deleteFieldGroupSQL, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	stmt, err1 = tx.Prepare(query)
	if err1 != nil {
		err = err1
		return
	}
	_, err = stmt.ExecContext(ctx, queryArgs...)
	if err != nil {
		return
	}
	groupFieldsOrderBytes, internalErr := json.Marshal(fieldOrder)
	if internalErr != nil {
		return internalErr
	}

	//新增字段分组信息
	fieldOrderArgs := map[string]interface{}{
		"id":                pkg.NewMysqlID(),
		"physical_model_id": indicatorID,
		"field_group_view":  string(groupFieldsOrderBytes),
		"created_on":        time.Now(),
		"modified_on":       time.Now(),
		"created_by":        account,
		"modified_by":       account,
	}
	sqlText := "insert into dap_m_physical_model_field_group (id,physical_model_id,field_group_view,created_on,modified_on,created_by,modified_by) " +
		" values (:id,:physical_model_id,:field_group_view,:created_on,:modified_on,:created_by,:modified_by) "
	query, queryArgs, err1 = sqlx.Named(sqlText, fieldOrderArgs)
	if err1 != nil {
		err = err1
		return
	}
	_, err1 = tx.ExecContext(ctx, query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	return
}

func (s *IndicatorViewStore) UpdateIndicatorUseTx(ctx context.Context, tx *sql.Tx, indicator *ivBase.Indicator) (err error) {
	account, err := context_helper.GetSessionAccount(ctx)
	if err != nil {
		return err
	}
	args := map[string]interface{}{
		"id":                   indicator.ID,
		"name":                 indicator.Name,
		"description":          indicator.Description,
		"subject_id":           indicator.SubjectID,
		"content":              indicator.Content,
		"viewContent":          indicator.ViewContent,
		"physical_model_code":  indicator.PhysicalModelCode,
		"multi_dim_model_code": indicator.MultiDimModelCode,
		"modify_status":        indicator.ModifyStatus,
		"account":              account,
		"ref_type":             indicator.RefType,
		"disable_dimensional":  indicator.DisableDimensional,
		"edited_standard_flag": indicator.EditedStandardFlag,
	}
	updateSQL := "update dap_m_indicator set name = :name, description = :description, view_content = :viewContent, modify_status = :modify_status, " +
		"modified_by = :account, content = :content, physical_model_code = :physical_model_code,ref_type = :ref_type," +
		"multi_dim_model_code = :multi_dim_model_code, subject_id = :subject_id,disable_dimensional = :disable_dimensional,edited_standard_flag = :edited_standard_flag "
	if indicator.ViewName != "" {
		updateSQL += ", view_name= :viewName "
		args["viewName"] = indicator.ViewName
	}
	if indicator.TableName != "" {
		updateSQL += ", table_name= :tableName "
		args["tableName"] = indicator.TableName
	}
	updateSQL += " where id = :id"

	query, queryArgs, err1 := sqlx.Named(updateSQL, args)
	if err1 != nil {
		err = err1
		return
	}
	query, queryArgs, err1 = sqlx.In(query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	_, err1 = tx.ExecContext(ctx, query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	return
}

func (s *IndicatorViewStore) DeleteIndicatorUseTx(ctx context.Context, tx *sql.Tx, indicatorID string) (err error) {
	args := map[string]interface{}{"indicatorID": indicatorID}
	deleteSQL := "delete from dap_m_indicator where id = :indicatorID "
	query, queryArgs, err1 := sqlx.Named(deleteSQL, args)
	if err1 != nil {
		err = err1
		return
	}
	query, queryArgs, err1 = sqlx.In(query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	_, err1 = tx.ExecContext(ctx, query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	return
}

func (s *IndicatorViewStore) DeleteIndicatorUseTxByCode(ctx context.Context, tx *sql.Tx, code string) (err error) {
	args := map[string]interface{}{"code": code}
	deleteSQL := "delete from dap_m_indicator where code = :code "
	query, queryArgs, err1 := sqlx.Named(deleteSQL, args)
	if err1 != nil {
		err = err1
		return
	}
	query, queryArgs, err1 = sqlx.In(query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	_, err1 = tx.ExecContext(ctx, query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	return
}

func (s *IndicatorViewStore) DeleteModelCollect(ctx context.Context, tx *sql.Tx, code string) (err error) {
	args := map[string]interface{}{"code": code}
	deleteSQL := "delete from dap_m_model_collect where model_code = :code "
	query, queryArgs, err1 := sqlx.Named(deleteSQL, args)
	if err1 != nil {
		err = err1
		return
	}
	query, queryArgs, err1 = sqlx.In(query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	_, err1 = tx.ExecContext(ctx, query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	return
}

func (s *IndicatorViewStore) DeleteIndicatorFieldUseTx(ctx context.Context, tx *sql.Tx, indicatorID string) (err error) {
	args := map[string]interface{}{"indicatorID": indicatorID}
	deleteSQL := "delete from dap_m_indicator_field where indicator_id = :indicatorID "
	query, queryArgs, err1 := sqlx.Named(deleteSQL, args)
	if err1 != nil {
		err = err1
		return
	}
	query, queryArgs, err1 = sqlx.In(query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	_, err1 = tx.ExecContext(ctx, query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	return
}

func (s *IndicatorViewStore) DeleteIndicatorFieldUseTxByCode(ctx context.Context, tx *sql.Tx, code string) (err error) {
	args := map[string]interface{}{"code": code}
	deleteSQL := "delete from dap_m_indicator_field where indicator_id in (select id from dap_m_indicator where code=:code) "
	query, queryArgs, err1 := sqlx.Named(deleteSQL, args)
	if err1 != nil {
		err = err1
		return
	}
	query, queryArgs, err1 = sqlx.In(query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	_, err1 = tx.ExecContext(ctx, query, queryArgs...)
	if err1 != nil {
		err = err1
		return
	}
	return
}

func (s *IndicatorViewStore) ExistNameCn(ctx context.Context, project string, name string, excludeCode string) (exist bool, err error) {
	var sqlStore *sqlx.DB
	sqlStore, err = s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	var count int64
	err = sqlStore.GetContext(ctx, &count,
		"select count(*) from dap_m_indicator where name=? and code != ? and environment != '历史'", name, excludeCode)
	if err != nil {
		return
	}
	return count >= 1, nil
}

func (s *IndicatorViewStore) ExistName(ctx context.Context, project string, name string, excludeCode string) (exist bool, err error) {
	var sqlStore *sqlx.DB
	sqlStore, err = s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	var count int64
	err = sqlStore.GetContext(ctx, &count,
		"select count(*) from dap_m_indicator where view_name=? and code != ? and environment != '历史'", name, excludeCode)
	if err != nil {
		return
	}
	return count >= 1, nil
}

func (s *IndicatorViewStore) ExistPhysicalModelName(ctx context.Context, project string, name string, excludeCode string) (exist bool, err error) {
	var sqlStore *sqlx.DB
	sqlStore, err = s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	var count int64
	err = sqlStore.GetContext(ctx, &count,
		"select count(*) from dap_m_physical_model where `table_name`=? and code != ? and environment != '历史' and is_del = 0", name, excludeCode)
	if err != nil {
		return
	}
	return count >= 1, nil
}

func (s *IndicatorViewStore) GetIndicatorViewCount(ctx context.Context, project string, env cBase.ProjectEnvType) (count int64, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	args := map[string]interface{}{"environment": env}
	sqlText := "select count(*) as count from dap_m_indicator where environment=:environment"
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	err = sess.GetContext(ctx, &count, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) GetIndicatorViewCountBySubject(ctx context.Context, project string, env cBase.ProjectEnvType) (rsp ivProto.GetIndicatorCountBySubjectRsp, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	args := map[string]interface{}{"environment": env}
	sqlText := "select subject_id, count(*) as count from dap_m_indicator where environment=:environment group by subject_id"
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	err = sess.SelectContext(ctx, &rsp, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) BatchGetModelFieldCount(ctx context.Context, project string, req ivProto.BatchGetModelFieldCountReq) (result map[string]*ivProto.BatchGetModelFieldCountRspItem, err error) {
	sqlText := "select indicator_id as id, count(*) as count from dap_m_indicator_field where indicator_id in (:model_ids) and field_source='custom' group by indicator_id"
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}

	result = map[string]*ivProto.BatchGetModelFieldCountRspItem{}
	if len(req.ModelIDs) == 0 {
		return
	}

	args := map[string]interface{}{
		"model_ids": req.ModelIDs,
	}

	var aggDatas []*ivProto.BatchGetModelFieldCountRspItem
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	if err = sess.SelectContext(ctx, &aggDatas, query, queryArgs...); err != nil {
		return nil, err
	}

	for _, item := range aggDatas {
		result[item.ID] = item
	}
	return
}

func (s *IndicatorViewStore) GetPhysicalModelResource(ctx context.Context, project string, codes []string, env cBase.ProjectEnvType) (resources []entities.ProjectResourceType, err error) {
	resources = []entities.ProjectResourceType{}

	if len(codes) == 0 {
		return
	}

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	// 获取开发环境的物理模型的code
	sqlText := "select resource from dap_m_physical_model where code in (:codes) and environment=:environment"
	args := map[string]interface{}{
		"codes":       codes,
		"environment": env,
	}

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	err = sess.SelectContext(ctx, &resources, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return
	}
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

func (s *IndicatorViewStore) GetAllProdViewAds(ctx context.Context, project string) (indicators []*ivBase.Indicator, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	sqlText := "select id,code,environment,physical_model_code,multi_dim_model_code,name,view_name,description,subject_id,content," +
		"view_content,param,created_on,modified_on,created_by,modified_by,release_comment,version,resource,source_project_level,source_project from dap_m_indicator where environment = '生产' "

	_, err = commonUtils.GetSelectResp(ctx, sess, sqlText, &indicators, nil)
	if err != nil {
		return
	}

	return
}

func (s *IndicatorViewStore) GetAdsViewRelationsByUpstreamPhysicalModelCodes(ctx context.Context, project string, codes []string) ([]*modelProto.ModelRefRelation, error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	args := map[string]interface{}{"codes": codes}

	sqlText := "select distinct physical_model_code as from_code, code as to_code " +
		"from dap_m_indicator where physical_model_code in (:codes) and environment in ('开发', '生产') "
	var relations []*modelProto.ModelRefRelation
	_, err = commonUtils.GetSelectResp(ctx, sess, sqlText, &relations, args)
	if err != nil {
		return nil, err
	}
	return relations, nil
}

func (s *IndicatorViewStore) GetAdsViewRelationsByUpstreamMultiDimModelCodes(ctx context.Context, project string, codes []string) ([]*modelProto.ModelRefRelation, error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	args := map[string]interface{}{"codes": codes}

	sqlText := "select distinct multi_dim_model_code as from_code, code as to_code " +
		"from dap_m_indicator where multi_dim_model_code in (:codes) and environment in ('开发','生产') "
	var relations []*modelProto.ModelRefRelation
	_, err = commonUtils.GetSelectResp(ctx, sess, sqlText, &relations, args)
	if err != nil {
		return nil, err
	}
	return relations, nil
}

func (s *IndicatorViewStore) BatchUpdateStatus(ctx context.Context, tx *sql.Tx, code, releaseStatus string, modifyStatus modelBase.ModifyStatus, ApplyStatus int, flag int32) (err error) {
	args := map[string]interface{}{
		"release_status":           releaseStatus,
		"modify_status":            modifyStatus,
		"apply_status":             ApplyStatus,
		"all_dimension_are_public": flag,
		"code":                     code,
	}

	updateSQL := `update dap_m_indicator 
set release_status = :release_status,
	modify_status = :modify_status,
	apply_status = :apply_status,
	all_dimension_are_public = :all_dimension_are_public
where code = :code and environment != '历史' `
	query, queryArgs, err := sqlx.Named(updateSQL, args)
	if err != nil {
		return
	}

	_, err = tx.ExecContext(ctx, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) UpdateModifyStatus(ctx context.Context, tx *sql.Tx, code string, modifyStatus modelBase.ModifyStatus) (err error) {
	args := map[string]interface{}{
		"modify_status": modifyStatus,
		"code":          code,
	}

	updateSQL := "update dap_m_indicator set modify_status = :modify_status where code = :code and environment = '开发' "
	query, queryArgs, err := sqlx.Named(updateSQL, args)
	if err != nil {
		return
	}

	_, err = tx.ExecContext(ctx, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) UpdateReleaseStatus(ctx context.Context, tx *sql.Tx, code, releaseStatus string) (err error) {
	args := map[string]interface{}{
		"release_status": releaseStatus,
		"code":           code,
	}

	updateSQL := "update dap_m_indicator set release_status = :release_status where code = :code and environment != '历史' "
	query, queryArgs, err := sqlx.Named(updateSQL, args)
	if err != nil {
		return
	}

	_, err = tx.ExecContext(ctx, query, queryArgs...)
	if err != nil {
		return
	}
	return
}
func (s *IndicatorViewStore) UpdateApplyStatus(ctx context.Context, tx *sql.Tx, code string, ApplyStatus int) (err error) {
	args := map[string]interface{}{
		"apply_status": ApplyStatus,
		"code":         code,
	}

	updateSQL := "update dap_m_indicator set apply_status = :apply_status where code = :code  and environment != '历史'"
	query, queryArgs, err := sqlx.Named(updateSQL, args)
	if err != nil {
		return
	}

	_, err = tx.ExecContext(ctx, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) UpdateAllDimensionArePublic(ctx context.Context, tx *sql.Tx, code string, flag int32) (err error) {
	args := map[string]interface{}{
		"all_dimension_are_public": flag,
		"code":                     code,
	}

	updateSQL := "update dap_m_indicator set all_dimension_are_public = :all_dimension_are_public where code = :code and environment != '历史' "
	query, queryArgs, err := sqlx.Named(updateSQL, args)
	if err != nil {
		return
	}

	_, err = tx.ExecContext(ctx, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) UpdateSubjectByCode(ctx context.Context, tx *sql.Tx, code, subjectId string) error {
	args := map[string]interface{}{
		"subject_id": subjectId,
		"code":       code,
	}

	updateSQL := "update dap_m_indicator set subject_id = :subject_id where code = :code and environment != '历史' "
	query, queryArgs, err := sqlx.Named(updateSQL, args)
	if err != nil {
		return err
	}

	_, err = tx.ExecContext(ctx, query, queryArgs...)
	if err != nil {
		return err
	}

	return nil
}

func (s *IndicatorViewStore) ListIndicatorByCodesEnv(ctx context.Context, project string, codes []string, envs []cBase.ProjectEnvType) (indicators []ivBase.Indicator, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	sqlText := "select id,code,subject_id,environment from dap_m_indicator where code in(:codes) and environment in (:envs);"
	args := map[string]interface{}{
		"codes": codes,
		"envs":  envs,
	}

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}

	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}

	err = sess.SelectContext(ctx, &indicators, query, queryArgs...)
	return
}

func (s *IndicatorViewStore) GetDepCodes(ctx context.Context, project, code string, category modelBase.ModelRelationCategory, env cBase.ProjectEnvType) (codes []string, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	sqlText := "select code from dap_m_indicator where physical_model_code = :code and environment = :environment;"
	if category == modelBase.MultiDimModel {
		sqlText = "select code from dap_m_indicator where multi_dim_model_code = :code and environment = :environment;"
	}

	args := map[string]interface{}{
		"code":        code,
		"environment": env,
	}

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}

	err = sess.SelectContext(ctx, &codes, query, queryArgs...)
	if err != nil {
		return
	}

	return
}

func (s *IndicatorViewStore) GetAllCodes(ctx context.Context, project string) (codes []string, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	sqlText := "select distinct code from dap_m_indicator;"
	err = sess.SelectContext(ctx, &codes, sqlText)
	if err != nil {
		return
	}

	return
}

func (s *IndicatorViewStore) UpdateIndicatorSourceInfo(ctx context.Context, project string, codes []string, sourceProject string, sourceProjectLevel int) (err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	args := map[string]interface{}{
		"source_project_level": sourceProjectLevel,
		"codes":                codes,
		"source_project":       sourceProject,
	}

	updateSQL := "update dap_m_indicator set source_project_level = :source_project_level, source_project = :source_project where code in (:codes)"
	query, queryArgs, err := sqlx.Named(updateSQL, args)
	if err != nil {
		return
	}

	_, err = sess.ExecContext(ctx, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) UpdateIndicatorRefTypeInfoById(ctx context.Context, project string, id string, refType string) (err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	args := map[string]interface{}{
		"ref_type": refType,
		"id":       id,
	}

	updateSQL := "update dap_m_indicator set ref_type = :ref_type where id = :id"
	query, queryArgs, err := sqlx.Named(updateSQL, args)
	if err != nil {
		return
	}

	_, err = sess.ExecContext(ctx, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) UpdateIndicatorFieldsSourceInfo(ctx context.Context, project string, indicatorIds []string, sourceProject string, sourceProjectLevel int) (err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	args := map[string]interface{}{
		"source_project_level": sourceProjectLevel,
		"ids":                  indicatorIds,
		"source_project":       sourceProject,
	}

	updateSQL := "update dap_m_indicator set source_project_level = :source_project_level, source_project = :source_project where indicator_id in (:ids)"
	query, queryArgs, err := sqlx.Named(updateSQL, args)
	if err != nil {
		return
	}

	_, err = sess.ExecContext(ctx, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) GetIndicatorFieldsByIndicatorCodes(ctx context.Context, project string, codes []string) (fields []*ivBase.IndicatorField, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	sqlText := "select dap_m_indicator_field.id, dap_m_indicator_field.indicator_id,dap_m_indicator_field.name,dap_m_indicator_field.name_cn from dap_m_indicator_field inner join dap_m_indicator on dap_m_indicator_field.indicator_id = indicator.id   where dap_m_indicator.code in (:codes) "

	args := map[string]interface{}{
		"codes": codes,
	}

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}

	err = sess.SelectContext(ctx, &codes, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorViewStore) GetIndicatorByCodes(ctx context.Context, project string, codes []string) (indicatorList []*ivBase.Indicator, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	args := map[string]interface{}{"codes": codes}
	sqlText := fmt.Sprintf("select id,code,view_content " +
		"from dap_m_indicator where code in (:codes) ",
	)
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	err = sess.GetContext(ctx, &indicatorList, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return
	}
	return
}

func (s *IndicatorViewStore) GetIndicatorFieldsGroup(ctx context.Context, project, indicatorID string) (fields []*physicalModelProto.GroupFieldsOrder, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	fields = make([]*physicalModelProto.GroupFieldsOrder, 0)
	orderFieldConfig := ""
	err = sess.GetContext(ctx, &orderFieldConfig, "select field_group_view from dap_m_physical_model_field_group where physical_model_id = ? ", indicatorID)
	if err != nil && err != sql.ErrNoRows {
		return
	}
	err = nil
	if orderFieldConfig != "" {
		err = json.Unmarshal([]byte(orderFieldConfig), &fields)
		if err != nil {
			return
		}
	}
	return
}

func (s *IndicatorViewStore) AddPhysicalModelDep(ctx context.Context, tx *sql.Tx, env string, modelCode string, depModels []*ivBase.PhysicalModelDependence) (err error) {

	account, err := context_helper.GetSessionAccount(ctx)
	if err != nil {
		return err
	}
	_, err = tx.Exec("delete from dap_m_physical_model_dependence where code = ? and environment = ?", modelCode, env)
	if err != nil {
		return err
	}

	insertSQL := "insert into dap_m_physical_model_dependence (id,code,physical_model_code,dep_model_code,environment,created_on," +
		"modified_on,created_by,modified_by)" +
		"values(?,?,?,?,?,?,?,?,?)"
	stmt, err := tx.Prepare(insertSQL)
	if err != nil {
		return err
	}

	for _, dep := range depModels {
		dep.CreatedOn = time.Now()
		dep.ModifiedOn = time.Now()
		dep.CreatedBy = account
		dep.ModifiedBy = account
		dep.Environment = env
		_, err = stmt.ExecContext(ctx, dep.Id, dep.Code, dep.PhysicalModelCode, dep.DepModelCode, dep.Environment, dep.CreatedOn, dep.ModifiedOn, dep.CreatedBy, dep.ModifiedBy)
		if err != nil {
			return err
		}

	}
	return
}

func (s *IndicatorViewStore) GetPhysicalModelDepByCode(ctx context.Context, project, env string, modelCode string) (result []*ivBase.PhysicalModelDependence, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	result = make([]*ivBase.PhysicalModelDependence, 0)
	err = sess.GetContext(ctx, &result, "select * from dap_m_physical_model_dependence where physical_model_code = ? and env = ? ", modelCode, env)
	if err != nil && err != sql.ErrNoRows {
		return
	}
	return
}
