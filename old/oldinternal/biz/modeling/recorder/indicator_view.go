package recorder

import (
	"context"
	"time"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	ivBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/indicator_view/base"
	logicBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	subjectRepo "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/subject/repo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	fastBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/base"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewIndicatorViewFastRecorder))
}

type IndicatorViewFastRecorder struct {
	subjectRepo subjectRepo.SubjectRepo
}

func NewIndicatorViewFastRecorder(repo subjectRepo.SubjectRepo) *IndicatorViewFastRecorder {
	return &IndicatorViewFastRecorder{
		subjectRepo: repo,
	}
}

func (s *IndicatorViewFastRecorder) GenerateReleaseModelFastBizContent(ctx context.Context, project string, model *ivBase.Indicator, fields []*ivBase.IndicatorField) fastBase.BizParamsGenerator {
	return func() map[string]interface{} {
		content := make(map[string]interface{})
		now := time.Now().Format("2006-01-02 15:04:05")
		content["release_time"] = now
		content["log_time"] = now
		// 数据域id
		content["subject_id"] = model.SubjectID
		// 模型名称
		// content.ModelName = model.ViewName
		content["physical_model_name"] = model.Name
		// 模型ID
		content["physical_model_code"] = model.Code
		// 模型分类
		content["model_type"] = "指标模型" //fast.AdsModelCategory

		// 字段信息
		content["filed_num"] = 0
		content["metric_num"] = 0
		content["indicator_num"] = statisticIndicatorCount(model.ViewContent, fields)
		// 数据域名称
		subject, err := s.subjectRepo.GetSubjectById(ctx, project, model.SubjectID)
		if err == nil {
			content["subject_name"] = subject.Name
		}
		return content
	}
}

func statisticIndicatorCount(viewContentStr string, fields []*ivBase.IndicatorField) int {
	viewContent, err := logicBase.UnmarshalAdsViewContent(viewContentStr)
	if err != nil {
		return 0
	}
	indicatorMap := make(map[string]rpc_call.Prop)
	for _, prop := range viewContent.CustomFieldDimensions {
		k := getRealPropObjKey(prop)
		indicatorMap[k] = prop
	}
	count := 0
	for _, field := range fields {
		_, ok := indicatorMap[field.Name]
		if ok {
			count += 1
		}
	}
	return count
}

func getRealPropObjKey(prop rpc_call.Prop) string {
	k := prop.PropName
	if prop.Alias != "" {
		k = prop.Alias
	}
	return k
}
