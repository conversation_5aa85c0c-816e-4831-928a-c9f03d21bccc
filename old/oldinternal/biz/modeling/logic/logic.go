package logic

import (
	"github.com/defval/inject/v2"
	dap_common "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common"
	dap_indicator_modeling "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-indicator-modeling"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/moql"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/sqlce"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewModelLogic))
}

type ModelLogic struct {
	logger            *logger.Logger
	sqlCeCaller       sqlce.SqlceCallerIface
	dapCommon         dap_common.DapCommonService
	indicatorModeling dap_indicator_modeling.IndicatorModeling
	moqlService       moql.MoqlService
}

func NewModelLogic(logger *logger.Logger) *ModelLogic {
	return &ModelLogic{
		logger:            logger,
		sqlCeCaller:       sqlce.NewSqlceHttpCaller(),
		dapCommon:         dap_common.NewDapCommonServiceS(),
		indicatorModeling: dap_indicator_modeling.NewIndicatorModelingServiceS(),
		moqlService:       moql.NewMoqlServiceS(),
	}
}
