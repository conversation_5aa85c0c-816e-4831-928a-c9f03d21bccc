package base

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
)

type FormulaType string

const (
	DefaultFormulaType FormulaType = "default"
	CustomFormulaType  FormulaType = "custom"
)

type SystemFieldType string

const (
	String SystemFieldType = "STRING"
	Double SystemFieldType = "DOUBLE"
	Bigint SystemFieldType = "BIGINT"
)

type Field struct {
	IsHide               bool   `json:"is_hide"`
	Name                 string `json:"name"`
	NameCn               string `json:"name_cn"`
	FieldType            string `json:"field_type"`
	AliasName            string `json:"alias_name"`
	Description          string `json:"description"`
	BusinessModelFieldID string `json:"business_model_field_id"`
	PhysicalModelFieldID string `json:"physical_model_field_id"`
	SystemFieldType      string `json:"system_field_type"`
}

type Node struct {
	TableName string             `json:"table_name"`
	Fields    []Field            `json:"fields"`
	Id        string             `json:"id"`
	CodeId    string             `json:"code_id"`
	Category  base.ModelCategory `json:"category"`
	Name      string             `json:"name"`
}

type JoinField struct {
	Left            string `json:"left"`
	Right           string `json:"right"`
	Operator        string `json:"operator"`
	LogicalRelation string `json:"logical_relation"`
}

type Link struct {
	TableName  string      `json:"table_name"`
	Fields     []JoinField `json:"join_fields"`
	FromId     string      `json:"from_id"`
	ToId       string      `json:"to_id"`
	JoinType   string      `json:"join_type"`
	JoinWheres interface{} `json:"join_wheres"`
}
