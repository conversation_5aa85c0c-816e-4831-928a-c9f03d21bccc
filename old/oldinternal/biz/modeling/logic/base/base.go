package base

import (
	"context"
	dap_indicator_modeling "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-indicator-modeling"

	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
)

// 模型逻辑定义模式
type ModelLogicMode string

const (
	LogicSQLMode  ModelLogicMode = "sql"
	LogicViewMode ModelLogicMode = "view"
)

type Logic interface {
	// 生成执行sql语句
	GenExecuteSQL(ctx context.Context, resource *cBase.ProjectResource, devMode string) (executeSQL string, err error)
}

type logicBase struct {
	metadata          *AdsViewContent
	indicatorModeling dap_indicator_modeling.IndicatorModeling
	project           string
}

type RelationTableFieldExtra struct {
	FieldNameCn     string `json:"field_name_cn"`
	FieldDesc       string `json:"field_desc"`
	FieldType       string `json:"field_type"`
	SystemFieldType string `json:"system_field_type"`
	FieldAlias      string `json:"field_alias"`
}

type RelationTableField struct {
	TableName string `json:"table_name"`
	FieldName string `json:"field_name"`
	RelationTableFieldExtra
}

func NewLogic(project string, viewContent *AdsViewContent) (logic Logic, err error) {
	logicBase := logicBase{
		metadata:          viewContent,
		indicatorModeling: dap_indicator_modeling.NewIndicatorModelingServiceS(),
		project:           project,
	}
	logic = &ViewLogic{logicBase: logicBase}

	return
}
