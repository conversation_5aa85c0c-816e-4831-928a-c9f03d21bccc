package base

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"regexp"
	"strings"
	"time"

	dap_indicator_modeling "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-indicator-modeling"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/ads_common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"

	"github.com/pkg/errors"

	indicator_utils "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/indicator_view/utils"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

func UnmarshalAdsViewContent(viewContentStr string) (viewContent *AdsViewContent, err error) {
	// 将大写关联关系替换为小写
	viewContentStr = strings.ReplaceAll(viewContentStr, "LEFT JOIN", "left join")
	viewContentStr = strings.ReplaceAll(viewContentStr, "INNER JOIN", "inner join")
	err = json.Unmarshal([]byte(viewContentStr), &viewContent)
	if err != nil {
		return
	}
	for i, f := range viewContent.CustomFieldDimensions {
		if f.Mode == rpc_call.AdvanceProp {
			err = json.Unmarshal([]byte(viewContent.CustomFieldDimensions[i].DialectSettingContentStr), &viewContent.CustomFieldDimensions[i].DialectSettingContent)
			if err != nil {
				return
			}
		}
	}
	return
}

type AdsViewContentI interface {
	GetDependence() (dependence *rpc_call.Dependence, err error)
	GetIndicators() (indicators []*rpc_call.Prop)
	GetIndicatorMap() (indicatorMap map[string]*rpc_call.Prop)
	GetDimensions() (dimensions []*rpc_call.Prop)
	GetDimensionMap() (dimensionMap map[string]*rpc_call.Prop)
	FillUpExtraSaveInfo(resource entities.ProjectResourceType) (err error)
	ValidViewContent() (err error)
}

var _ AdsViewContentI = (*AdsViewContent)(nil)
var _ json.Unmarshaler = (*AdsViewContent)(nil)
var _ json.Marshaler = (*AdsViewContent)(nil)

// ads ViewContent
type AdsViewContent struct {
	GlobalFilter          []rpc_call.ModelFilter  `json:"global_filter"`         // 全局过滤条件
	Dimensions            []rpc_call.Prop         `json:"dimensions"`            // 维度
	Dependence            *rpc_call.Dependence    `json:"dependence"`            // 依赖的模型
	DetailRelationList    []rpc_call.RelationItem `json:"detail_relation_list"`  // 明细关联模型列表数据
	CustomFieldDimensions []rpc_call.Prop         `json:"customFieldDimensions"` // 指标
	Variables             []rpc_call.Variable     `json:"variables"`             // 变量
	EmptyVarSelectAll     bool                    `json:"empty_var_select_all"`  // 全局变量过滤条件是否选择全部值
}

type ModelDep struct {
	PhysicalModelCode string `json:"physical_model_code"`
	DepModelCode      string `json:"dep_model_code"`
	Environment       string `json:"environment"`
}

type tmpJsonAdsViewContent AdsViewContent

func (s *AdsViewContent) MarshalJSON() ([]byte, error) {
	if s.Variables == nil {
		s.Variables = []rpc_call.Variable{}
	}
	raw := tmpJsonAdsViewContent(*s)
	return json.Marshal(raw)
}

func (s *AdsViewContent) UnmarshalJSON(bytes []byte) error {
	var raw tmpJsonAdsViewContent
	if err := json.Unmarshal(bytes, &raw); err != nil {
		return err
	}
	*s = AdsViewContent(raw)
	if s.Variables == nil {
		s.Variables = []rpc_call.Variable{}
	}
	return nil
}

func (s *AdsViewContent) GetIndicators() (indicators []*rpc_call.Prop) {
	for i := range s.CustomFieldDimensions {
		indicator := &s.CustomFieldDimensions[i]
		if indicator.Mode == "" {
			// 过滤掉空数据
			continue
		}
		indicators = append(indicators, indicator)
	}
	return
}

func (s *AdsViewContent) GetIndicatorMap() (indicatorMap map[string]*rpc_call.Prop) {
	indicatorMap = map[string]*rpc_call.Prop{}
	for i := range s.CustomFieldDimensions {
		indicator := &s.CustomFieldDimensions[i]
		if indicator.Mode == "" || indicator.Alias == "" {
			// 过滤掉空数据
			continue
		}
		indicatorMap[indicator.Alias] = indicator
	}
	return
}

func (s *AdsViewContent) GetDimensions() (dimensions []*rpc_call.Prop) {
	for i := range s.Dimensions {
		dimension := &s.Dimensions[i]
		if dimension.Alias == "" {
			// 过滤掉空数据
			continue
		}
		dimensions = append(dimensions, dimension)
	}
	return
}

func (s *AdsViewContent) GetDimensionMap() (dimensionMap map[string]*rpc_call.Prop) {
	dimensionMap = map[string]*rpc_call.Prop{}
	for i := range s.Dimensions {
		dimension := &s.Dimensions[i]
		if dimension.Alias == "" {
			// 过滤掉空数据
			continue
		}
		dimensionMap[dimension.Alias] = dimension
	}
	return
}

func (s *AdsViewContent) FillUpExtraSaveInfo(resource entities.ProjectResourceType) (err error) {
	// 填充一些其他必要信息，用于后续的处理（有顺序要求）
	// 1. 简单模式指标根据配置信息 填充 最终表达式
	// 2. 解析每个指标中引用的字段内容
	// 3. 解析指标中引用的聚合函数信息
	//todo 暂时不填充指标表达式
	if err = s.fillupIndicatorsExpression(resource); err != nil {
		return
	}
	if err = s.FillupIndicatorsDepFields(); err != nil {
		return
	}

	for i, f := range s.CustomFieldDimensions {
		if f.Mode == rpc_call.AdvanceProp {
			err = json.Unmarshal([]byte(s.CustomFieldDimensions[i].DialectSettingContentStr), &s.CustomFieldDimensions[i].DialectSettingContent)
			if err != nil {
				return err
			}
		}
	}

	return
}

func (s *AdsViewContent) FillUpDimensionViewData(dict map[string]*base.FieldViewModel) error {
	// 统计粒度视图数据填充
	for i := 0; i < len(s.Dimensions); i++ {
		realDimViewData, ok := dict[s.Dimensions[i].PropName]
		if !ok {
			return errors.Errorf("维度定义[%s]不存在, 请检查参数", s.Dimensions[i].PropName)
		}
		s.Dimensions[i].ViewModel = realDimViewData
	}
	return nil
}

func (s *AdsViewContent) fillupIndicatorsExpression(resource entities.ProjectResourceType) (err error) {
	// 简单模式填充表达式
	simpleIndicators := []*rpc_call.Prop{}
	for i := range s.CustomFieldDimensions {
		indicator := &s.CustomFieldDimensions[i]
		if indicator.Mode == rpc_call.AdvanceProp {
			continue
		}
		simpleIndicators = append(simpleIndicators, &s.CustomFieldDimensions[i])
	}
	if len(simpleIndicators) == 0 {
		return
	}
	indicatorString, err := json.Marshal(simpleIndicators)
	if err != nil {
		err = utils.NewUserErrorWrapf(err, "生成简单模式指标表达式失败")
	}
	variablesStr := ""
	if len(s.Variables) != 0 {
		variables, err := json.Marshal(s.Variables)
		if err != nil {
			return utils.NewUserErrorWrapf(err, "获取variables失败")
		}
		variablesStr = string(variables)
	}

	var rpc dap_indicator_modeling.IndicatorModeling = dap_indicator_modeling.NewIndicatorModelingServiceS()
	exprs, err := rpc.BatchGenSimpleIndicatorExpression(context.TODO(), &ads_common.BatchGenSimpleIndicatorExpressionRequest{
		Resource:       string(resource),
		AdsPropsString: string(indicatorString),
		VariablesStr:   variablesStr,
	})
	if err != nil {
		return
	}
	if len(exprs) != len(simpleIndicators) {
		err = utils.NewUserError("生成简单模式指标表达式失败，生成数量错误")
		return
	}
	for idx := range simpleIndicators {
		simpleIndicators[idx].PropRaw = exprs[idx]
	}

	return
}

func (s *AdsViewContent) FillupIndicatorsDepFields() (err error) {
	for i := range s.CustomFieldDimensions {
		// 解析指标中引用的字段
		indicator := &s.CustomFieldDimensions[i]
		var propFields []rpc_call.PropField
		propFields, err = s.analysisDepFields(indicator)
		if err != nil {
			return
		}
		indicator.PropFields = s.distinctPropFields(propFields)
	}

	return
}

func (s *AdsViewContent) distinctPropFields(fields []rpc_call.PropField) (distinctedFields []rpc_call.PropField) {
	fieldSet := map[string]struct{}{}
	for _, field := range fields {
		if _, ok := fieldSet[fmt.Sprintf("%s.%s", field.TableName, field.FieldName)]; !ok {
			fieldSet[fmt.Sprintf("%s.%s", field.TableName, field.FieldName)] = struct{}{}
			distinctedFields = append(distinctedFields, field)
		}
	}
	return
}

func (s *AdsViewContent) ValidViewContent() (err error) {
	// 校验过滤条件值变量是否存在
	if err = s.checkGlobalFilterVarExist(); err != nil {
		return
	}
	// 校验dependence合法性
	if err = s.validDependence(); err != nil {
		return
	}
	if err = s.validFieldName(); err != nil {
		return
	}
	// 校验不存在同名字段
	if err = s.checkFieldNoSameName(); err != nil {
		return
	}
	// 校验变量合法性
	if err = s.validVariables(); err != nil {
		return
	}
	// 校验指标合法性
	if err = s.validIndicator(); err != nil {
		return
	}

	return
}

func (s *AdsViewContent) validDependence() error {
	if s.Dependence == nil {
		return utils.NewUserError("汇总表取数来源不能为空")
	}
	// 校验node与link的数量node.count = link.count+1
	if len(s.Dependence.RelationalModel.Node) > 0 &&
		len(s.Dependence.RelationalModel.Node) != len(s.Dependence.RelationalModel.Link)+1 {
		return utils.NewUserError("汇总宽表的节点与连线数量不匹配")
	}
	//  校验link的joinFields不能为空，至少有一个join关系
	for _, l := range s.Dependence.RelationalModel.Link {
		if len(l.JoinFields) == 0 {
			return utils.NewUserError("汇总宽表的连接关系不能为空")
		}
	}
	switch s.Dependence.Category {
	case base.MultiDimCate:
		// 检查关系模型是否存在
		if s.Dependence.CodeId == "" || len(s.Dependence.RelationalModel.Node) < 1 {
			return errors.Errorf("依赖的关系模型编码(%s)或节点不能为空",
				s.Dependence.CodeId)
		}
	case base.DimModelCate, base.DwdModelCate, base.DwsModelCate:
		// 检查表名
		if s.Dependence.TableName == "" || s.Dependence.CodeId == "" {
			return errors.Errorf("依赖的明细表名(%s)或Code(%s)不能为空",
				s.Dependence.TableName, s.Dependence.CodeId)
		}
	default:
		return errors.Errorf("依赖模型的类型非法: %s", s.Dependence.Category)
	}
	return nil
}

func (s *AdsViewContent) GetDependence() (dependence *rpc_call.Dependence, err error) {
	if s.Dependence == nil {
		err = utils.NewUserError("汇总表取数来源为空")
		return
	}
	return s.Dependence, nil
}

func (s *AdsViewContent) validFieldName() (err error) {
	return
}

func (s *AdsViewContent) checkFieldNoSameName() (err error) {
	return
}

func (s *AdsViewContent) checkGlobalFilterVarExist() (err error) {
	// 检查过滤条件值变量是否存在
	varsMap := make(map[string]bool)
	for _, vars := range s.Variables {
		varsMap[vars.Name] = true
	}
	for _, filter := range s.GlobalFilter {
		for _, condition := range filter.Conditions {
			if condition.RightValue.Value != nil {
				val := ""
				if v, ok := condition.RightValue.Value.(string); ok {
					val = v
				} else {
					continue
				}
				re := regexp.MustCompile(`\{([^}]*)\}`)
				match := re.FindStringSubmatch(val)
				if len(match) != 2 {
					continue
				}
				varContent := match[1]
				if _, ok := varsMap[varContent]; !ok {
					return utils.NewUserError("过滤条件值变量[%s]不存在", varContent)
				}
			}
		}
	}
	return
}

func (s *AdsViewContent) validVariables() (err error) {
	return ValidateVariables(s.Variables)
}

func ValidateVariables(variables []rpc_call.Variable) (err error) {
	varNameSet := map[string]struct{}{}
	varIDSet := map[string]struct{}{}
	for _, varDef := range variables {
		// 校验变量名称和ID
		if varDef.Name == "" {
			return errors.Errorf("变量名称不能为空")
		}
		if varDef.ID == "" {
			return errors.Errorf("变量[%s]ID不能为空", varDef.Name)
		}
		if _, ok := varNameSet[varDef.Name]; ok {
			return errors.Errorf("变量名[%s]重复", varDef.Name)
		}
		varNameSet[varDef.Name] = struct{}{}
		if _, ok := varIDSet[varDef.ID]; ok {
			return errors.Errorf("变量[%s]ID重复", varDef.Name)
		}
		varIDSet[varDef.ID] = struct{}{}
		// 校验变量格式
		if varDef.VariableType == rpc_call.StringVar {
			if varDef.ScopeType != rpc_call.SingleValue && varDef.ScopeType != rpc_call.SequenceValue {
				return errors.Errorf("文本变量[%s]必须为单值/序列类型", varDef.Name)
			}
		}
	}
	return nil
}

func (s *AdsViewContent) validIndicator() (err error) {
	variableSet := map[string]struct{}{}
	for _, v := range s.Variables {
		variableSet[v.Name] = struct{}{}
	}
	// 校验指标正确性
	for _, indicator := range s.CustomFieldDimensions {
		if indicator.Mode == rpc_call.SimpleProp && indicator.Specifier == "" {
			return utils.NewUserError("指标<%s>计算逻辑不能为空", indicator.Alias)
		}
		if indicator.Mode == rpc_call.AdvanceProp {
			if indicator.PropRaw == "" {
				return utils.NewUserError("指标<%s>计算逻辑不能为空", indicator.Alias)
			}
			// 检查指标必须包含聚合函数
			if !indicator_utils.RegCheckHasAggFunc(indicator.PropRaw) {
				return utils.NewUserError("指标<%s>必须包含聚合函数", indicator.Alias)
			}
			// 检查指标中的变量必须有定义
			variables := indicator_utils.RegGetVariableExpr(indicator.PropRaw)
			for _, variable := range variables {
				if _, ok := variableSet[variable]; !ok {
					return utils.NewUserError("指标<%s>中的变量<%s>未定义", indicator.Alias, variable)
				}
			}
		}
	}
	return nil
}

func (s *AdsViewContent) analysisDepFields(prop *rpc_call.Prop) (fields []rpc_call.PropField, err error) {
	if prop.Mode == rpc_call.SimpleProp {
		for _, condition := range prop.Conditions {
			fields = append(fields, s.getNewConditionFields(condition)...)
		}
		fields = append(fields, rpc_call.PropField{TableName: prop.ObjName, FieldName: prop.PropName})
		return fields, nil
	}
	return indicator_utils.RegGetTableAndFieldProp(prop.PropRaw), nil
}

func (s *AdsViewContent) getNewConditionFields(condition global.Condition) []rpc_call.PropField {
	var fields []rpc_call.PropField
	if condition.LeftField.Name != "" {
		fields = append(fields, rpc_call.PropField{
			TableName: condition.LeftField.Table,
			FieldName: condition.LeftField.Name,
		})
	}
	if condition.RightValue.Name != "" {
		fields = append(fields, rpc_call.PropField{
			TableName: condition.RightValue.Table,
			FieldName: condition.RightValue.Name,
		})
	}
	return fields
}

func (s *AdsViewContent) getConditionFields(condition rpc_call.Condition) []rpc_call.PropField {
	fields := []rpc_call.PropField{}
	if condition.Left != nil && condition.Left.PropName != "" {
		fields = append(fields, rpc_call.PropField{
			TableName: condition.Left.ObjName,
			FieldName: condition.Left.PropName,
		})
	}
	if condition.Right != nil && condition.Right.PropName != "" {
		fields = append(fields, rpc_call.PropField{
			TableName: condition.Left.ObjName,
			FieldName: condition.Left.PropName,
		})
	}
	for _, subCondition := range condition.Conditions {
		fields = append(fields, s.getConditionFields(subCondition)...)
	}
	return fields
}

// 物理模型依赖表
type ModelDependence struct {
	Id                string    `gorm:"column:id;type:char(36);comment:主键id;primary_key" json:"id"`
	Code              string    `gorm:"column:code;type:char(36);comment: 汇总表code;NOT NULL" json:"code"`
	PhysicalModelCode string    `gorm:"column:physical_model_code;type:char(36);comment:物理模型code;NOT NULL" json:"physical_model_code"`
	DepModelCode      string    `gorm:"column:dep_model_code;type:char(36);comment:依赖的物理模型code;NOT NULL" json:"dep_model_code"`
	Environment       string    `gorm:"column:environment;type:enum('开发','生产','历史');default:开发;comment:环境;NOT NULL" json:"environment"`
	CreatedOn         time.Time `gorm:"column:created_on;type:datetime;default:CURRENT_TIMESTAMP;comment:记录创建时间;NOT NULL" json:"created_on"`
	ModifiedOn        time.Time `gorm:"column:modified_on;type:datetime;comment:记录修改时间" json:"modified_on"`
	CreatedBy         string    `gorm:"column:created_by;type:char(36);comment:记录创建者Id" json:"created_by"`
	ModifiedBy        string    `gorm:"column:modified_by;type:char(36);comment:记录修改者Id" json:"modified_by"`
}
