package base

import (
	"context"
	"encoding/json"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/ads_common"

	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"
)

// 视图模式
type ViewLogic struct {
	logicBase
}

func (ml *ViewLogic) GenExecuteSQL(ctx context.Context, resource *cBase.ProjectResource, devMode string) (sql string, err error) {
	sql, err = ml.genAdsExecuteSQL(ctx, resource, devMode)
	return
}

// 生成派生指标的查询SQL
func (ml *ViewLogic) genAdsExecuteSQL(ctx context.Context, resource *cBase.ProjectResource, devMode string) (string, error) {
	var engine ads_common.QueryEngine
	switch resource.ResourceType {
	case cBase.StarRocksResourceType:
		engine = ads_common.QueryEngine_StarRocks
	case cBase.StarRocksSaaSResourceType:
		engine = ads_common.QueryEngine_StarRocksSaaS
	case cBase.RDSSaaSResourceType:
		engine = ads_common.QueryEngine_RDSSaaS
	case cBase.RDSResourceType:
		engine = ads_common.QueryEngine_RDS
	case cBase.DamengSaaSResourceType:
		engine = ads_common.QueryEngine_DamengSaas
	default:
		return "", utils.NewUserError("暂不支持的引擎类型<%s>", resource.ResourceType)
	}

	viewContentString, err := json.Marshal(ml.metadata)
	if err != nil {
		return "", utils.NewUserErrorWrapf(err, "获取ViewContent失败")
	}

	return ml.indicatorModeling.GenAdsRunSql(ctx, &ads_common.GenAdsRunSqlRequest{
		Engine:               engine,
		ProjectCode:          ml.project,
		AdsViewContentString: string(viewContentString),
		DevMode:              devMode,
	})
}
