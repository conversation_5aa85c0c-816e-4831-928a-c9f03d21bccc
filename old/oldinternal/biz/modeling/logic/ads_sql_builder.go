package logic

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"

	dap_common "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/sqlce"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/context_helper"

	"github.com/pkg/errors"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/dmp/gopkg/str"
)

func (s *ModelLogic) GenRunSql(ctx context.Context, project string, data proto.AsyncRunSQL) (sql string, executeSQL string, err error) {
	// 获取资源
	data.Resource = entities.RDSResourceType
	resource, err := s.getExecuteResource(ctx, project)
	if err != nil {
		return
	}

	// 校验ViewContent内容
	if err = data.ViewContent.ValidViewContent(); err != nil {
		return
	}

	// 后端补充ViewContent必要信息
	if err = data.ViewContent.FillUpExtraSaveInfo(data.Resource); err != nil {
		return
	}

	//todo 这里应该要判断指标到底用moql 还是用当前查询引擎。还需可能使用查询引擎来决定用那个片段取拼接sql？
	//genSqlType, err := s.getRunSqlDialectType(ctx, project, data)
	//if err != nil {
	//	return
	//}

	//需要修改根据采取的引擎，判断用何种方言的指标片段去拼接sql
	for i, f := range data.ViewContent.CustomFieldDimensions {
		if f.Mode != rpc_call.AdvanceProp {
			continue
		}
		if resource.ResourceType == cBase.DamengSaaSResourceType {
			data.ViewContent.CustomFieldDimensions[i].PropRaw = data.ViewContent.CustomFieldDimensions[i].DialectSettingContent.DmSql
		} else if resource.ResourceType == cBase.RDSSaaSResourceType || resource.ResourceType == cBase.RDSResourceType {
			data.ViewContent.CustomFieldDimensions[i].PropRaw = data.ViewContent.CustomFieldDimensions[i].DialectSettingContent.MySql
		}
	}

	// 生成执行sql
	logic, err := base.NewLogic(project, data.ViewContent)
	if err != nil {
		return
	}

	sql, err = logic.GenExecuteSQL(ctx, &cBase.ProjectResource{ResourceType: resource.ResourceType}, data.DevMode)
	if err != nil {
		return
	}
	executeSQL = sql
	if !data.IsNotLimit {
		executeSQL = fmt.Sprintf("%s limit 20", sql)
	}

	//如果是moql(mysql的生成方言) 且当前查询查询引擎是达梦，那么就需要将sql翻译成达梦
	//if genSqlType == cBase.RDSResourceType && resource.ResourceType == cBase.DamengSaaSResourceType {
	//	result, err1 := s.moqlService.Translate(ctx, string(moql.DmDialect), executeSQL)
	//	if err1 != nil {
	//		err = err1
	//		return
	//	}
	//	if result.Success {
	//		executeSQL = result.Sql
	//	} else {
	//		err = errors.New(strings.Join(result.Errors, ";"))
	//		return
	//	}
	//}
	return
}

func (s *ModelLogic) getRunSqlDialectType(ctx context.Context, project string, data proto.AsyncRunSQL) (resourceType cBase.ProjectResourceType, err error) {
	resourceType = cBase.RDSResourceType
	//需要根据查询引擎
	resource, err := s.dapCommon.GetStorageResources(ctx, project)
	if err != nil {
		return "", err
	}
	for _, indicatorField := range data.ViewContent.CustomFieldDimensions {
		//单值sql跳过
		if indicatorField.DialectSettingContentStr == "" {
			continue
		}
		if indicatorField.DialectSettingContent.DialectSetting == global.MoSqlDialectSetting {
			return
		} else if indicatorField.DialectSettingContent.DialectSetting == global.MultiSqlDialectSetting {
			if resource.ResourceType == string(cBase.RDSResourceType) || resource.ResourceType == string(cBase.RDSSaaSResourceType) {
				return
			} else if resource.ResourceType == string(cBase.DamengSaaSResourceType) {
				resourceType = cBase.DamengSaaSResourceType
			} else {
				return
			}
		}
	}
	return
}

// AdsAsyncRunSQL 异步执行sql
func (s *ModelLogic) AdsAsyncRunSQL(ctx context.Context, project string, data proto.AsyncRunSQL) (resp sqlce.AsyncRunSQLResponse, err error) {
	var executeSQL string
	_, executeSQL, err = s.GenRunSql(ctx, project, data)
	if err != nil {
		return
	}
	// 获取资源
	resource, err := s.getExecuteResource(ctx, project)
	if err != nil {
		return
	}
	data.Resource = entities.ProjectResourceType(resource.ResourceType)
	// 执行sql
	executionTime := global.AppConfig.GetSQLMaxExecutionTime()
	reqOpts := []sqlce.BaseRequestOption{
		sqlce.WithBaseRequestMaxExecutionTime(executionTime)}
	tenantDB := sqlce.DatasourceMeta{}
	if data.Resource == entities.StarRocksSaaSResourceType {
		if tenantDB, err = s.getStarRocksTenactDB(ctx, project, resource, data.TenantCode); err != nil {
			return
		}
	}
	if data.Resource == entities.RDSSaaSResourceType {
		if tenantDB, err = s.getRDSSaaSTenantDB(ctx, project, resource, data.TenantCode); err != nil {
			return
		}
	}

	if data.Resource == entities.DamengSaaSResourceType {
		if tenantDB, err = s.getDamengSaaSTenantDB(ctx, project, resource, data.TenantCode); err != nil {
			return
		}
	}
	runSqlReq := sqlce.AsyncRunSQLRequest{
		BaseRequest: sqlce.NewBaseRequest(resource.ResourceID, reqOpts...),
		SQL:         executeSQL,
		Project:     project,
		Datasource:  tenantDB,
	}
	resp, err = s.sqlCeCaller.AsyncRunSqlAction(ctx, runSqlReq)
	if err != nil {
		return
	}

	resp.SQL = executeSQL
	resp.ExecuteSQL = executeSQL
	return
}

// GetAdsAsyncRunSQLResult 获取异步执行sql的执行结果
func (s *ModelLogic) GetAdsAsyncRunSQLResult(ctx context.Context, project string, data proto.AsyncRunSQLResult) (rsp sqlce.GetAsyncRunSQLResultResponse, err error) {
	// 获取资源
	resource, err := s.getExecuteResource(ctx, project)
	if err != nil {
		return
	}

	// 获取SQL执行结果
	executionTime := global.AppConfig.GetSQLMaxExecutionTime()
	reqOpts := []sqlce.BaseRequestOption{sqlce.WithBaseRequestMaxExecutionTime(executionTime)}
	rsp, err = s.sqlCeCaller.GetAsyncRunSQLResultAction(ctx, sqlce.GetAsyncRunSQLResultRequest{
		BaseRequest: sqlce.NewBaseRequest(resource.ResourceID, reqOpts...),
		InstanceId:  data.InstanceID,
		Project:     project,
	})
	if err != nil {
		return
	}

	return
}

// 同步执行sql获取结果
func (s *ModelLogic) PreviewIndicatorModel(ctx context.Context, project, tenantCode string, sql string) (rsp proto.PreviewIndicatorModelResponse, err error) {
	// 获取资源
	resource, err := s.getExecuteResource(ctx, project)
	if err != nil {
		return
	}

	// 调用sqlce进行取数
	data, columns, innerError := s.queryData(ctx, project, resource, tenantCode, sql)
	if innerError != nil {
		err = innerError
		return
	}
	header := []string{}
	for _, column := range columns {
		header = append(header, column.ColumnName)
	}
	rsp.Header = header
	rsp.Data = s.convertMapDataToListData(data, header)
	rsp.Sql = sql
	return
}

func (s *ModelLogic) queryData(ctx context.Context, project string, resource *cBase.ProjectResource, tenantCode, sql string) ([]map[string]interface{}, []sqlce.Column, error) {
	var err error
	tenantDB := sqlce.DatasourceMeta{}
	if entities.ProjectResourceType(resource.ResourceType) == entities.StarRocksSaaSResourceType {
		if tenantDB, err = s.getStarRocksTenactDB(ctx, project, resource, tenantCode); err != nil {
			return nil, nil, err
		}
	}
	if entities.ProjectResourceType(resource.ResourceType) == entities.RDSSaaSResourceType {
		if tenantDB, err = s.getRDSSaaSTenantDB(ctx, project, resource, tenantCode); err != nil {
			return nil, nil, err
		}
	}

	if entities.ProjectResourceType(resource.ResourceType) == entities.DamengSaaSResourceType {
		if tenantDB, err = s.getDamengSaaSTenantDB(ctx, project, resource, tenantCode); err != nil {
			return nil, nil, err
		}
	}

	// 执行取数
	rsp, err := s.sqlCeCaller.ExecuteSQLResultAction(ctx, sqlce.ExecuteSQLResultRequest{
		BaseRequest: sqlce.NewBaseRequest(resource.ResourceID,
			sqlce.WithBaseRequestMaxExecutionTime(context_helper.GetSQLMaxExecutionTime(ctx))),
		Datasource: tenantDB,
		SQL:        sql,
		Select:     true,
	})
	if err != nil {
		return nil, nil, err
	}

	if rsp.Result == nil {
		return make([]map[string]interface{}, 0), rsp.Columns, nil
	}
	return rsp.Result, rsp.Columns, nil
}

func (s *ModelLogic) convertMapDataToListData(data []map[string]interface{}, header []string) (newData [][]interface{}) {
	newData = [][]interface{}{}
	for _, row := range data {
		newRow := []interface{}{}
		for _, headerItem := range header {
			if cell, ok := row[headerItem]; ok {
				newRow = append(newRow, cell)
			} else {
				newRow = append(newRow, nil)
			}
		}
		newData = append(newData, newRow)
	}
	return
}

// 获取当前执行资源
func (s *ModelLogic) getExecuteResource(ctx context.Context, project string) (resource *cBase.ProjectResource, err error) {
	var resourceInfo *dap_common.ResourceInfo
	resourceInfo, err = s.dapCommon.GetStorageResources(ctx, project)
	if err != nil {
		return
	}
	resource = &cBase.ProjectResource{
		ResourceID:   resourceInfo.Id,
		ResourceType: cBase.ProjectResourceType(resourceInfo.ResourceType),
	}
	resource.Content, err = json.Marshal(resourceInfo.Content)
	if err != nil {
		return
	}
	return
}

func (s *ModelLogic) getStarRocksTenactDB(ctx context.Context, project string, resource *cBase.ProjectResource, code string) (datasourceMeta sqlce.DatasourceMeta, err error) {
	if err = resource.LoadConnectionConfig(); err != nil {
		return
	}
	config := resource.ConnConfig.(*cBase.StarRocksSaaSConfig)
	var pwd string
	pwd, err = str.DMPAesDecrypt(config.Password, global.AppConfig.Security.DmpCryptKey)
	if err != nil {
		s.logger.Error("getStarRocksTenactDB DMPAesDecrypt fail: ", err)
		return
	}
	// 默认使用配置好的模版库,如果不是saas就是单库
	datasourceMeta = sqlce.DatasourceMeta{
		Host:     config.Host,
		Port:     config.Port,
		Database: config.Database,
		User:     config.User,
		Password: sqlce.CryptStr(pwd),
	}
	// 如果指定了租户库，则默认是saas环境，执行相应的租户
	if code != "" && code != config.Database {
		tenantInfo, err1 := s.getTenantInfoByCode(ctx, project, code)
		if err1 != nil {
			err = err1
			return
		}
		pwd := sqlce.CryptStr(tenantInfo.QueryPassword)
		rawPwd, err1 := pwd.Decrypt(global.AppConfig.Security.DmpCryptKey)
		if err1 != nil {
			err = err1
			return
		}
		datasourceMeta.Database = tenantInfo.DataBase
		datasourceMeta.Port = tenantInfo.Port
		datasourceMeta.Password = rawPwd
		datasourceMeta.User = tenantInfo.QueryUser
		datasourceMeta.Host = tenantInfo.Host
	}
	return
}

func (s *ModelLogic) getRDSSaaSTenantDB(ctx context.Context, project string, resource *cBase.ProjectResource, code string) (
	datasourceMeta sqlce.DatasourceMeta, err error) {
	if err = resource.LoadConnectionConfig(); err != nil {
		return
	}
	config := resource.ConnConfig.(*cBase.MysqlDataSourceConn)
	var pwd string
	pwd, err = str.DMPAesDecrypt(config.Password, global.AppConfig.Security.DmpCryptKey)
	if err != nil {
		s.logger.Error("getRDSSaaSTenantDB DMPAesDecrypt fail: ", err)
		return
	}
	// 默认使用配置好的模版库,如果不是saas就是单库
	datasourceMeta = sqlce.DatasourceMeta{
		Host:     config.Host,
		Port:     config.Port,
		Database: config.Database,
		User:     config.User,
		Password: sqlce.CryptStr(pwd),
	}
	// 如果指定了租户库且不等于模板库，则默认是saas环境，执行相应的租户
	if code != "" && code != config.Database {
		tenantInfo, err1 := s.getTenantInfoByCode(ctx, project, code)
		if err1 != nil {
			err = err1
			return
		}
		pwd := sqlce.CryptStr(tenantInfo.QueryPassword)
		rawPwd, err1 := pwd.Decrypt(global.AppConfig.Security.DmpCryptKey)
		if err1 != nil {
			err = err1
			return
		}
		datasourceMeta.Database = tenantInfo.DataBase
		datasourceMeta.Port = tenantInfo.Port
		datasourceMeta.Password = rawPwd
		datasourceMeta.User = tenantInfo.QueryUser
		datasourceMeta.Host = tenantInfo.Host
	}
	return
}

func (s *ModelLogic) getDamengSaaSTenantDB(ctx context.Context, project string, resource *cBase.ProjectResource, code string) (
	datasourceMeta sqlce.DatasourceMeta, err error) {
	if err = resource.LoadConnectionConfig(); err != nil {
		return
	}
	config := resource.ConnConfig.(*cBase.DamengDataSourceConn)
	var pwd string
	pwd, err = str.DMPAesDecrypt(config.Password, global.AppConfig.Security.DmpCryptKey)
	if err != nil {
		s.logger.Error("getRDSSaaSTenantDB DMPAesDecrypt fail: ", err)
		return
	}
	// 默认使用配置好的模版库,如果不是saas就是单库
	datasourceMeta = sqlce.DatasourceMeta{
		Host:     config.Host,
		Port:     config.Port,
		Database: config.Database,
		User:     config.User,
		Password: sqlce.CryptStr(pwd),
	}
	// 如果指定了租户库且不等于模板库，则默认是saas环境，执行相应的租户
	if code != "" && code != config.Database {
		tenantInfo, err1 := s.getTenantInfoByCode(ctx, project, code)
		if err1 != nil {
			err = err1
			return
		}
		pwd := sqlce.CryptStr(tenantInfo.QueryPassword)
		rawPwd, err1 := pwd.Decrypt(global.AppConfig.Security.DmpCryptKey)
		if err1 != nil {
			err = err1
			return
		}
		datasourceMeta.Database = tenantInfo.DataBase
		datasourceMeta.Port = tenantInfo.Port
		datasourceMeta.Password = rawPwd
		datasourceMeta.User = tenantInfo.QueryUser
		datasourceMeta.Host = tenantInfo.Host
	}
	return
}

func (s *ModelLogic) getTenantInfoByCode(ctx context.Context, project, tenantCode string) (*dap_common.TenantInfo, error) {

	tenantInfos, err := s.dapCommon.ExecutableList(ctx, project, false)
	if err != nil {
		return nil, err
	}
	var requestTenantInfo *dap_common.TenantInfo
	for _, t := range tenantInfos {
		if t.Code == tenantCode {
			requestTenantInfo = t
		}
	}

	if requestTenantInfo == nil {
		err = errors.New(fmt.Sprintf("找不到租户【%s】的连接实例信息", tenantCode))
	}
	return requestTenantInfo, nil
}
