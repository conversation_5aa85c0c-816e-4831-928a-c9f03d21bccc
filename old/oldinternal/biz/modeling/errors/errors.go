package errors

import (
	"fmt"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"
)

func NewModelNotExistErr(modelID string) error {
	return utils.NewUserError(fmt.Sprintf("模型(ID：%s)不存在", modelID))
}

func NewModelCodeNotExistErr(code string) error {
	return utils.NewUserError(fmt.Sprintf("模型(Code：%s)不存在", code))
}

func NewModelCodeNotReleaseErr(code string) error {
	return utils.NewUserError(fmt.Sprintf("模型(Code：%s)尚未发布，请先发布。", code))
}

func NewModelNotDefinitionErr(modelID string) error {
	return utils.NewUserError(fmt.Sprintf("模型(ID：%s)尚未物理化", modelID))
}

func NewModelDeleteForbidErr(modelID string) error {
	return utils.NewUserError(fmt.Sprintf("模型(ID: %s)已物理化，不可删除", modelID))
}

func NewModelFieldNotExistErr(fieldID string) error {
	return utils.NewUserError(fmt.Sprintf("模型字段(ID：%s)不存在", fieldID))
}

func NewModelFieldNameExistErr(fieldName string) error {
	return utils.NewUserError(fmt.Sprintf("同一模型字段中英文名不可重复:%s", fieldName))
}

func NewModelFieldNameNotExistErr(fieldName string) error {
	return utils.NewUserError(fmt.Sprintf("已发布模型字段不可减少:%s", fieldName))
}

func NewModelModifyFieldNameExistErr(fieldName string) error {
	return utils.NewUserError(fmt.Sprintf("已物理化不允许修改英文名:%s", fieldName))
}

func NewModelFieldForbidEditErr(fieldID string) error {
	return utils.NewUserError(fmt.Sprintf("模型字段(ID：%s)已物理化，不可修改英文名、排序、以及删除", fieldID))
}

func NewModelDefinitionForbidErr(modelID string) error {
	return utils.NewUserError(fmt.Sprintf("模型(ID: %s)已物理化，不可重复物理化", modelID))
}

func NewModelDefinitionNotExistsErr(modelPhysicalID string) error {
	return utils.NewUserError("物理模型(ID: %s)不存在", modelPhysicalID)
}

func NewModelDefinitionCodeNotExistsErr(modelPhysicalID string) error {
	return utils.NewUserError("物理模型(Code: %s)不存在", modelPhysicalID)
}

func NewModelDefinitionNameExistErr(tableName string) error {
	return utils.NewUserError(fmt.Sprintf("物理表英文名%s已存在，", tableName))
}

func NewModelDefinitionRepeatFieldErr(fieldName string) error {
	return utils.NewUserError(fmt.Sprintf("存在重复字段名：%s", fieldName))
}

func NewModelDefinitionFieldNotExistErr(fieldName string) error {
	return utils.NewUserError(fmt.Sprintf("字段表达式中存在非法字段名：%s", fieldName))
}

func NewTableNameTooLongErr(tableName string) error {
	return utils.NewUserError(fmt.Sprintf("%s 只能用英文的a-z、A-Z、数字和下划线（_），且以字母开头，名称的长度不超过128字节", tableName))
}

func NewModelFieldTypeNotExistErr(fieldName string) error {
	return utils.NewUserError(fmt.Sprintf("字段(名称: %s)类型不能为空", fieldName))
}

func NewIndicatorNotExistsErr(indicatorID string) error {
	return utils.NewUserError("指标(ID: %s)不存在", indicatorID)
}

func NewIndicatorCodeNotExistsErr(indicatorCode string) error {
	return utils.NewUserError("模型(Code: %s)不存在", indicatorCode)
}

func NewReleaseIndicatorCannotModifyName() error {
	return utils.NewUserError("已经发布的模型不能修改表名")
}

func NewIndicatorNameNotExistsErr() error {
	return utils.NewUserError("指标名称不能为空")
}

func NewIndicatorFieldReduceErr() error {
	return utils.NewUserError("待更新字段数量不能少于已发布指标字段数量")
}

func NewIndicatorCodeNotReleaseErr(code string) error {
	return utils.NewUserError(fmt.Sprintf("指标(Code：%s)尚未发布，请先发布。", code))
}

func NewIndicatorIDNotReleaseErr(indicatorID string) error {
	return utils.NewUserError(fmt.Sprintf("指标(ID：%s)尚未发布，请先发布。", indicatorID))
}

func NewIndicatorCodeReleaseErr(name string) error {
	return utils.NewUserError(fmt.Sprintf("指标(名称：%s)已发布，无法直接删除", name))
}

func NewIndicatorReleaseNoContentErr(indicatorID string) error {
	return utils.NewUserError(fmt.Sprintf("指标(ID: %s)配置数据不能为空", indicatorID))
}

func NewIndicatorNameExistsErr(name string) error {
	return utils.NewUserError("模型名称(Name: %s)已存在", name)
}

func NewCodeIsEmptyErr() error {
	return utils.NewUserError("更新【汇总模型】或【派生指标】时，code必填")
}

func NewCategoryNotExistErr(category string) error {
	return utils.NewUserError("模型类型(category: %s)不支持", category)
}

func NewModelSourceErr(source int32) error {
	return utils.NewUserError("模型类型(source: %d)不存在", source)
}

func NewPublishErr(pubish int32) error {
	return utils.NewUserError("不支持可发布状态(pubish:%d)不存在", pubish)
}

func NewFieldReleaseCheckErr(name, modify string) error {
	return utils.NewUserError("字段:%s %s修改了,请先下线模型再修改", name, modify)
}

func NewPhysicalModelNoDependModel(name string) error {
	return utils.NewUserError("指标:%s依赖为空", name)
}

func NewReleaseRelationModel(name string) error {
	return utils.NewUserError("请先发布:%s", name)
}

func NewFieldNullErr(name, nullField string) error {
	return utils.NewUserError("字段%s:%s没有填写", name, nullField)
}

func NewMsgErr(msg string) error {
	return utils.NewUserError(msg)
}

func NewFieldParentFiledErr(name, msg string) error {
	return utils.NewUserError("字段%s:%s", name, msg)
}

func NewPhysicalModelCodeHasBeenDepErr(code string) error {
	return utils.NewUserError("当前模型已被汇总表(%s)依赖", code)
}
