package base

import (
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type ModelCategory string

const (
	// DIM: DIMENSION 维度表
	DimModelCate ModelCategory = "dim"
	// DWD: 明细数据层(DWD，Data Warehouse Detail)
	DwdModelCate ModelCategory = "dwd"
	// DWS: 公共汇总层(DWS, Data Warehouse Summary)
	DwsModelCate ModelCategory = "dws"
	// ADS: 应用数据层(ADS，Application Data Store)
	AdsModelCate ModelCategory = "ads"
	// AdsViewModelCate 派生视图
	AdsViewModelCate ModelCategory = "ads_view"
	// MultiDimCate 多维模型
	MultiDimCate ModelCategory = "multi_dim"
)

type ModelRelationCategory string

const (
	PhysicalModel ModelRelationCategory = "physical"
	MultiDimModel ModelRelationCategory = "multi_dim"
)

type SelfReportStatus string

const (
	Enable SelfReportStatus = "1"
)

type AdsCategory string

const (
	// 默认，为派生指标模型
	DefaultAdsCate AdsCategory = "model"
	// IV(IV，Indicator View)，为派生指标视图
	IndicatorViewAdsCate AdsCategory = "view"
)

type FuncCategory string

const (
	AggregateFunc FuncCategory = "聚合函数"
	StringFunc    FuncCategory = "字符串函数"
	DatetimeFunc  FuncCategory = "日期函数"
	NumericalFunc FuncCategory = "数值函数"
	LogicFunc     FuncCategory = "逻辑函数"
)

type Model struct {
	ID                   string        `json:"id"`
	SubjectID            string        `json:"subject_id"`
	Name                 string        `json:"name"`
	Description          string        `json:"description"`
	Category             ModelCategory `json:"category"`
	DispatchCategory     db.NullInt32  `json:"dispatch_category"`
	CreatedOn            db.NullTime   `json:"created_on"`
	ModifiedOn           db.NullTime   `json:"modified_on"`
	CreatedBy            string        `json:"created_by"`
	ModifiedBy           string        `json:"modified_by"`
	Fields               []*ModelField
	DefinitionTableName  string        `json:"table_name"`
	DefinitionCode       string        `json:"physical_model_code"`
	ReleaseDefinitionID  string        `json:"release_physical_model_id"`
	Status               string        `json:"status"`
	Publishable          int64         `json:"publishable"`
	Environment          string        `json:"environment"`
	IsPhysicalized       string        `json:"is_physicalized"`
	AggData              AggData       `json:"agg_data"`
	RelationModelIDs     []string      `json:"relation_model_ids"`
	IsRelease            bool          `json:"is_release"`
	SyncOn               int           `json:"sync_on"`
	Source               int           `json:"source"`
	Resource             string        `json:"resource"`
	ModifyStatus         ModifyStatus  `json:"modify_status"`
	ReleaseStatus        ReleaseStatus `json:"release_status"`
	IsBindExpandTable    int           `json:"is_bind_expand_table"`
	ExpandTableName      string        `json:"expand_table_name"`
	SourceProjectLevel   int           `json:"source_project_level"`
	IndividualType       string        `json:"individual_type"`
	IsDisableExpandTable bool          `json:"is_disable_expand_table"`
	EditedStandardFlag   int           `json:"edited_standard_flag"`
	IsDel                int           `json:"is_del"`
	DevMode              string        `json:"dev_mode"`
	ApplyStatus          int           `json:"apply_status"`
}

type AggData struct {
	PhysicalModelId string  `json:"release_physical_model_id" db:"physical_model_id"`
	FieldAmount     int64   `json:"field_amount" db:"field_amount"`
	DataSize        float64 `json:"data_size" db:"data_size"`
	SizeUint        string  `json:"size_unit" db:"size_unit"`
}

type ModelRelation struct {
	ID                 string      `json:"id" db:"id"`
	Category           string      `json:"category" db:"category"`
	PhysicalModelId    string      `json:"physical_model_id" db:"physical_model_id"`
	DepPhysicalModelId string      `json:"dep_physical_model_id" db:"dep_physical_model_id"`
	CreatedOn          db.NullTime `json:"created_on" db:"created_on"`
	ModifiedOn         db.NullTime `json:"modified_on" db:"modified_on"`
	CreatedBy          string      `json:"created_by" db:"created_by"`
	ModifiedBy         string      `json:"modified_by" db:"modified_by"`
}

type BuiltinFunction struct {
	Name    string `json:"name"`
	Usage   string `json:"usage"`
	Declare string `json:"declare"`
	Sample  string `json:"sample"`
}

var AggregateFuncs = []BuiltinFunction{
	{
		Name:    "AVG",
		Usage:   "AVG(字段)",
		Declare: "返回字段的平均值。",
		Sample:  "AVG([订单金额]),返回订单金额的平均值。",
	},
	{
		Name:    "COUNT",
		Usage:   "COUNT(字段)",
		Declare: "返回字段的数据条目数。",
		Sample:  "COUNT([项目名称]),返回项目名称的条目数。",
	},
	{
		Name:    "SUM",
		Usage:   "SUM(字段)",
		Declare: "返回字段的总和。",
		Sample:  "SUM([订单金额]),返回订单金额的总和。",
	},
	{
		Name:    "MAX",
		Usage:   "MAX(字段)",
		Declare: "返回字段中的最大值。",
		Sample:  "MAX([订单金额]),返回订单金额的最大值。",
	},
	{
		Name:    "MIN",
		Usage:   "MIN(字段)",
		Declare: "返回字段中的最小值。",
		Sample:  "MIN([订单金额]),返回订单金额的最小值。",
	},
}

var StringFuncs = []BuiltinFunction{
	{
		Name:    "SUBSTR",
		Usage:   "SUBSTR(字符串,起始位置,[长度])",
		Declare: "从开始位置(字符串从1开始计数)截取指定长度的字符串返回，长度为可选参数，不给则默认截取到字符串尾部。",
		Sample:  "SUBSTR('abc123', 1, 3) = 'abc'， SUBSTR('abc123', 1) = 'abc123'",
	},
	{
		Name:    "REPLACE",
		Usage:   "REPLACE(字符串,子串,替换串)",
		Declare: "返回一个字符串，在该字符串中，子字符串的每次出现都会替换为替换字符串。字串和替换串必须为常量字符串，如果未找到子字符串，则字符串保持不变。",
		Sample:  "REPLACE('Calculation', 'ion', 'ed') = 'Calculated'",
	},
}

var DatetimeFuncs = []BuiltinFunction{
	{
		Name:    "DATE_ADD",
		Usage:   "DAY_ADD(日期字段, Delta)",
		Declare: "按照Delta幅度大小增减日期字段的天数。",
		Sample:  "DATE_ADD('2020-01-01', 1)，返回'2020-01-02'",
	},
}

var NumericalFuncs = []BuiltinFunction{
	{
		Name:    "ABS",
		Usage:   "ABS(字段)",
		Declare: "返回字段的绝对值",
		Sample:  "ABS([订单金额]),返回订单金额的绝对值",
	},
	{
		Name:    "ROUND",
		Usage:   "ROUND(字段,[保留小数位])",
		Declare: "返回字段四舍五入后的值。",
		Sample:  "ROUND([订单金额])，返回订单金额四舍五入后的值。",
	},
}

var LogicFuncs = []BuiltinFunction{
	{
		Name:    "CASE WHEN",
		Usage:   "CASE 表达式 WHEN 值1 THEN 返回值1 ... [ELSE 默认返回值] END",
		Declare: "当表达式为值1时返回返回值1 ... 否则返回默认返回值。可以不指定默认返回值 （但这时候应该确保枚举到了所有情况）。",
		Sample:  "CASE WHEN [订单金额] < 500 THEN '小订单' WHEN [订单金额]>= 500 AND [订单金额] < 5000 THEN '大订单' ELSE '超大订单' END",
	},
}

const TableSizeUnit = "MB"

type ModelLabel string

const (
	DefaultLabel ModelLabel = ""        // 默认无标签
	DwsLabel     ModelLabel = "dws"     // 标签为汇总表
	ProcessLabel ModelLabel = "process" // 标签为加工过程表
)

type ModelBriefStatus string

const (
	Unreleased     ModelBriefStatus = "unreleased" // 未发布
	Released       ModelBriefStatus = "released"   // 已发布
	Editing        ModelBriefStatus = "editing"    // 修订中
	OfflineAlready ModelBriefStatus = "offline"    // 已下线
)

type ModifyStatus int

const (
	InitModifyStatus     ModifyStatus = 0 // 初始态
	ModifiedModifyStatus ModifyStatus = 1 // 修改态
)

type ReleaseStatus string

const (
	Unrelease    ReleaseStatus = "unrelease"     // 未发布
	FirstRelease ReleaseStatus = "first_release" // 新增(只有发布态数据)
	Release      ReleaseStatus = "release"       // 更新(有发布也有历史)
	Offline      ReleaseStatus = "offline"       // 下线(只有历史态)
)

type ApplyStatus int

const (
	InitApplyStatus     ApplyStatus = 0 //未上架
	OnShelfApplyStatus  ApplyStatus = 1 //已上架
	OffShelfApplyStatus ApplyStatus = 2 //已下架
)
