package base

import (
	"time"
)

type ModelField struct {
	ID           string    `json:"id"`
	NameCn       string    `json:"name_cn"`
	Name         string    `json:"name"`
	Rank         int64     `json:"rank"`
	Description  string    `json:"description"`
	ValueComment string    `json:"value_comment"`
	AssetType    string    `json:"asset_type"`
	DimType      DimType   `json:"dim_type"`
	CreatedOn    time.Time `json:"created_on"`
	ModifiedOn   time.Time `json:"modified_on"`
	CreatedBy    string    `json:"created_by"`
	ModifiedBy   string    `json:"modified_by"`

	ModelID                string `json:"business_model_id" db:"business_model_id"`
	ModelDefinitionFieldID string `json:"physical_model_field_id" db:"physical_model_field_id"`
	FieldCategory          string `json:"field_category,omitempty"`
}

type DimType string

const (
	// 维度
	DimensionDimType DimType = "dimension"
	// 描述
	DescriptionDimType DimType = "description"
	// 默认
	DefaultDimType DimType = ""
	// 指标
	IndicatorDimType DimType = "indicator"
)
