package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"

	commonUtils "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"

	"github.com/defval/inject/v2"
	"github.com/jmoiron/sqlx"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	baseStore "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/base/store"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewModelStore, inject.As(new(ModelRepo))))
}

type ModelRepo interface {
	ListModels(ctx context.Context, project string, data proto.ListModels) ([]*base.Model, error)
	ListIndicatorModels(ctx context.Context, project string, data proto.ListModels) ([]*base.Model, error)
	HasModelRelease(ctx context.Context, project string, codes []string) (map[string]struct{}, error)
	HasIndicatorModelRelease(ctx context.Context, project string, codes []string) (map[string]struct{}, error)
	ListPhysicalModelBriefs(ctx context.Context, project string, req proto.ListModelDefinitionReq) ([]*proto.PhysicalModelBrief, error)
}

var _ ModelRepo = (*ModelStore)(nil)

type ModelEntity struct {
	ID                 string             `json:"id" db:"id"`
	SubjectID          string             `json:"subject_id" db:"subject_id"`
	PhysicalModelCode  string             `json:"physical_model_code" db:"physical_model_code"`
	TableName          db.NullString      `json:"table_name" db:"table_name"`
	Status             db.NullString      `json:"status" db:"status"`
	Name               string             `json:"name" db:"name"`
	Description        db.NullString      `json:"description" db:"description"`
	Category           base.ModelCategory `json:"category" db:"category"`
	DispatchCategory   db.NullInt32       `json:"dispatch_category" db:"dispatch_category"`
	CreatedOn          db.NullTime        `json:"created_on" db:"created_on"`
	ModifiedOn         db.NullTime        `json:"modified_on" db:"modified_on"`
	CreatedBy          db.NullString      `json:"created_by" db:"created_by"`
	ModifiedBy         db.NullString      `json:"modified_by" db:"modified_by"`
	SyncOn             int                `json:"sync_on" db:"sync_on"`
	Source             int                `json:"source" db:"source"`
	Resource           string             `json:"resource" db:"resource"`
	ModifyStatus       base.ModifyStatus  `json:"modify_status" db:"modify_status"`
	ReleaseStatus      base.ReleaseStatus `json:"release_status" db:"release_status"`
	SourceProjectLevel int                `json:"source_project_level" db:"source_project_level"`
	EditedStandardFlag int                `json:"edited_standard_flag" db:"edited_standard_flag"`
	IsDel              int                `json:"is_del" db:"is_del"`
	DevMode            string             `json:"dev_mode" db:"dev_mode"`
	ApplyStatus        int                `json:"apply_status" db:"apply_status"`
}

type DepModelID struct {
	ID string `json:"id" db:"id"`
}

type DepModelRelation struct {
	PhysicalModelId    string `json:"physical_model_id" db:"physical_model_id"`
	DepPhysicalModelId string `json:"dep_physical_model_id" db:"dep_physical_model_id"`
}

func (e *ModelEntity) ToBiz() *base.Model {
	return &base.Model{
		ID:                  e.ID,
		SubjectID:           e.SubjectID,
		DefinitionCode:      e.PhysicalModelCode,
		DefinitionTableName: e.TableName.String,
		Status:              e.Status.String,
		Name:                e.Name,
		Description:         e.Description.String,
		Category:            e.Category,
		DispatchCategory:    e.DispatchCategory,
		CreatedOn:           e.CreatedOn,
		ModifiedOn:          e.ModifiedOn,
		CreatedBy:           e.CreatedBy.String,
		ModifiedBy:          e.ModifiedBy.String,
		SyncOn:              e.SyncOn,
		Source:              e.Source,
		Resource:            e.Resource,
		ReleaseStatus:       e.ReleaseStatus,
		ModifyStatus:        e.ModifyStatus,
		SourceProjectLevel:  e.SourceProjectLevel,
		EditedStandardFlag:  e.EditedStandardFlag,
		DevMode:             e.DevMode,
		ApplyStatus:         e.ApplyStatus,
	}
}

type ModelDetail struct {
	ModelEntity
	TableName           db.NullString `json:"table_name" db:"table_name"`
	DefinitionCode      db.NullString `json:"physical_model_code" db:"physical_model_code"`
	ReleaseDefinitionID db.NullString `json:"release_physical_model_id" db:"release_physical_model_id"`
	Publishable         db.NullInt64  `json:"publishable"  db:"publishable"`
	Environment         db.NullString `json:"environment"  db:"environment"`
	IsPhysicalized      db.NullString `json:"is_physicalized"  db:"is_physicalized"`
}

func (e *ModelDetail) ToBiz() *base.Model {
	model := e.ModelEntity.ToBiz()
	model.DefinitionTableName = e.TableName.String
	// 应该是取model中的
	if model.DefinitionTableName == "" {
		model.DefinitionTableName = e.ModelEntity.TableName.String
	}
	model.DefinitionCode = e.DefinitionCode.String
	if model.DefinitionCode == "" {
		model.DefinitionCode = e.ModelEntity.PhysicalModelCode
	}
	model.ReleaseDefinitionID = e.ReleaseDefinitionID.String
	model.Publishable = e.Publishable.Int64
	model.Environment = e.Environment.String
	model.IsPhysicalized = e.IsPhysicalized.String

	return model
}

type EntityRelation struct {
	ID            string                      `json:"id" db:"id"`
	Category      proto.ModelRelationCategory `json:"category" db:"category"`
	Parent        string                      `json:"parent" db:"parent"`
	SourceEntity  string                      `json:"source_entity" db:"source_entity"`
	FromEntity    string                      `json:"from_entity" db:"from_entity"`
	FromFieldName string                      `json:"from_field_name" db:"from_field_name"`
	ToEntity      string                      `json:"to_entity" db:"to_entity"`
	ToFieldName   string                      `json:"to_field_name" db:"to_field_name"`
	CreatedOn     db.NullTime                 `json:"created_on" db:"created_on"`
	ModifiedOn    db.NullTime                 `json:"modified_on" db:"modified_on"`
	CreatedBy     db.NullString               `json:"created_by" db:"created_by"`
	ModifiedBy    db.NullString               `json:"modified_by" db:"modified_by"`
}

func (e *EntityRelation) ToModelBiz() proto.ModelRelation {
	return proto.ModelRelation{
		ID:            e.ID,
		Category:      e.Category,
		Parent:        e.Parent,
		FromCode:      e.FromEntity,
		FromFieldName: e.FromFieldName,
		ToCode:        e.ToEntity,
		ToFieldName:   e.ToFieldName,
		CreatedOn:     e.CreatedOn,
		ModifiedOn:    e.ModifiedOn,
		CreatedBy:     e.CreatedBy.String,
		ModifiedBy:    e.ModifiedBy.String,
	}
}

type ModelStore struct {
	*baseStore.Repo
	logger *logger.Logger
}

func NewModelStore(saas db.SaaS, logger *logger.Logger) *ModelStore {
	return &ModelStore{
		Repo:   baseStore.NewRepo(saas),
		logger: logger,
	}
}

func (s *ModelStore) GetTx(ctx context.Context, project string) (tx *sql.Tx, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	tx, err = sess.Begin()
	return
}

func (s *ModelStore) DeleteModelFieldsUseTx(ctx context.Context, project string, fieldIds []string, tx *sql.Tx) (err error) {
	deleteFieldSQL := "delete from dap_m_physical_model_field where physical_model_id in (:ids)"

	args := map[string]interface{}{"ids": fieldIds}
	newDeleteFieldSQL, deleteArgs, err := sqlx.Named(deleteFieldSQL, args)
	if err != nil {
		return
	}

	newDeleteFieldSQL, deleteArgs, err = sqlx.In(newDeleteFieldSQL, deleteArgs...)
	if err != nil {
		return
	}

	stmt, err := tx.Prepare(newDeleteFieldSQL)
	if err != nil {
		return
	}

	if _, err = stmt.ExecContext(ctx, deleteArgs...); err != nil {
		return
	}
	return
}

func (s *ModelStore) HasModelRelease(ctx context.Context, project string, codes []string) (map[string]struct{}, error) {
	if len(codes) == 0 {
		return map[string]struct{}{}, nil
	}
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	args := map[string]interface{}{
		"environment": entities.ProjectEnvProd,
		"code":        codes,
	}
	var sqlCmd = `select code from dap_m_physical_model where environment=:environment and code in (:code)`
	type ReleaseCode struct {
		Code string `json:"code" db:"code"`
	}
	releaseCodes := make([]ReleaseCode, 0)
	query, queryArgs, err := sqlx.Named(sqlCmd, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	if err = sess.SelectContext(ctx, &releaseCodes, query, queryArgs...); err != nil {
		return nil, err
	}
	result := make(map[string]struct{}, len(releaseCodes))
	for _, code := range releaseCodes {
		result[code.Code] = struct{}{}
	}
	return result, nil
}

func (s *ModelStore) HasIndicatorModelRelease(ctx context.Context, project string, codes []string) (map[string]struct{}, error) {
	if len(codes) == 0 {
		return map[string]struct{}{}, nil
	}
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	args := map[string]interface{}{
		"environment": entities.ProjectEnvProd,
		"code":        codes,
	}
	var sqlCmd = `select code from dap_m_indicator where environment=:environment and code in (:code)`
	type ReleaseCode struct {
		Code string `json:"code" db:"code"`
	}
	releaseCodes := make([]ReleaseCode, 0)
	query, queryArgs, err := sqlx.Named(sqlCmd, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	if err = sess.SelectContext(ctx, &releaseCodes, query, queryArgs...); err != nil {
		return nil, err
	}
	result := make(map[string]struct{}, len(releaseCodes))
	for _, code := range releaseCodes {
		result[code.Code] = struct{}{}
	}
	return result, nil
}

func (s *ModelStore) ListModels(ctx context.Context, project string, req proto.ListModels) ([]*base.Model, error) {
	var modelDBList []ModelEntity

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}

	sqlText := `
		SELECT pm.id, pm.subject_id, pm.name, pm.category, pm.code physical_model_code, pm.description, pm.created_on,
			pm.modified_on, pm.created_by, pm.modified_by, pm.table_name,pm.sync_on, pm.source, pm.modify_status, pm.release_status,
			pm.is_del, pm.dev_mode,pm.resource,pm.source_project_level,pm.edited_standard_flag,pm.apply_status
		FROM dap_m_physical_model pm 
	`
	args := map[string]interface{}{
		"skip": req.GetSkip(),
		"size": req.PageSize,
	}

	// 添加where
	whereSQLs := []string{}

	args["is_del"] = 0
	whereSQLs = append(whereSQLs, "(pm.is_del = :is_del)")
	args["dev_mode"] = []string{"", "sql"}
	whereSQLs = append(whereSQLs, "(pm.dev_mode in (:dev_mode))")
	if req.SubjectID != "" {
		args["subject_id"] = req.SubjectID
		whereSQLs = append(whereSQLs, "(pm.subject_id = :subject_id)")
	}

	if req.Environment != "" {
		args["environment"] = req.Environment
		whereSQLs = append(whereSQLs, "(pm.environment=:environment)")
	} else {
		args["environment"] = cBase.ProjectEnvDev
		whereSQLs = append(whereSQLs, "(pm.environment=:environment)")
	}
	if req.Category != "" {
		args["category"] = req.Category
		whereSQLs = append(whereSQLs, "(pm.category=:category)")
	}

	whereSQL := "where " + strings.Join(whereSQLs, " and ")

	// 添加limit
	limitSQL := " limit :skip, :size"

	// 添加orderby
	var orderBy string
	// 默认created_on
	if req.Sorts != "" {
		switch {
		case strings.Contains(req.Sorts, "name"):
			sort := strings.Split(req.Sorts, " ")
			if len(sort) == 2 {
				orderBy = fmt.Sprintf(" order by pm.%s  ", req.Sorts)
			}
		case strings.Contains(req.Sorts, "created_on"):
			orderBy = fmt.Sprintf(" order by pm.%s ", req.Sorts)
		}
	} else {
		orderBy = " order by pm.created_on "
	}
	sqlText += whereSQL + orderBy + limitSQL

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	if err = sess.SelectContext(ctx, &modelDBList, query, queryArgs...); err != nil {
		return nil, err
	}

	modelList := []*base.Model{}
	for _, e := range modelDBList {
		modelList = append(modelList, e.ToBiz())
	}
	return modelList, nil
}

func (s *ModelStore) ListIndicatorModels(ctx context.Context, project string, req proto.ListModels) ([]*base.Model, error) {
	var modelDBList []ModelEntity

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}

	sqlText := `
		SELECT id, subject_id, name, code as physical_model_code, description,  dev_mode, created_on,modified_on, 
		created_by, modified_by, table_name,  modify_status, release_status, dev_mode, 'category' as category,apply_status,source_project_level,edited_standard_flag FROM dap_m_indicator
	`
	args := map[string]interface{}{
		"skip": req.GetSkip(),
		"size": req.PageSize,
	}

	// 添加where
	whereSQLs := []string{}
	if req.SubjectID != "" {
		args["subject_id"] = req.SubjectID
		whereSQLs = append(whereSQLs, "(subject_id = :subject_id)")
	}

	if req.Environment != "" {
		args["environment"] = req.Environment
		whereSQLs = append(whereSQLs, "(environment=:environment)")
	} else {
		args["environment"] = cBase.ProjectEnvDev
		whereSQLs = append(whereSQLs, "(environment=:environment)")
	}

	whereSQL := "where " + strings.Join(whereSQLs, " and ")

	// 添加limit
	limitSQL := " limit :skip, :size"

	// 添加orderby
	var orderBy string
	// 默认created_on
	if req.Sorts != "" {
		switch {
		case strings.Contains(req.Sorts, "name"):
			sort := strings.Split(req.Sorts, " ")
			if len(sort) == 2 {
				orderBy = fmt.Sprintf(" order by %s  ", req.Sorts)
			}
		case strings.Contains(req.Sorts, "created_on"):
			orderBy = fmt.Sprintf(" order by %s ", req.Sorts)
		}
	} else {
		orderBy = " order by created_on "
	}
	sqlText += whereSQL + orderBy + limitSQL

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	if err = sess.SelectContext(ctx, &modelDBList, query, queryArgs...); err != nil {
		return nil, err
	}

	modelList := []*base.Model{}
	for _, e := range modelDBList {
		modelList = append(modelList, e.ToBiz())
	}
	return modelList, nil
}

type adsBriefWithRawViewContent struct {
	*proto.PhysicalModelBrief
	ViewContent string `db:"view_content"`
}

type physicalModelBriefWithRawContent struct {
	*proto.PhysicalModelBrief
	ViewContent string `json:"view_content" db:"view_content"`
}

func (s *ModelStore) genWhere(args map[string]interface{}, req proto.ListModelDefinitionReq) (sql string) {
	var conditions []string
	conditions = append(conditions, " is_del != 1 ")
	if req.SubjectID != "" {
		subjectIds := strings.Split(req.SubjectID, ",")
		if len(subjectIds) > 1 {
			conditions = append(conditions, "subject_id in (:subjectIDs)")
			args["subjectIDs"] = subjectIds
		} else {
			conditions = append(conditions, "subject_id = :subjectID")
			args["subjectID"] = req.SubjectID
		}
	}
	if req.Category != "" {
		// 支持多个类型多虑，例如dim,dwd这种情况需要
		categorys := strings.Split(req.Category, ",")
		if len(categorys) > 1 {
			conditions = append(conditions, "category in (:categorys)")
			args["categorys"] = categorys
		} else {
			conditions = append(conditions, "category = :category")
			args["category"] = req.Category
		}
	}
	if string(req.Environment) != "" {
		conditions = append(conditions, "environment = :environment")
		args["environment"] = req.Environment
	}
	if len(req.PhysicalModelCodes) > 0 {
		conditions = append(conditions, "code in (:codes)")
		args["codes"] = req.PhysicalModelCodes
	}
	if req.CodeGTE != "" {
		conditions = append(conditions, "code >= :code_gte")
		args["code_gte"] = req.CodeGTE
	}
	if req.Keyword != "" {
		kw := req.GetEscapeKeyword()
		conditions = append(conditions, "(name like :keyword or description like :keyword or table_name like :keyword)")
		args["keyword"] = kw
	}
	if len(req.EtlDevMode) > 0 {
		conditions = append(conditions, "etl_dev_mode = :etl_dev_mode")
		args["etl_dev_mode"] = req.EtlDevMode
	}
	if len(conditions) > 0 {
		strConditions := strings.Join(conditions, " and ")
		sql += "where " + strConditions
	}
	return
}

func (s *ModelStore) ListPhysicalModelBriefs(ctx context.Context, project string, req proto.ListModelDefinitionReq) ([]*proto.PhysicalModelBrief, error) {
	var physicalModelList []*proto.PhysicalModelBrief
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	sqlText := "select id,code,name,table_name,subject_id,resource,created_on,modified_on,created_by,modified_by,category,modify_status,release_status,source_project_level,edited_standard_flag,dev_mode,description,etl_dev_mode from dap_m_physical_model "
	args := map[string]interface{}{
		"skip": req.GetSkip(),
		"size": req.PageSize,
	}
	whereSql := s.genWhere(args, req)
	sqlText += whereSql

	var orderBy string
	// 默认created_on
	if req.Sorts != "" {
		switch {
		case strings.Contains(req.Sorts, "name"):
			sort := strings.Split(req.Sorts, " ")
			if len(sort) == 2 {
				orderBy = fmt.Sprintf(" order by CONVERT(%s using gbk) %s ", sort[0], sort[1])
			}
		default:
			orderBy = fmt.Sprintf(" order by %s ", req.Sorts)
		}
	} else {
		orderBy = " order by created_on "
	}

	limitSQL := " limit :skip, :size "
	sqlText += orderBy + limitSQL
	_, err = commonUtils.GetSelectResp(ctx, sess, sqlText, &physicalModelList, args)
	if err != nil {
		return nil, err
	}

	return physicalModelList, nil
}

func (s *ModelStore) BatchGetModelFieldCount(ctx context.Context, project string, req proto.BatchGetModelFieldCountReq) (result map[string]*proto.BatchGetModelFieldCountRspItem, err error) {
	sqlText := "select physical_model_id as id, count(*) as count from dap_m_physical_model_field where physical_model_id in (:model_ids) group by physical_model_id"
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}

	result = map[string]*proto.BatchGetModelFieldCountRspItem{}
	if len(req.ModelIDs) == 0 {
		return
	}

	args := map[string]interface{}{
		"model_ids": req.ModelIDs,
	}

	var aggDatas []*proto.BatchGetModelFieldCountRspItem
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	if err = sess.SelectContext(ctx, &aggDatas, query, queryArgs...); err != nil {
		return nil, err
	}

	for _, item := range aggDatas {
		result[item.ID] = item
	}
	return
}
