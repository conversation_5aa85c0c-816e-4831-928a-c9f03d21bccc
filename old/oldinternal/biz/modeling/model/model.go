package model

import (
	"context"
	"database/sql"
	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	newModelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewModelService))
}

type ModelService struct {
	logger *logger.Logger
	repo   ModelRepo
}

func NewModelService(logger *logger.Logger, repo ModelRepo) (*ModelService, error) {

	ms := &ModelService{
		logger: logger,
		repo:   repo,
	}
	return ms, nil
}

func (s *ModelService) List(ctx context.Context, project string, data proto.ListModels) ([]*base.Model, error) {
	models, err := s.repo.ListModels(ctx, project, data)
	if err != nil && err != sql.ErrNoRows {
		return nil, err
	}
	codes := make([]string, 0, len(models))
	for _, code := range models {
		codes = append(codes, code.DefinitionCode)
	}
	releaseCodes, err := s.repo.HasModelRelease(ctx, project, codes)
	if err != nil {
		return nil, err
	}
	for idx, model := range models {
		model.IndividualType = newModelBase.ConvertSourceProjectLevelToInIndividualType(model.SourceProjectLevel, model.EditedStandardFlag)
		if _, ok := releaseCodes[model.DefinitionCode]; ok {
			models[idx].IsRelease = true
		}
	}
	return models, nil
}

func (s *ModelService) DwsViewList(ctx context.Context, project string, data proto.ListModels) ([]*base.Model, error) {
	models, err := s.repo.ListIndicatorModels(ctx, project, data)
	if err != nil && err != sql.ErrNoRows {
		return nil, err
	}
	codes := make([]string, 0, len(models))
	for _, code := range models {
		codes = append(codes, code.DefinitionCode)
	}
	releaseCodes, err := s.repo.HasIndicatorModelRelease(ctx, project, codes)
	if err != nil {
		return nil, err
	}
	for idx, model := range models {
		model.IndividualType = newModelBase.ConvertSourceProjectLevelToInIndividualType(model.SourceProjectLevel, model.EditedStandardFlag)
		if _, ok := releaseCodes[model.DefinitionCode]; ok {
			models[idx].IsRelease = true
		}
	}
	return models, nil
}

func (s *ModelService) ListPhysicalModelBriefs(ctx context.Context, project string, req proto.ListModelDefinitionReq) (records []*proto.PhysicalModelBrief, err error) {
	return s.repo.ListPhysicalModelBriefs(ctx, project, req)
}

func (s *ModelService) GetBuiltinFunctions() (map[base.FuncCategory][]base.BuiltinFunction, error) {
	funcMap := make(map[base.FuncCategory][]base.BuiltinFunction)
	funcMap[base.AggregateFunc] = base.AggregateFuncs
	funcMap[base.StringFunc] = base.StringFuncs
	funcMap[base.NumericalFunc] = base.NumericalFuncs
	funcMap[base.DatetimeFunc] = base.DatetimeFuncs
	funcMap[base.LogicFunc] = base.LogicFuncs
	return funcMap, nil
}
