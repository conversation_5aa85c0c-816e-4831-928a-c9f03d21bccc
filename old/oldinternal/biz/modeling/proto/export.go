package proto

import (
	"time"
)

type ModelParam struct {
	Name          string       `json:"name" form:"name"`
	NameCn        string       `json:"name_cn" form:"name_cn"`
	Category      []string     `json:"category" form:"category"`
	SubjectId     string       `json:"subject_id" form:"subject_id"`
	Submitter     string       `json:"submitter" form:"submitter"`
	StartDate     string       `json:"start_date" form:"start_date"`
	EndDate       string       `json:"end_date" form:"end_date"`
	ReleaseStatus string       `json:"release_status" form:"release_status"`
	Models        []BriefModel `json:"models" form:"models"`
}

type ExportModel struct {
	Name          string    `json:"name" form:"name" db:"name"`
	NameCn        string    `json:"name_cn" form:"name_cn" db:"name_cn"`
	Category      string    `json:"category" form:"category" db:"category"`
	SubjectName   string    `json:"subject_name" form:"subject_name" db:"subject_name"`
	Submitter     string    `json:"submitter" form:"submitter" db:"modified_by"`
	SubmitTime    time.Time `json:"submit_time" form:"submit_time" db:"modified_on"`
	ReleaseStatus string    `json:"release_status" form:"release_status" db:"release_status"`
	Code          string    `json:"code" form:"code" db:"code"`
	Id            string    `json:"id" form:"id" db:"id"`
	SubjectId     string    `json:"subject_id" form:"subject_id" db:"subject_id"`
}


type BriefModel struct {
	Category string `json:"category" form:"category" db:"category"`
	Code     string `json:"code" form:"code" db:"code"`
}
