package proto

import (
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	logicBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

type AsyncRunSQL struct {
	ViewContent *logicBase.AdsViewContent    `form:"view_content" json:"view_content"`
	Env         cBase.ProjectEnvType         `form:"env" json:"env"`
	Resource    entities.ProjectResourceType `form:"resource" json:"resource"`
	// 业务模型字段
	TenantCode string `form:"tenant_code" json:"tenant_code"`
	DevMode    string `form:"dev_mode" json:"dev_mode"`
	IsNotRun   bool
	IsNotLimit bool
}

type AsyncRunSQLResult struct {
	Env         cBase.ProjectEnvType         `form:"env" json:"env"`
	SQL         string                       `form:"sql" json:"sql"`
	InstanceID  string                       `form:"instance_id" json:"instance_id" binding:"required"`
	ViewContent *logicBase.AdsViewContent    `form:"view_content" json:"view_content"`
	Resource    entities.ProjectResourceType `form:"resource" json:"resource"`
}
