package proto

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"regexp"

	"gitlab.mypaas.com.cn/dmp/gopkg/db"

	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	ivBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/indicator_view/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	physicalModelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/server/physical_model/proto"
)

type ListIndicatorViewParamReq struct {
	ID          string               `form:"id" json:"id"`
	Code        string               `form:"code" json:"code"`
	Environment cBase.ProjectEnvType `form:"environment" json:"environment"`
}

type ListIndicatorViewParamRespItem struct {
	ivBase.IndicatorParam
}

type UpdateIndicatorViewParamReq struct {
	ID    string                  `form:"id" json:"id"`
	Param []ivBase.IndicatorParam `form:"param" json:"param"`
}

type AsyncRunSQLIndicatorParamItem struct {
	Name         string               `form:"name" json:"name"`
	Value        string               `form:"value" json:"value"`
	DefaultValue string               `form:"default_value" json:"default_value"`
	DataType     ivBase.ParamDataType `form:"data_type" json:"data_type"`
}

const ParamPatternStr = `\$\{%s\}`

type ListIndicatorViewReq struct {
	SubjectID     string                  `form:"subject_id" json:"subject_id" binding:"required"`
	Category      modelBase.ModelCategory `form:"category" json:"category" binding:"required"`
	Environment   cBase.ProjectEnvType    `form:"environment" json:"environment" binding:"oneof=生产 开发 历史"`
	Code          string                  `form:"code" json:"code"`
	Codes         []string                `form:"codes" json:"codes"`
	CodeGTE       string
	MergeStandard bool `form:"merge_standard" json:"merge_standard"`
	cBase.QueryBaseRequest
}

type ListIndicatorViewRespItem struct {
	ivBase.Indicator
	DependentModel *ListModelDefinitionDepModelItem `json:"dependent_model" db:"dependent_model"`
}

type IndicatorViewBrief struct {
	ID                 string                  `json:"id" db:"id"`
	Code               string                  `json:"code" db:"code"`
	PhysicalModelCode  string                  `json:"physical_model_code" db:"physical_model_code"`
	MultiDimModelCode  string                  `json:"multi_dim_model_code" db:"multi_dim_model_code"`
	Name               string                  `json:"name" db:"name"`
	ViewName           string                  `json:"view_name" db:"view_name"`
	SubjectID          string                  `json:"subject_id" db:"subject_id"`
	CreatedOn          db.NullTime             `json:"created_on" db:"created_on"`
	CreatedBy          db.NullString           `json:"created_by" db:"created_by"`
	ModifiedOn         db.NullTime             `json:"modified_on" db:"modified_on"`
	ModifiedBy         db.NullString           `json:"modified_by" db:"modified_by"`
	ReleaseStatus      modelBase.ReleaseStatus `json:"release_status" db:"release_status"`
	ModifyStatus       modelBase.ModifyStatus  `json:"modify_status" db:"modify_status"`
	SourceProjectLevel int                     `json:"source_project_level" db:"source_project_level"`
	DevMode            string                  `json:"dev_mode" db:"dev_mode"`
	Description        string                  `json:"description" db:"description"`
}

type IndicatorViewBriefWithContent struct {
	*IndicatorViewBrief
	ViewContent *base.AdsViewContent
}

var CustomExprKwPattern = regexp.MustCompile(`([a-zA-Z]+?)\(`)

// 聚合函数
var AggFuncMap = map[string]string{
	"AVG":   "",
	"COUNT": "",
	"SUM":   "",
	"MIN":   "",
	"MAX":   "",
}

type GetIndicatorCountBySubjectItem struct {
	SubjectId string `json:"subject_id" db:"subject_id"`
	Count     int    `json:"count" db:"count"`
}

type GetIndicatorCountBySubjectRsp []GetIndicatorCountBySubjectItem

type ValidateIndicatorExprReq struct {
	Expr           string                       `json:"expr"`
	Resource       entities.ProjectResourceType `json:"resource"`
	Dependence     *rpc_call.Dependence         `json:"dependence"`
	Variables      []rpc_call.Variable          `json:"variables"`
	DialectSetting global.DialectSettingType    `json:"dialect_setting"`
}

type ValidateIndicatorExprRsp struct {
	Errors   []string `json:"errors"`
	Warnings []string `json:"warnings"`
}

type GetIndicatorAvailableFiltersReq struct {
	Dependence         *rpc_call.Dependence `json:"dependence"`
	IndicatorTableName string               `json:"indicator_table_name"`
}

type GetIndicatorAvailableFiltersRsp struct {
	NoLimit               bool     `json:"no_limit"`
	AvailableFilterTables []string `json:"available_filter_tables"`
}

type GetAvailableDimAndFiltersReq struct {
	Environment string                                  `json:"environment"`
	ViewContent *base.AdsViewContent                    `json:"view_content"`
	TenantCode  string                                  `json:"tenant_code"`
	Fields      []physicalModelProto.PhysicalModelField `json:"fields"`
}

type GetAvailableDimAndFiltersRsp struct {
	NoLimit                  bool                  `json:"no_limit"`
	AvailableDimensionFields []*rpc_call.PropField `json:"available_dimension_fields"`
	AvailableFilterTables    []string              `json:"available_filter_tables"`
}

type GetAllDimensionalReq struct {
	Code        string `form:"code" json:"code" binding:"required"`
	RefCategory string `json:"ref_category" form:"ref_category"`
	RefCode     string `json:"ref_code" form:"ref_code"`
}

type GetPublicDimensionReq struct {
	Code           string   `form:"code" json:"code" binding:"required"`
	IndicatorCodes []string `form:"indicator_codes" json:"indicator_codes" binding:"required"`
}
