package proto

import (
	"time"

	baseModels "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	ivBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/indicator_view/base"
	logicBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/logic/base"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	physicalModelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/server/physical_model/proto"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type ModelRelationType string
type ModelRelationCategory string

const (
	ModelRelationParent ModelRelationType = "parent"

	ModelRelationModel     ModelRelationCategory = "model"
	TableRelationModel     ModelRelationCategory = "table"
	FieldRelationModel     ModelRelationCategory = "field"
	FieldBindRelationModel ModelRelationCategory = "field_bind"
	LineageModelRelation   ModelRelationCategory = "lineage"
)

type AddModel struct {
	Name        string                  `form:"name" json:"name" binding:"required"`
	Description string                  `form:"description" json:"description"`
	SubjectID   string                  `form:"subject_id" json:"subject_id" binding:"required"`
	Category    modelBase.ModelCategory `form:"category" json:"category" binding:"required"`
}

type AddBusinessOrPhysicsModel struct {
	SubjectID   string                       `form:"subject_id" json:"subject_id" binding:"required"`
	Name        string                       `form:"name" json:"name" binding:"required"`
	Category    modelBase.ModelCategory      `form:"category" json:"category" binding:"required"`
	TableName   string                       `form:"table_name" json:"table_name" binding:"required"`
	Description string                       `form:"description" json:"description"`
	Label       modelBase.ModelLabel         `form:"label" json:"label"`
	AdsCategory modelBase.AdsCategory        `form:"ads_category" json:"ads_category"`
	Source      int32                        `form:"source" json:"source"`
	Resource    entities.ProjectResourceType `form:"resource" json:"resource"`
	DevMode     string                       `form:"dev_mode" json:"dev_mode"`
}

type UpdateBusinessOrPhysicsModel struct {
	Code        string               `form:"code" json:"code" binding:"required"`
	Name        string               `form:"name" json:"name" binding:"required"`
	Description string               `form:"description" json:"description"`
	Environment cBase.ProjectEnvType `form:"environment" json:"environment" binding:"oneof=生产 开发"`
	Label       modelBase.ModelLabel `form:"label" json:"label"`
}

type ListModels struct {
	SubjectID   string                       `form:"subject_id" json:"subject_id" binding:"required"`
	Category    modelBase.ModelCategory      `form:"category" json:"category" binding:"required"`
	Categorys   []modelBase.ModelCategory    `form:"categorys" json:"categorys"`
	Environment string                       `form:"environment" json:"environment"`
	Code        string                       `form:"code" json:"code"`
	Name        string                       `form:"name" json:"name"`
	TableName   string                       `form:"table_name" json:"table_name"`
	Resource    entities.ProjectResourceType `form:"resource" json:"resource"`
	cBase.QueryBaseRequest
}

type QueryModels struct {
	SubjectID            string                       `form:"subject_id" json:"subject_id" binding:"required"`
	CategoryModel        []string                     `form:"category_model" json:"category_model"`
	Category             string                       `form:"category" json:"category"`
	Environment          entities.ProjectEnvType      `form:"environment" json:"environment"`
	ProjectCode          string                       `form:"project_code" json:"project_code"`
	RequireAggData       bool                         `form:"require_agg_data" json:"require_agg_data"`
	RequireModelRelation bool                         `form:"require_model_relation" json:"require_model_relation"`
	Sorts                string                       `form:"sorts" json:"sorts" binding:"validateQuerySorts=created_on name"`
	Resource             entities.ProjectResourceType `form:"resource" json:"resource"`
	cBase.QueryBaseRequest
}

type UpdateModel struct {
	ID          string `form:"id" json:"id" binding:"required"`
	Code        string `form:"code" json:"code"`
	Name        string `form:"name" json:"name" binding:"required"`
	Description string `form:"description" json:"description"`
	Category    string `form:"category" json:"category"`
	// 原主题
	SubjectID string `form:"subject_id" json:"subject_id" binding:"required"`
	// 变更目标主题
	SubjectTargetID string `form:"subject_target_id" json:"subject_target_id" binding:"required"`
}

type GetModelDefinition struct {
	Code        string                `form:"code" json:"code"`
	ID          string                `form:"id" json:"id"`
	Environment cBase.ProjectEnvType  `form:"environment" json:"environment"`
	AdsCategory modelBase.AdsCategory `form:"ads_category" json:"ads_category"`
}

type PhysicalModelBrief struct {
	ID                 string                    `json:"id" db:"id"`
	Code               string                    `json:"code" db:"code"`
	SubjectID          string                    `json:"subject_id" db:"subject_id"`
	Name               string                    `json:"name" db:"name"`
	TableName          string                    `json:"table_name" db:"table_name"`
	Category           modelBase.ModelCategory   `json:"category" db:"category"`
	ReleaseStatus      modelBase.ReleaseStatus   `json:"release_status" db:"release_status"`
	Resource           cBase.ProjectResourceType `json:"resource" db:"resource"`
	ModifyStatus       modelBase.ModifyStatus    `json:"modify_status" db:"modify_status"`
	CreatedOn          db.NullTime               `json:"created_on" db:"created_on"`
	CreatedBy          db.NullString             `json:"created_by" db:"created_by"`
	ModifiedOn         db.NullTime               `json:"modified_on" db:"modified_on"`
	ModifiedBy         db.NullString             `json:"modified_by" db:"modified_by"`
	SourceProjectLevel int                       `json:"source_project_level" db:"source_project_level"`
	EditedStandardFlag int                       `json:"edited_standard_flag" db:"edited_standard_flag"`
	DevMode            string                    `json:"dev_mode" db:"dev_mode"`
	Description        string                    `json:"description" db:"description"`
	EtlDevMode         string                    `json:"etl_dev_mode" db:"etl_dev_mode"`
}

type AdsPhysicalModelBriefWithContent struct {
	*PhysicalModelBrief
	ViewContent *logicBase.AdsViewContent `json:"view_content" db:"view_content"`
}

type ListModelDefinition struct {
	ID               string                           `json:"id" db:"id"`
	Code             string                           `json:"code" db:"code"`
	ModelID          db.NullString                    `json:"business_model_id" db:"business_model_id"`
	TableName        string                           `json:"table_name" db:"table_name"`
	CreatedOn        db.NullTime                      `json:"created_on" db:"created_on"`
	ModifiedOn       db.NullTime                      `json:"modified_on" db:"modified_on"`
	CreatedBy        db.NullString                    `json:"created_by" db:"created_by"`
	DispatchCategory db.NullInt32                     `json:"dispatch_category" db:"dispatch_category"`
	Name             db.NullString                    `json:"name" db:"name"`
	Description      db.NullString                    `json:"description" db:"description"`
	Schedule         db.NullString                    `json:"schedule" db:"schedule"`
	Status           db.NullString                    `json:"status" db:"status"`
	Category         modelBase.ModelCategory          `json:"category" db:"category"`
	Label            modelBase.ModelLabel             `form:"label" json:"label"`
	AdsCategory      modelBase.AdsCategory            `form:"ads_category" json:"ads_category"`
	DependentModel   *ListModelDefinitionDepModelItem `json:"dependent_model" db:"dependent_model"`
	IsRelease        bool                             `json:"is_release"`
	ReleaseID        string                           `json:"release_id"`
	Resource         entities.ProjectResourceType     `json:"resource"`
	SubjectID        string                           `json:"subject_id" db:"subject_id"`
	ModifyStatus     modelBase.ModifyStatus           `json:"modify_status" db:"modify_status"`
	ReleaseStatus    modelBase.ReleaseStatus          `json:"release_status" db:"release_status"`
}

type ListModelDefinitionDepModelItem struct {
	Code     string                  `json:"code" db:"code"`
	Name     string                  `json:"name" db:"name"`
	Category modelBase.ModelCategory `json:"category" db:"category"`
}

type ListModelDefinitionReq struct {
	SubjectID          string   `form:"subject_id" json:"subject_id" binding:"required"`
	Category           string   `form:"category" json:"category" binding:"required"`
	Sorts              string   `form:"sorts" json:"sorts" binding:"validateQuerySorts=created_on name"`
	PhysicalModelCodes []string `form:"physical_model_codes" json:"physical_model_codes"`
	CodeGTE            string
	Environment        cBase.ProjectEnvType `form:"environment" json:"environment"`
	MergeStandard      bool                 `form:"merge_standard" json:"merge_standard"`
	EtlDevMode         string               `form:"etl_dev_mode" json:"etl_dev_mode"`
	cBase.QueryBaseRequest
}

type BatchListModelDefinitionReq struct {
	SubjectID           string                       `form:"subject_id" json:"subject_id" binding:"required"`
	Categorys           string                       `form:"categorys" json:"categorys"`
	Sorts               string                       `form:"sorts" json:"sorts" binding:"validateQuerySorts=created_on name"`
	Environment         cBase.ProjectEnvType         `form:"environment" json:"environment" binding:"oneof=生产 开发"`
	Resource            entities.ProjectResourceType `form:"resource" json:"resource"`
	IncludeUnsavedModel int                          `form:"include_unsaved_model" json:"include_unsaved_model"`
	cBase.QueryBaseRequest
}

type BatchGetModelRelationsReq struct {
	ModelIDs []string
}

type ModelDepCode struct {
	ID      string `json:"id" db:"id"`
	DepCode string `json:"dep_code" db:"dep_code"`
}

type BatchGetModelFieldCountReq struct {
	ModelIDs []string
}

type BatchGetModelFieldCountRspItem struct {
	ID    string `form:"id" json:"id" db:"id"`
	Count int    `form:"count" json:"count" db:"count"`
}

type BatchGetModelFieldCountRsp []BatchGetModelFieldCountRspItem

type UpdateModelDefinitionField struct {
	ID                    string                     `form:"id" json:"id"`
	ModelFieldID          string                     `form:"business_model_field_id" json:"business_model_field_id"`
	DefinitionID          string                     `form:"physical_model_id" json:"physical_model_id"`
	Name                  string                     `form:"name" json:"name" binding:"required,physicalFiledName"`
	FieldType             string                     `form:"field_type" json:"field_type"`
	Length                int64                      `form:"length" json:"length"`
	IsPartition           int64                      `form:"is_partition" json:"is_partition"`
	Rank                  int64                      `form:"rank" json:"rank"`
	PartitionRank         int64                      `form:"partition_rank" json:"partition_rank"`
	NameCN                string                     `form:"name_cn" json:"name_cn"`
	Description           string                     `form:"description" json:"description"`
	PhysicalModelFieldID  string                     `form:"physical_model_field_id" json:"physical_model_field_id"`
	FieldCategory         string                     `form:"field_category" json:"field_category,omitempty"`
	ParentField           string                     `form:"parent_field" json:"parent_field"`
	PrimaryRank           int32                      `form:"primary_rank" json:"primary_rank"`
	IsPrimary             int64                      `form:"is_primary" json:"is_primary"`
	AssetType             string                     `form:"asset_type" json:"asset_type"`
	DimType               modelBase.DimType          `form:"dim_type" json:"dim_type"`
	DataStandards         string                     `form:"data_standards" json:"data_standards"`
	Scale                 int                        `form:"scale" json:"scale"`
	ValueComment          string                     `form:"value_comment" json:"value_comment"`
	IndicatorBusinessCode string                     `form:"indicator_business_code" json:"indicator_business_code"`
	Code                  string                     `form:"code" json:"code"`
	FieldCode             string                     `form:"field_code" json:"field_code"`
	BusinessModelFieldID  string                     `json:"business_model_field_id"  gorm:"column:business_model_field_id;type:char(36)"`
	TableFieldName        string                     `json:"table_field_name"  gorm:"column:table_field_name"`
	DimViewData           string                     `json:"dim_view_data"  gorm:"column:dim_view_data"`
	FieldBusinessType     int                        `json:"field_business_type"  gorm:"column:field_business_type"`
	SystemFieldType       string                     `json:"system_field_type" gorm:"-"`
	StorageFieldType      string                     `json:"storage_field_type" gorm:"-"`
	GroupName             string                     `gorm:"column:group_name" json:"group_name"` // 字段分组名称
	ParentId              string                     `json:"parent_id"  gorm:"column:parent_id"`
	ViewModel             *baseModels.FieldViewModel `gorm:"-" json:"view_model"`
}

type ModelContent struct {
	SQL     string `form:"sql" json:"sql"`
	DDL     string `json:"ddl"`
	RunSQL  string `json:"run_sql"`
	ViewSQL string `json:"view_sql"`
}

type UpdateModelDefinitionSyncConfigReq struct {
	// 物理模型id
	DefinitionID string                  `form:"id" json:"id" binding:"required"`
	Category     modelBase.ModelCategory `form:"category" json:"category"`
	SyncConfig   interface{}             `form:"sync_config" json:"sync_config"`
}

type UpdateModelDefinitionReq struct {
	// 物理模型id
	DefinitionID string                   `form:"id" json:"id" binding:"required"`
	Code         string                   `form:"code" json:"code" binding:"required"`
	Name         string                   `form:"name" json:"name"`
	Category     modelBase.ModelCategory  `form:"category" json:"category"`
	SubjectId    string                   `form:"subject_id" json:"subject_id"`
	Content      ModelContent             `form:"content" json:"content" binding:"required"`
	ViewContent  interface{}              `form:"view_content" json:"view_content"`
	SyncConfig   interface{}              `form:"sync_config" json:"sync_config"`
	SyncOn       int                      `form:"sync_on" json:"sync_on"`
	Mode         logicBase.ModelLogicMode `form:"mode" json:"mode"`
	// 物理化字段
	Fields []UpdateModelDefinitionField `form:"fields" json:"fields" binding:"required,dive"`
	// 业务模型字段
	ModelFields []*modelBase.ModelField `form:"business_model_fields" json:"business_model_fields"`
	Publishable int
	Description string                `form:"description" json:"description"`
	Label       modelBase.ModelLabel  `form:"label" json:"label"`
	AdsCategory modelBase.AdsCategory `form:"ads_category" json:"ads_category"`
	TableName   string                `form:"table_name" json:"table_name"`
	// 模块类型：物理模型；标签萃取；
	ModuleCategory        string                                       `form:"module_category" json:"module_category"`
	DispatchCategory      int                                          `form:"dispatch_category" json:"dispatch_category"`
	BindingFlow           interface{}                                  `form:"binding_flow" json:"binding_flow"`
	NodeDispatch          interface{}                                  `form:"node_dispatch" json:"node_dispatch"`
	SyncBindingFlow       interface{}                                  `form:"sync_binding_flow" json:"sync_binding_flow"`
	SyncNodeDispatch      interface{}                                  `form:"sync_node_dispatch" json:"sync_node_dispatch"`
	ShowFields            string                                       `form:"show_fields" json:"show_fields"`
	Source                int                                          `form:"source" json:"source"`
	Resource              entities.ProjectResourceType                 `form:"resource" json:"resource"`
	DeriveFieldConfigList []physicalModelProto.DeriveFieldConfig       `form:"derive_field_config_list" json:"derive_field_config_list"`
	DisableDimensional    []ivBase.BriefAnalysisDimension              `form:"disable_dimensional" json:"disable_dimensional"`
	GroupFields           []physicalModelProto.PhysicalModelGroupField `json:"group_fields"`
	GroupFieldsOrder      []*physicalModelProto.GroupFieldsOrder       `json:"group_fields_order"`
	CreatedBy             string                                       `json:"created_by,omitempty" db:"created_by"`
	ModifiedBy            string                                       `json:"modified_by,omitempty" db:"modified_by"`
	SourceProjectLevel    int                                          `json:"source_project_level" db:"source_project_level"` // 来源项目级别
	DevMode               string                                       `json:"dev_mode" db:"dev_mode"`
}

type UpdateModelDefinitionRsp struct {
	DdlChange bool `json:"ddl_change"`
}

type UpdateModelDefinitionInfo struct {
	ID              string               `form:"id" json:"id" binding:"required"`
	Name            string               `form:"name" json:"name" binding:"required"`
	Code            string               `form:"code" json:"code"`
	Description     string               `form:"description" json:"description"`
	SubjectID       string               `form:"subject_id" json:"subject_id"`
	SubjectTargetID string               `form:"subject_target_id" json:"subject_target_id"`
	ModifyBy        string               `form:"modify_by" json:"modify_by"`
	Label           modelBase.ModelLabel `form:"label" json:"label"`
}

type NodeCntTableColumn struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Value       string `json:"value"` // 可选，分区字段 value 值
	Comment     string `json:"comment"`
	IsPartition bool   `json:"is_partition"`
}

type ListRelationRequest struct {
	Type     string                `form:"type" json:"type"`
	Category ModelRelationCategory `form:"category" json:"category"`
	Code     string                `form:"code" json:"code"`
}

type GetModelRelationRequest struct {
	BusinessID        string                `form:"id" json:"id"`
	PhysicalModelCode string                `form:"physical_model_code" json:"physical_model_code" binding:"required"`
	Type              string                `form:"type" json:"type"`
	Category          ModelRelationCategory `form:"category" json:"category"`
}

type ListRelationResponse = []ModelRelation

type GetModelRelationResponse = ListRelationResponse

type ModelRelation struct {
	ID            string                `form:"id" json:"id"`
	Category      ModelRelationCategory `form:"category" json:"category"`
	Parent        string                `form:"parent" json:"parent"`
	SourceEntity  string                `form:"source_entity" json:"source_entity"`
	FromCode      string                `form:"from_code" json:"from_code"`
	FromFieldName string                `form:"from_field_name" json:"from_field_name"`
	ToCode        string                `form:"to_code" json:"to_code"`
	ToFieldName   string                `form:"to_field_name" json:"to_field_name"`
	CreatedOn     db.NullTime           `json:"created_on,omitempty" db:"created_on"`
	ModifiedOn    db.NullTime           `json:"modified_on,omitempty" db:"modified_on"`
	CreatedBy     string                `json:"created_by,omitempty" db:"created_by"`
	ModifiedBy    string                `json:"modified_by,omitempty" db:"modified_by"`
}

type UpdateModelRelationRequest struct {
	PhysicalModelCode string                `form:"physical_model_code" json:"physical_model_code" binding:"required"`
	ToFieldName       string                `form:"to_field_name" json:"to_field_name"`
	Type              string                `form:"type" json:"type"`
	Category          ModelRelationCategory `form:"category" json:"category"`
	Relations         []UpdateModelRelation `form:"relations" json:"relations"`
}

type UpdateModelRelation struct {
	ID            string `form:"id" json:"id"`
	Parent        string `form:"parent" json:"parent"`
	FromCode      string `form:"from_code" json:"from_code" binding:"required"`
	FromFieldName string `form:"from_field_name" json:"from_field_name" binding:"required"`
	ToCode        string `form:"to_code" json:"to_code" binding:"required"`
	ToFieldName   string `form:"to_field_name" json:"to_field_name" binding:"required"`
}

type LinkRelation struct {
	FromID        string              `form:"from_id" json:"from_id"`
	FromFieldName string              `form:"from_field_name" json:"from_field_name"`
	ToIDs         []*RelationToIDItem `form:"to_ids" json:"to_ids"`
}

type RelationToIDItem struct {
	ToID        string `form:"to_id" json:"to_id"`
	ToFieldName string `form:"to_field_name" json:"to_field_name"`
}

type ReleaseDefinitionReq struct {
	ID          string                `form:"id" json:"id"`
	Code        string                `form:"code" json:"code"`
	AdsCategory modelBase.AdsCategory `form:"ads_category" json:"ads_category"`
	Comment     string                `form:"comment" json:"comment" binding:"required"`
	Category    modelBase.ModelCategory
	IsImport    bool
}

type OfflineDefinitionReq struct {
	Code string `form:"code" json:"code" binding:"required"`
}

type DeleteDefinitionReq struct {
	ID          string                `form:"id" json:"id"`
	Code        string                `form:"code" json:"code"`
	AdsCategory modelBase.AdsCategory `form:"ads_category" json:"ads_category"`
}

type GetAdsDepModelRespItem struct {
	PhysicalModelID string        `json:"physical_model_id" db:"physical_model_id"`
	Code            db.NullString `json:"code" db:"code"`
	Name            db.NullString `json:"name" db:"name"`
}

type GetVersionListReq struct {
	Code     string                  `form:"code" json:"code" db:"code"`
	Category modelBase.ModelCategory `form:"category" json:"category"`
	cBase.QueryBaseRequest
}

type GetVersionListItem struct {
	ID             string        `json:"id"`
	Code           string        `json:"code"`
	ModifiedBy     db.NullString `json:"modified_by"`
	ModifiedOn     db.NullTime   `json:"modified_on"`
	ReleaseComment string        `json:"release_comment"`
	Version        int32         `json:"version"`
}

type GetVersionListRsp []GetVersionListItem

type GetDependentReleaseModelReq struct {
	Code     string                  `form:"code" json:"code" db:"code"`
	Category modelBase.ModelCategory `form:"category" json:"category"`
}

type ReleaseCheckReq struct {
	ID       string                  `form:"id" json:"id"`
	Category modelBase.ModelCategory `form:"category" json:"category"`
}

type ReleaseCheckRsp struct {
	RejectMsg string `form:"reject_msg" json:"reject_msg"`
	RiskMsg   string `form:"risk_msg" json:"risk_msg"`
}

// FastReportModelMessage 维度建模上报天眼日志结构体
type FastReportModelMessage struct {
	ID                   string                  `json:"id"` // 模型id、code、name
	Code                 string                  `json:"code"`
	Name                 string                  `json:"name"`
	Category             modelBase.ModelCategory `json:"category"`
	NodeID               string                  `json:"node_id"` // 节点id、code、name
	NodeCode             string                  `json:"node_code"`
	NodeName             string                  `json:"node_name"`
	NodeVersion          string                  `json:"node_version"`
	SubjectID            string                  `json:"subject_id"` // 主题
	SubjectName          string                  `json:"subject_name"`
	Status               string                  `json:"status"` // 状态，todo 枚举
	ReleaseTime          time.Time               `json:"release_time"`
	OfflineTime          time.Time               `json:"offline_time"`
	Msg                  string                  `json:"msg"`
	BizExtend            string                  `json:"biz_extend"`
	Extend               string                  `json:"extend"`
	LogTime              time.Time               `json:"log_time"`
	AdsCategory          modelBase.AdsCategory   `json:"ads_category"`
	IndicatorFieldsCount int                     `json:"indicator_fields_count"` // 指标字段数量
	DimensionFieldsCount int                     `json:"dimension_fields_count"` // 维度字段数量
}

type SetModePublishAbleReq struct {
	Id      string `form:"id" json:"id" binding:"required"`
	Publish int32  `form:"publish" json:"publish"`
}

type LastReleaseStatus struct {
	Status   int    `json:"status"`
	TaskID   string `json:"task_id"`
	BatchID  string `json:"batch_id"`
	NodeCode string `json:"node_code"`
}

type UpgradeTaskType string

const (
	DataflowMoveoutUpgrade          UpgradeTaskType = "dataflow_moveout_upgrade"
	AdsUpgrade                      UpgradeTaskType = "ads_upgrade"
	IndicatorUpgrade                UpgradeTaskType = "indicator_upgrade"
	MultiDimLinkQuantityTypeUpgrade UpgradeTaskType = "multi_dim_link_quantity_type_upgrade"
	AdsIndicatorAggInfoUpgrade      UpgradeTaskType = "ads_indicator_agg_info_upgrade"
	SubjectDelBugFixUpgrade         UpgradeTaskType = "subject_del_bug_fix_upgrade"
	IndicatorCodeFixUpgrade         UpgradeTaskType = "indicator_code_fix_upgrade"
)

type ModelUpgradeReq struct {
	UpgradeType            int             `json:"upgrade_type"`          //升级类型:默认0,0-升级指定项目,1-升级全部升级
	UpgradeTaskType        UpgradeTaskType `json:"upgrade_task_type"`     // 升级任务类型: 默认为dataflow_moveout_upgrade
	Async                  bool            `json:"async"`                 // 单租户异步执行
	Project                string          `json:"project"`               //升级类型为0时有效,指定升级项目库
	Time                   string          `json:"time"`                  //升级该时间之前的数据
	AdsPhysicalModelId     string          `json:"ads_physical_model_id"` // 应用表物理模型id
	AdsIndicatorId         string          `json:"ads_indicator_id"`      // 应用表视图模型id
	MultiDimModelId        string          `json:"multi_dim_model_id"`    // 多维模型id
	IndicatorCodeFixAction string          `json:"indicator_code_fix_action"`
}

type GetPhysicalModelCountBySubjectItem struct {
	SubjectId string `json:"subject_id" db:"subject_id"`
	Count     int    `json:"count" db:"count"`
}

type GetPhysicalModelCountBySubjectRsp []GetPhysicalModelCountBySubjectItem

type ModelRefRelation struct {
	FromCode string `db:"from_code" json:"from_code"`
	ToCode   string `db:"to_code" json:"to_code"`
}

type PhysicalModelExpand struct {
	Id                 string        `db:"id" json:"id"`
	ExpandTableName    string        `db:"expand_table_name" json:"expand_table_name"`
	PhysicalModelCode  string        `db:"physical_model_code" json:"physical_model_code"`
	IsBind             int           `db:"is_bind" json:"is_bind"`
	CreatedOn          db.NullTime   `json:"created_on" db:"created_on"`
	ModifiedOn         db.NullTime   `json:"modified_on" db:"modified_on"`
	CreatedBy          string        `json:"created_by" db:"created_by"`
	ModifiedBy         string        `json:"modified_by" db:"modified_by"`
	SourceProjectLevel int           `json:"source_project_level" db:"source_project_level"`
	SourceProject      db.NullString `json:"source_project" db:"source_project"`
}

type MetaData struct {
	Name        string `json:"name"`
	NameCn      string `json:"name_cn"`
	FieldType   string `json:"field_type"`
	Description string `json:"description"`
	Length      int    `json:"length"`
}
type GetExpandTableListReq struct {
}

type GetExpandTableMetaDataReq struct {
	PhysicalModelCode string `form:"physical_model_code" json:"physical_model_code"`
	TenantCode        string `form:"tenant_code"  json:"tenant_code"`
	ExpandTableName   string `form:"expand_table_name" json:"expand_table_name"`
}

type GetExpandTableMetaDataResp struct {
	ModelMetaData       []MetaData          `json:"model_metadata"`
	ExpandTableMetaData []MetaData          `json:"expand_table_metadata"`
	ExpandTableInfo     PhysicalModelExpand `json:"expand_table_info"`
}
type BindExpandTableReq struct {
	PhysicalModelCode string `form:"physical_model_code" json:"physical_model_code"`
	ExpandTableName   string `form:"expand_table_name" json:"expand_table_name"`
}

type UnBindExpandTableReq struct {
	PhysicalModelCode string `form:"physical_model_code" json:"physical_model_code"`
}

type NotifyBrief struct {
	Code     string                  `json:"code"`
	Category modelBase.ModelCategory `json:"category"`
}

type IndicatorCodeMapReq struct {
	Project string `form:"project" json:"project"`
}

type IndicatorCodeMapEntry struct {
	OriIndicatorCode string `db:"ori_indicator_code" json:"ori_indicator_code"`
	NewIndicatorCode string `db:"new_indicator_code" json:"new_indicator_code"`
}

type ModelBriefInfoReq struct {
	Code        string                  `form:"code" json:"code"`
	Category    modelBase.ModelCategory `form:"category" json:"category"`
	TenantCode  string                  `form:"tenant_code" json:"tenant_code"`
	ProjectCode string                  `form:"project_code" json:"project_code"`
}

type ModelBriefInfoRsp struct {
	Code        string                  `form:"code" json:"code"`
	Category    modelBase.ModelCategory `form:"category" json:"category"`
	Name        string                  `form:"name" json:"name"`
	TableName   string                  `form:"table_name" json:"table_name"`
	SubjectId   string                  `form:"subject_id" json:"subject_id"`
	SubjectName string                  `form:"subject_name" json:"subject_name"`
	Fields      []ModelBriefFieldInfo   `form:"fields" json:"fields"`
}

type ModelBriefFieldInfo struct {
	Name        string `form:"name" json:"name"`
	NameCn      string `form:"name_cn" json:"name_cn"`
	Description string `form:"description" json:"description"`
	FieldType   string `form:"field_type" json:"field_type"`
	DimType     string `form:"dim_type" json:"dim_type"`
}

type BusinessCodeModelField struct {
	Code         string `form:"code" json:"code" db:"code"`
	Category     string `form:"category" json:"category" db:"category"`
	ModelName    string `form:"model_name" json:"model_name" db:"model_name"`
	Environment  string `form:"environment" json:"environment" db:"environment"`
	FieldName    string `form:"field_name" json:"field_name" db:"field_name"`
	BusinessCode string `form:"business_code" json:"business_code" db:"business_code"`
}

type PreviewIndicatorModelResponse struct {
	Header []string        `json:"header"`
	Data   [][]interface{} `json:"data"`
	Sql    string          `json:"sql"`
}
