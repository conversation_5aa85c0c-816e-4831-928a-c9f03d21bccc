package proto

import (
	"encoding/json"

	"gitlab.mypaas.com.cn/dmp/gopkg/db"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/project/base"
)

type BatchUpdatePhysicalModelStorageRep struct {
	Items []*base.PhysicalModelStorage `form:"items" json:"items"`
}

type BatchListModelTableNameResp struct {
	ID        string `json:"id" db:"id"`
	Code      string `json:"code" db:"code"`
	TableName string `json:"table_name" db:"table_name"`
	SubjectID string `json:"subject_id" db:"subject_id"`
}

type RedisStorageSlice []interface{}

func (r RedisStorageSlice) MarshalBinary() ([]byte, error) {
	return json.Marshal(r)
}

type ListProjectsResp struct {
	ID                string             `json:"id" db:"id"`
	Name              string             `json:"name" db:"name"`
	Status            string             `json:"status" db:"status"`
	Code              string             `json:"code" db:"code"`
	SpaceCategory     base.SpaceCategory `json:"space_category" db:"space_category"`
	PublicProjectCode string             `json:"public_project_code" db:"public_project_code"`
}

type Subject struct {
	ID             string `json:"id"`
	Name           string `json:"name"`
	BusinessUnitId string `json:"business_unit_id" db:"business_unit_id"`
}

type ListSubjectsResp struct {
	ID             string        `form:"id" json:"id" db:"id"`
	Name           string        `form:"name" json:"name" db:"name"`
	DimAmount      int64         `form:"dim_amount" json:"dim_amount" db:"dim_amount"`
	DwdAmount      int64         `form:"dwd_amount" json:"dwd_amount" db:"dwd_amount"`
	DwsAmount      int64         `form:"dws_amount" json:"dws_amount" db:"dws_amount"`
	AdsAmount      int64         `form:"ads_amount" json:"ads_amount" db:"ads_amount"`
	AdsViewAmount  int64         `form:"ads_view_amount" json:"ads_view_amount" db:"ads_view_amount"`
	AdsTableAmount int64         `form:"ads_table_amount" json:"ads_table_amount" db:"ads_table_amount"`
	MultiDimAmount int64         `form:"multi_dim_amount" json:"multi_dim_amount" db:"multi_dim_amount"`
	ModelAmount    int64         `json:"model_amount"`
	CreatedOn      db.NullTime   `form:"created_on" json:"created_on" db:"created_on"`
	CreatedBy      db.NullString `form:"created_by" json:"created_by" db:"created_by"`
}

type ListBusinessUnitsResp struct {
	Id             string      `db:"id" json:"id"`
	Name           string      `db:"name" json:"name"`
	CreatedOn      db.NullTime `db:"created_on" json:"created_on"`
	CreatedBy      string      `db:"created_by" json:"created_by"`
	ModifiedOn     db.NullTime `db:"modified_on" json:"modified_on"`
	ModifiedBy     string      `db:"modified_by" json:"modified_by"`
	IsDel          int         `db:"is_del" json:"is_del"`
	PersonInCharge string      `db:"person_in_charge" json:"person_in_charge"`
}

type ListSubjectModelTablesResp struct {
	SubjectID   string `form:"subject_id" json:"subject_id" db:"subject_id"`
	Category    string `form:"category" json:"category" db:"category"`
	Environment string `form:"environment" json:"environment" db:"environment"`
	Name        string `form:"name" json:"name" db:"name"`
	Description string `form:"description" json:"description" db:"description"`
	Amount      int64  `form:"amount" json:"amount" db:"amount"`
}

type ListSubjectMultiDimModelsRsp struct {
	SubjectID   string `form:"subject_id" json:"subject_id" db:"subject_id"`
	Environment string `form:"environment" json:"environment" db:"environment"`
	Name        string `form:"name" json:"name" db:"name"`
	Description string `form:"description" json:"description" db:"description"`
	Amount      int64  `form:"amount" json:"amount" db:"amount"`
}

type ListSubjectIndicatorViewModelsRsp struct {
	SubjectID string `form:"subject_id" json:"subject_id" db:"subject_id"`
	Amount    int64  `form:"amount" json:"amount" db:"amount"`
}

type GetOverViewDataRespAll struct {
	ProjectAmount  int64   `json:"project_amount"`
	SubjectAmount  int64   `json:"subject_amount"`
	ModelAmount    int64   `json:"model_amount"`
	TableAmount    int64   `json:"table_amount"`
	DimAmount      int64   `json:"dim_amount"`
	DwdAmount      int64   `json:"dwd_amount"`
	DwsAmount      int64   `json:"dws_amount"`
	AdsAmount      int64   `json:"ads_amount"`
	AdsViewAmount  int64   `json:"ads_view_amount"`
	AdsTableAmount int64   `json:"ads_table_amount"`
	MultiDimAmount int64   `json:"multi_dim_amount"`
	DataSize       float64 `json:"data_size"`
	SizeUnit       string  `json:"size_unit"`
}

type GetOverViewDataRespProject struct {
	ListProjectsResp
	SubjectAmount      int64               `json:"subject_amount"`
	ModelAmount        int64               `json:"model_amount"`
	TableAmount        int64               `json:"table_amount"`
	DimAmount          int64               `json:"dim_amount"`
	DwdAmount          int64               `json:"dwd_amount"`
	DwsAmount          int64               `json:"dws_amount"`
	AdsAmount          int64               `json:"ads_amount"`
	AdsViewAmount      int64               `json:"ads_view_amount"`
	AdsTableAmount     int64               `json:"ads_table_amount"`
	MultiDimAmount     int64               `json:"multi_dim_amount"`
	DataSize           float64             `json:"data_size"`
	SizeUnit           string              `json:"size_unit"`
	Subjects           []*ListSubjectsResp `json:"subjects"`
	BusinessUnitAmount int64               `json:"business_unit_amount"`
	IndicatorAmount    int64               `json:""`
	DatasourceAmount   int64               `json:"datasource_amount"`
	DashboardAmount    int64               `json:"dashboard_amount"`
}

type GetOverViewDataResp struct {
	All      GetOverViewDataRespAll        `json:"all"`
	Projects []*GetOverViewDataRespProject `json:"projects"`
}

type ProjectResource struct {
	ID           string `json:"id" db:"id"`
	ResourceType string `json:"resource_type" db:"resource_type"`
	ProjectCode  string `json:"project_code" db:"project_code"`
	ResourceId   string `json:"resource_id" db:"resource_id"`
	Module       string `json:"module" db:"module"`
	Environment  string `json:"environment" db:"environment"`
}

type TenantDBInfo struct {
	Host       string      `json:"host" db:"host"`
	Port       interface{} `json:"port" db:"port"`
	Database   string      `json:"database" db:"database"`
	User       string      `json:"user" db:"user"`
	Password   string      `json:"password" db:"password"`
	TenantCode string      `json:"tenant_code" db:"tenant_code"`
	StandardDB bool        `json:"standard_db" db:"-"`
}
