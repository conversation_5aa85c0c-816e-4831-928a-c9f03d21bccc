package services

import (
	"context"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/project/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/project/repo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewProjectService))
}

type ProjectService struct {
	repo repo.ProjectRepo
}

func NewProjectService(repo repo.ProjectRepo) (*ProjectService, error) {
	return &ProjectService{
		repo: repo,
	}, nil
}

func accumulateSubjectAmount(subjects []*proto.ListSubjectsResp) (int64, int64, int64, int64, int64, int64) {
	var dimAmount, dwdAmount, dwsAmount, adsTableAmount, adsViewAmount, multiDimAmount int64
	for _, subject := range subjects {
		dimAmount += subject.DimAmount
		dwdAmount += subject.DwdAmount
		dwsAmount += subject.DwsAmount
		adsTableAmount += subject.AdsTableAmount
		adsViewAmount += subject.AdsViewAmount
		multiDimAmount += subject.MultiDimAmount
	}
	return dimAmount, dwdAmount, dwsAmount, adsTableAmount, adsViewAmount, multiDimAmount
}

func (p *ProjectService) GetProjectSubjects(ctx context.Context, project string) ([]*proto.Subject, error) {
	return p.repo.GetProjectSubjects(ctx, project)
}

func (p *ProjectService) ListBusinessUnitsByIds(ctx context.Context, project string, ids []string) ([]*proto.ListBusinessUnitsResp, error) {
	return p.repo.ListBusinessUnitsByIds(ctx, project, ids)
}

// GetProjectOverViewData
func (p *ProjectService) GetProjectOverViewData(ctx context.Context, project *proto.ListProjectsResp) (resp *proto.GetOverViewDataRespProject, err error) {
	// 数据源的数量
	datasourceAccount, err := p.repo.GetDatasourceAccount(ctx, project.Code)
	if err != nil {
		return
	}

	// 报表数量
	dashboardAccount, err := p.repo.GetDashboardAccount(ctx, project.Code)
	if err != nil {
		return
	}

	subjects, err := p.repo.ListSubjects(ctx, project.Code)
	if err != nil {
		return
	}
	businessUnits, err := p.repo.ListBusinessUnits(ctx, project.Code)
	if err != nil {
		return
	}
	subjectMap := make(map[string]*proto.ListSubjectsResp)
	for _, subject := range subjects {
		subjectMap[subject.ID] = subject
	}

	// dim, dwd, dws, ads_table
	subjectTables, err := p.repo.ListSubjectModelTables(ctx, project.Code, entities.ProjectEnvProd)
	if err != nil {
		return
	}
	for _, subjectTable := range subjectTables {
		if _, ok := subjectMap[subjectTable.SubjectID]; ok {
			switch subjectTable.Category {
			case "dim_amount":
				subjectMap[subjectTable.SubjectID].DimAmount = subjectTable.Amount
			case "dwd_amount":
				subjectMap[subjectTable.SubjectID].DwdAmount = subjectTable.Amount
			case "dws_amount":
				subjectMap[subjectTable.SubjectID].DwsAmount = subjectTable.Amount
			}
		}
	}

	// multi_dim
	subjectMultiDimModels, err := p.repo.ListSubjectMultiDimModels(ctx, project.Code, entities.ProjectEnvProd)
	if err != nil {
		return
	}
	for _, subjectMultiDimModel := range subjectMultiDimModels {
		if _, ok := subjectMap[subjectMultiDimModel.SubjectID]; ok {
			subjectMap[subjectMultiDimModel.SubjectID].MultiDimAmount = subjectMultiDimModel.Amount
		}
	}

	// ads_view
	subjectIndicatorViewModels, err := p.repo.ListSubjectIndicatorViewModels(ctx, project.Code, entities.ProjectEnvProd)
	if err != nil {
		return
	}
	for _, subjectIndicatorViewModel := range subjectIndicatorViewModels {
		if _, ok := subjectMap[subjectIndicatorViewModel.SubjectID]; ok {
			subjectMap[subjectIndicatorViewModel.SubjectID].AdsViewAmount = subjectIndicatorViewModel.Amount
		}
	}
	indicatorFieldAccount, err := p.repo.GetIndicatorAccount(ctx, project.Code, entities.ProjectEnvProd)
	if err != nil {
		return
	}

	for _, subject := range subjectMap {
		subject.AdsAmount = subject.AdsViewAmount
	}

	subjectResps := make([]*proto.ListSubjectsResp, 0)
	for _, subjectResp := range subjectMap {
		subjectResps = append(subjectResps, subjectResp)
	}
	dimAmount, dwdAmount, dwsAmount, _, adsViewAmount, multiDimAmount := accumulateSubjectAmount(subjectResps)
	if err != nil {
		return
	}

	tableAmount := dimAmount + dwdAmount + dwsAmount + adsViewAmount
	resp = &proto.GetOverViewDataRespProject{
		ListProjectsResp:   *project,
		SubjectAmount:      int64(len(subjects)),
		BusinessUnitAmount: int64(len(businessUnits)),
		TableAmount:        tableAmount,
		MultiDimAmount:     multiDimAmount,
		IndicatorAmount:    indicatorFieldAccount,
		DashboardAmount:    dashboardAccount,
		DatasourceAmount:   datasourceAccount,
	}
	return
}

func (ac *ProjectService) GetProject(ctx context.Context, projectCode string) (project *proto.ListProjectsResp, err error) {
	return ac.repo.GetProjectByCode(ctx, projectCode)
}
