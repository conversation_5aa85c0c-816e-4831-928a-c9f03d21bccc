package base

import (
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type PhysicalModelStorage struct {
	PhysicalModelID string  `json:"physical_model_id" db:"physical_model_id"`
	TableName       string  `json:"table_name" db:"table_name"`
	DataSize        float64 `json:"data_size" db:"data_size"`

	CreatedBy  db.NullString `json:"created_by" db:"created_by"`
	CreatedOn  db.NullTime   `json:"created_on" db:"created_on"`
	ModifiedBy db.NullString `json:"modified_by" db:"modified_by"`
	ModifiedOn db.NullTime   `json:"modified_on" db:"modified_on"`
}

type SpaceCategory int

const (
	PrivateSpaceCategory SpaceCategory = 1 //私密空间
	PublicSpaceCategory  SpaceCategory = 2 //公共空间
)