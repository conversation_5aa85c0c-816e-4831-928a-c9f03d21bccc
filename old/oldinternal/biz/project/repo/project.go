package repo

import (
	"context"
	"database/sql"

	"github.com/jmoiron/sqlx"
	commonUtils "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"

	"github.com/defval/inject/v2"
	"github.com/pkg/errors"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	baseStore "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/base/store"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/project/proto"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewDataWorkStore, inject.As(new(ProjectRepo))))
}

type ProjectRepo interface {
	ListSubjects(ctx context.Context, project string) ([]*proto.ListSubjectsResp, error)
	ListBusinessUnits(ctx context.Context, project string) ([]*proto.ListBusinessUnitsResp, error)
	ListBusinessUnitsByIds(ctx context.Context, project string, subjectIds []string) ([]*proto.ListBusinessUnitsResp, error)
	GetProjectSubjects(ctx context.Context, project string) ([]*proto.Subject, error)
	GetIndicatorAccount(ctx context.Context, project string, env entities.ProjectEnvType) (int64, error)
	GetDatasourceAccount(ctx context.Context, project string) (int64, error)
	GetDashboardAccount(ctx context.Context, project string) (int64, error)
	ListSubjectModelTables(ctx context.Context, project string, env entities.ProjectEnvType) ([]*proto.ListSubjectModelTablesResp, error)
	ListSubjectMultiDimModels(ctx context.Context, project string, env entities.ProjectEnvType) ([]*proto.ListSubjectMultiDimModelsRsp, error)
	ListSubjectIndicatorViewModels(ctx context.Context, project string, env entities.ProjectEnvType) ([]*proto.ListSubjectIndicatorViewModelsRsp, error)
	GetProjectByCode(ctx context.Context, project string) (message *proto.ListProjectsResp, err error)
}

type ProjectStore struct {
	*baseStore.Repo
}

func NewDataWorkStore(saas db.SaaS) *ProjectStore {
	return &ProjectStore{
		Repo: baseStore.NewRepo(saas),
	}
}

// GetTx
func (s *ProjectStore) GetTx(ctx context.Context, project string) (tx *sql.Tx, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	tx, err = sess.Begin()
	return
}

// ListSubjects
func (s *ProjectStore) ListSubjects(ctx context.Context, project string) ([]*proto.ListSubjectsResp, error) {
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	querySql := "SELECT id,name,created_on,created_by FROM dap_m_subject where level = 1"
	var resp []*proto.ListSubjectsResp
	args := map[string]interface{}{}
	query, queryArgs, err := sqlx.Named(querySql, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	err = sqlStore.SelectContext(ctx, &resp, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "failed to ListSubjects")
	}
	return resp, nil
}

func (s *ProjectStore) ListBusinessUnitsByIds(ctx context.Context, project string, ids []string) ([]*proto.ListBusinessUnitsResp, error) {
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	querySql := "SELECT id,name,created_on,created_by FROM dap_m_business_unit where id in (:ids)"
	var resp = make([]*proto.ListBusinessUnitsResp, 0)
	if len(ids) < 1 {
		return resp, nil
	}
	args := map[string]interface{}{"ids": ids}
	query, queryArgs, err := sqlx.Named(querySql, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	err = sqlStore.SelectContext(ctx, &resp, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "failed to ListBusinessUnitsByIds")
	}
	return resp, nil
}

func (s *ProjectStore) GetProjectSubjects(ctx context.Context, project string) ([]*proto.Subject, error) {
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	querySql := "SELECT id,name,business_unit_id FROM dap_m_subject"
	var resp []*proto.Subject
	args := map[string]interface{}{}
	query, queryArgs, err := sqlx.Named(querySql, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	err = sqlStore.SelectContext(ctx, &resp, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "failed to ListSubjects")
	}
	return resp, nil
}

// ListSubjectModelTables
func (s *ProjectStore) ListSubjectModelTables(ctx context.Context, project string, env entities.ProjectEnvType) ([]*proto.ListSubjectModelTablesResp, error) {
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	querySql := "SELECT pm.subject_id,CONCAT(pm.category, '_amount') as category," +
		"count(*) AS amount FROM dap_m_physical_model pm INNER JOIN dap_m_subject s ON pm.subject_id = s.id WHERE " +
		"pm.environment = :environment and pm.is_del = 0 GROUP BY pm.subject_id, pm.category"
	var resp []*proto.ListSubjectModelTablesResp
	args := map[string]interface{}{"environment": env}
	query, queryArgs, err := sqlx.Named(querySql, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	err = sqlStore.SelectContext(ctx, &resp, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "failed to ListSubjectModelTables")
	}
	return resp, nil
}

// ListSubjectMultiDimModels
func (s *ProjectStore) ListSubjectMultiDimModels(ctx context.Context, project string, env entities.ProjectEnvType) ([]*proto.ListSubjectMultiDimModelsRsp, error) {
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	querySql := "SELECT subject_id,count(*) AS amount FROM dap_m_multi_dim_model WHERE environment = :environment GROUP BY subject_id"
	var resp []*proto.ListSubjectMultiDimModelsRsp
	args := map[string]interface{}{"environment": env}
	query, queryArgs, err := sqlx.Named(querySql, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	err = sqlStore.SelectContext(ctx, &resp, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "failed to ListSubjectMultiDimModels")
	}
	return resp, nil
}

func (s *ProjectStore) ListSubjectIndicatorViewModels(ctx context.Context, project string, env entities.ProjectEnvType) ([]*proto.ListSubjectIndicatorViewModelsRsp, error) {
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	querySql := "SELECT subject_id,count(*) AS amount FROM dap_m_indicator WHERE environment = :environment GROUP BY subject_id"
	var resp []*proto.ListSubjectIndicatorViewModelsRsp
	args := map[string]interface{}{"environment": env}
	query, queryArgs, err := sqlx.Named(querySql, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	err = sqlStore.SelectContext(ctx, &resp, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "failed to ListSubjectIndicatorViewModels")
	}
	return resp, nil
}

func (s *ProjectStore) GetProjectByCode(ctx context.Context, project string) (message *proto.ListProjectsResp, err error) {
	sess := s.GetDB()
	if err != nil {
		return message, err
	}
	args := map[string]interface{}{
		"project": project,
	}

	selectSQL := `SELECT id,code,name,space_category FROM dap_p_project_main WHERE code = :project `
	// 查询数据
	var result proto.ListProjectsResp
	if err = commonUtils.GetResp(ctx, sess, &result, selectSQL, args); err != nil {
		return message, err
	}
	return &result, err
}

func (s *ProjectStore) GetAllProject(ctx context.Context) (projects []*proto.ListProjectsResp, err error) {
	if sess := s.GetDB(); nil != sess {
		sqlText := "select id,name,status,code,space_category,public_project_code  from dap_p_project_main order by created_on"
		err = sess.SelectContext(ctx, &projects, sqlText)
	} else {
		err = errors.New("GetDB is empty!")
	}

	return
}

func (s *ProjectStore) GetProjectDevModelResource(ctx context.Context) (resources []*proto.ProjectResource, err error) {
	{
		if sess := s.GetDB(); nil != sess {
			sqlText := "SELECT R.id,R.resource_type,pr.resource_id,PR.project_code,pr.module,pr.environment from dap_p_project_main P  join dap_p_project_resources PR on P.code= PR.project_code" +
				" join dap_p_resources R on PR.resource_id = R.id where R.resource_type = 'Presto' and pr.module = '数据建模' and pr.environment = '开发' and pr.tag = '离线'"
			err = sess.SelectContext(ctx, &resources, sqlText)
		} else {
			err = errors.New("GetDB is empty!")
		}

		return
	}
}

func (s *ProjectStore) GetStandardProject(ctx context.Context) (*proto.ListProjectsResp, error) {
	if sess := s.GetDB(); sess != nil {
		project := proto.ListProjectsResp{}

		sqlText := `select id,name,status,code from dap_p_project_main where space_category = 2 and status = 1 order by created_on limit 1;`
		err := sess.GetContext(ctx, &project, sqlText)

		return &project, err
	}

	return nil, nil
}

func (s *ProjectStore) ListBusinessUnits(ctx context.Context, project string) ([]*proto.ListBusinessUnitsResp, error) {
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	querySql := "SELECT id,name,created_on,created_by FROM dap_m_business_unit"
	var resp []*proto.ListBusinessUnitsResp
	args := map[string]interface{}{}
	query, queryArgs, err := sqlx.Named(querySql, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	err = sqlStore.SelectContext(ctx, &resp, query, queryArgs...)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "failed to ListSubjects")
	}
	return resp, nil
}

func (s *ProjectStore) GetIndicatorAccount(ctx context.Context, project string, env entities.ProjectEnvType) (int64, error) {
	var account int64 = 0
	// var indicatorFieldAccount int64 = 0
	// var physicalModelFieldAccount int64 = 0
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return account, err
	}

	args := map[string]interface{}{
		// "environment": env,
	}

	selectSQL := `SELECT count(1) FROM dap_m_indicator_dict_field`
	// 查询数据
	if err = commonUtils.GetResp(ctx, sqlStore, &account, selectSQL, args); err != nil {
		return account, err
	}

	return account, nil
}

func (s *ProjectStore) GetDatasourceAccount(ctx context.Context, project string) (int64, error) {
	var account int64 = 0
	// var indicatorFieldAccount int64 = 0
	// var physicalModelFieldAccount int64 = 0
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return account, err
	}

	args := map[string]interface{}{
		// "environment": env,
	}

	selectSQL := `select count(1) from dap_m_data_source where is_buildin = 0`

	// 查询数据
	if err = commonUtils.GetResp(ctx, sqlStore, &account, selectSQL, args); err != nil {
		return account, err
	}

	return account, nil
}

func (s *ProjectStore) GetDashboardAccount(ctx context.Context, project string) (int64, error) {
	var account int64 = 0
	// var indicatorFieldAccount int64 = 0
	// var physicalModelFieldAccount int64 = 0
	sqlStore, err := s.GetProjDB(ctx, project)
	if err != nil {
		return account, err
	}

	args := map[string]interface{}{
		// "environment": env,
	}

	selectSQL := "SELECT  COUNT(*) FROM `dap_bi_dashboard` WHERE type = 'FILE'"
	// 查询数据
	if err = commonUtils.GetResp(ctx, sqlStore, &account, selectSQL, args); err != nil {
		return account, err
	}

	return account, nil
}
