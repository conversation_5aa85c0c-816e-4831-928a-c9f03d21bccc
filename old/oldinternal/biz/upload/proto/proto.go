package proto

type GenPreSignedPutObjectURLReq struct {
	ObjectKey string `form:"object_key" json:"object_key" binding:"required"`
}

type GenPreSignedPutObjectURLResp struct {
	SignedUrl string            `json:"signed_url"`
	FormData  map[string]string `json:"form_data"`
}

type GenPreSignedGetObjectURLReq struct {
	OSSUrl            string `form:"oss_url" json:"oss_url" binding:"required"`
	IsNewFileTransfer int    `json:"is_new_file_transfer" form:"is_new_file_transfer"`
	IsOp              int    `json:"is_op" form:"is_op"`
}
