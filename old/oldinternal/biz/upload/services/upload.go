package services

import (
	"time"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	fileProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/file"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/upload/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"
	"gitlab.mypaas.com.cn/dmp/gopkg/minio"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewUploadService))
}

const SignedExpireDuration = 9 * time.Hour

type UploadService struct {
	ossClient minio.ObjectStoreClient
}

func NewUploadService(ossClient minio.ObjectStoreClient) *UploadService {
	return &UploadService{
		ossClient: ossClient,
	}
}

func (s *UploadService) GenPreSignedPutObjectURL(req proto.GenPreSignedPutObjectURLReq) (resp proto.GenPreSignedPutObjectURLResp, err error) {
	urlObj, formData, err1 := s.ossClient.PreSignedPostObject(req.ObjectKey, SignedExpireDuration)
	if err1 != nil {
		err = utils.NewUserErrorWrapf(err1, "签名Url失败.")
		return
	}
	resp.SignedUrl = urlObj.String()
	resp.FormData = formData
	return
}

func (s *UploadService) GenPreSignedGetObjectURL(req proto.GenPreSignedGetObjectURLReq) (resp proto.GenPreSignedPutObjectURLResp, err error) {
	objectKey, err := fileProto.GetObjectKeyFromFileUrl(req.OSSUrl)
	if err != nil {
		return
	}
	urlObj, err1 := s.ossClient.PreSignedGetObject(objectKey, SignedExpireDuration)
	if err1 != nil {
		err = utils.NewUserErrorWrapf(err1, "提取对象key失败.")
		return
	}
	resp.SignedUrl = urlObj.String()
	//返回oss url的话必须调用这个接口来转换一下,看是否要转换成后端服务下载地址
	resp.SignedUrl = fileProto.GetNewDownloadUrl(resp.SignedUrl)
	return
}
