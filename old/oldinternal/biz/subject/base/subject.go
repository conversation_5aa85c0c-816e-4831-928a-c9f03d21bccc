package base

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/model_distribute"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type Subject struct {
	ID                 string                       `json:"id" db:"id"`
	Name               string                       `json:"name" db:"name"`
	Description        db.NullString                `json:"description,omitempty" db:"description"`
	CreatedOn          db.NullTime                  `json:"created_on" db:"created_on"`
	ModifiedOn         db.NullTime                  `json:"modified_on" db:"modified_on"`
	CreatedBy          db.NullString                `json:"created_by" db:"created_by"`
	ModifiedBy         db.NullString                `json:"modified_by" db:"modified_by"`
	SourceProjectLevel model_distribute.ProjectType `db:"source_project_level"`
	SourceProject      db.NullString                `db:"source_project"`
}
