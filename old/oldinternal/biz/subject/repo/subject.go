package repo

import (
	"context"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/subject/base"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	baseStore "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/base/store"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewSubjectStore, inject.As(new(SubjectRepo))))
}

type SubjectRepo interface {
	GetSubjectById(ctx context.Context, project string, subjectId string) (subject *base.Subject, err error)
}

var _ SubjectRepo = (*SubjectStore)(nil)

type SubjectStore struct {
	*baseStore.Repo
}

func NewSubjectStore(saas db.SaaS) *SubjectStore {
	return &SubjectStore{
		Repo: baseStore.NewRepo(saas),
	}
}

func (r *SubjectStore) GetSubjectById(ctx context.Context, project string, subjectId string) (subject *base.Subject, err error) {
	return r.getSubjectById(ctx, project, subjectId)
}

func (r *SubjectStore) getSubjectById(ctx context.Context, project string, subjectId string) (subject *base.Subject, err error) {
	subject = new(base.Subject)
	sess, err := r.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	selectSQL := "select id, `name`, description, created_on, created_by, modified_on, modified_by, source_project_level, source_project from dap_m_subject where id=?"
	err = sess.GetContext(ctx, subject, selectSQL, subjectId)
	return
}
