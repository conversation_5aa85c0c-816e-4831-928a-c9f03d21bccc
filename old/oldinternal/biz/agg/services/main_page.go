package services

import (
	"context"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/agg/proto"
	projectProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/project/proto"
	"runtime/debug"
	"sync"
)

func (s *AggService) GetMainPageAssetStatistics(ctx context.Context, projectCode string) (*proto.GetAssetStatisticsRsp, error) {
	project, err := s.projectService.GetProject(ctx, projectCode)
	if err != nil {
		return nil, err
	}

	var m sync.Mutex
	var wg sync.WaitGroup
	rsp := new(proto.GetAssetStatisticsRsp)

	projects := []*projectProto.ListProjectsResp{project}

	for _, project := range projects {
		wg.Add(1)
		go func(project *projectProto.ListProjectsResp) {
			defer wg.Done()
			defer func() {
				if e := recover(); e != nil {
					debug.PrintStack()
					s.logger.Error(e)
				}
			}()

			// 总览数据
			overViewRsp, err := s.projectService.GetProjectOverViewData(ctx, project)
			if err == nil {
				m.Lock()
				rsp.SizeUnit = overViewRsp.SizeUnit
				rsp.DataSize += overViewRsp.DataSize
				rsp.AdsModelAmount += overViewRsp.AdsAmount
				rsp.MultiDimModelAmount += overViewRsp.MultiDimAmount
				rsp.SubjectAmount += overViewRsp.SubjectAmount
				rsp.TableAmount += overViewRsp.TableAmount
				rsp.IndicatorAmount += overViewRsp.IndicatorAmount
				rsp.BusinessUnitAmount += overViewRsp.BusinessUnitAmount
				rsp.DashboardAmount += overViewRsp.DashboardAmount
				rsp.DatasourceAmount += overViewRsp.DatasourceAmount
				m.Unlock()
			}
		}(project)
	}
	wg.Wait()

	return rsp, nil
}
