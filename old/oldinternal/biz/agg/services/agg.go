package services

import (
	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	projectService "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/project/services"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewAggService))
}

type AggService struct {
	logger         *logger.Logger
	projectService *projectService.ProjectService
}

func NewAggService(
	logger *logger.Logger,
	projectService *projectService.ProjectService,
) *AggService {
	return &AggService{
		logger,
		projectService,
	}
}
