package proto

import "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"

type GetAssetStatisticsRsp struct {
	ProjectAmount       int64   `json:"project_amount"`
	DataSize            float64 `json:"data_size"`
	SizeUnit            string  `json:"size_unit"`
	SubjectAmount       int64   `json:"subject_amount"`
	AdsModelAmount      int64   `json:"ads_model_amount"`
	MultiDimModelAmount int64   `json:"multi_dim_model_amount"`
	IndicatorAmount     int64   `json:"indicator_amount"`
	TagAmount           int64   `json:"tag_amount"`
	TagEntityAmount     int64   `json:"tag_entity_amount"`
	TableAmount         int64   `json:"table_amount"`
	ApiAmount           int64   `json:"api_amount"`
	BusinessUnitAmount  int64   `json:"business_unit_amount"`
	DatasourceAmount    int64   `json:"datasource_amount"`
	DashboardAmount     int64   `json:"dashboard_amount"`
}

type GetModelStatisticsRsp struct {
	SubjectAmount       int64 `json:"subject_amount"`
	DimModelAmount      int64 `json:"dim_model_amount"`
	DwdModelAmount      int64 `json:"dwd_model_amount"`
	MultiDimModelAmount int64 `json:"multi_dim_model_amount"`
	AdsModelAmount      int64 `json:"ads_model_amount"`
}

type DataSource struct {
	Id   string `json:"id"`
	Name string `json:"name"`
	Type string `json:"type"`
}

type App struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type GetDataChainMapRsp struct {
	DataSources []DataSource `json:"data_sources"`
	Apps        []App        `json:"app"`
}

type FlowStatistics struct {
	FlowCount    int `json:"flow_count"`
	RunningCount int `json:"running_count"`
	SuccessCount int `json:"success_count"`
	ErrorCount   int `json:"error_count"`
}

type QualityStatistics struct {
	RuleCount  int `json:"rule_count"`
	TaskCount  int `json:"task_count"`
	PassCount  int `json:"pass_count"`
	ErrorCount int `json:"error_count"`
}

type GetOperationStatusRsp struct {
	FlowStatistics    FlowStatistics    `json:"flow_statistics"`
	QualityStatistics QualityStatistics `json:"quality_statistics"`
}

type GetDataAssetCategoryStatisticsReq struct {
	ProjectCode string `form:"project_code" json:"project_code" binding:"required"`
}

type GetDataAssetCategoryStatisticsItem struct {
	SubjectID       string `json:"subject_id" db:"subject_id"`
	SubjectName     string `json:"subject_name" db:"subject_name"`
	IndicatorAmount int    `json:"indicator_amount" db:"indicator_amount"`
	ModelAmount     int    `json:"model_amount" db:"model_amount"`
}

type GetDataAssetCategoryStatisticsRsp []*GetDataAssetCategoryStatisticsItem

type GetDataAssetModelRelationshipsReq struct {
	ProjectCode string `form:"project_code" json:"project_code" binding:"required"`
}

type GetDataAssetModelRelationshipsRspItem struct {
	ID     string                                        `json:"id"`
	Models []*GetDataAssetModelRelationshipsRspModelItem `json:"models"`
}

type GetDataAssetModelRelationshipsRsp []*GetDataAssetModelRelationshipsRspItem

type GetDataAssetModelRelationshipsRspAggData struct {
	DataSize    float64 `json:"data_size"`
	SizeUnit    string  `json:"size_unit"`
	FieldAmount int     `json:"field_amount"`
	TableAmount int     `json:"table_amount"`
}

type ModelCategory string

const (
	DimModelCate ModelCategory = "dim"
	DwdModelCate ModelCategory = "dwd"
	DwsModelCate ModelCategory = "dws"
	AdsModelCate ModelCategory = "ads" // 应用表，包括应用物理表和应用视图
	MultiDimCate ModelCategory = "multi_dim"
)

type GetDataAssetModelRelationshipsRspModelItem struct {
	ID                 string                                   `json:"id"`
	Code               string                                   `json:"code"`
	Name               string                                   `json:"name"`
	NameCn             string                                   `json:"name_cn"`
	Category           ModelCategory                            `json:"category"`
	AdsCategory        base.AdsCategory                         `json:"ads_category"`
	RelationModelIds   []string                                 `json:"relation_model_ids"`
	RelationModelCodes []string                                 `json:"relation_model_codes"`
	AggData            GetDataAssetModelRelationshipsRspAggData `json:"agg_data"`
}
