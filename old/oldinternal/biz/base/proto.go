package base

import (
	"strings"
)

type QueryBaseRequest struct {
	Keyword       string `form:"keyword" json:"keyword" db:"keyword"`
	EscapeKeyword string `form:"escape_keyword" json:"escape_keyword" db:"escape_keyword"`
	Skip          int    `form:"skip" json:"skip" db:"skip"`
	Page          int    `form:"page" json:"page" db:"page"`
	PageSize      int    `form:"page_size" json:"page_size" db:"page_size"`
	Sorts         string `form:"sorts" json:"sorts" binding:"validateQuerySorts"`
}

type Sort struct {
	Id     string `form:"id" json:"id" db:"id"`
	Method string `form:"method" json:"method" db:"method"`
}

func (q *QueryBaseRequest) GetSkip() int {
	return (q.Page - 1) * q.PageSize
}

func (q *QueryBaseRequest) ValidPage() {
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 20
	}
}

func (q *QueryBaseRequest) GetEscapeKeyword() string {
	return "%" + strings.Replace(q.Keyword, "%", "\\%", -1) + "%"
}

func QueryBaseDefaultRequest() QueryBaseRequest {
	return QueryBaseRequest{
		Page:     1,
		PageSize: 20,
	}
}

func QueryBaseNoPageRequest() QueryBaseRequest {
	return QueryBaseRequest{
		Page:     1,
		PageSize: 100000,
	}
}
