package base

import (
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type ProjectResourceType string

const (
	MaxComputeResourceType    ProjectResourceType = "MaxCompute"
	RDSResourceType           ProjectResourceType = "RDS"
	RDSSaaSResourceType       ProjectResourceType = "RDS_SaaS"
	ADBMySQLResourceType      ProjectResourceType = "ADB_MySQL"
	DremioResourceType        ProjectResourceType = "Dremio"
	PrestoResourceType        ProjectResourceType = "Presto"
	KuduResourceType          ProjectResourceType = "Kudu"
	StarRocksResourceType     ProjectResourceType = "StarRocks"
	TiDB_QueryResourceType    ProjectResourceType = "TiDB_Query"
	StarRocksSaaSResourceType ProjectResourceType = "StarRocks_SaaS"
	DamengSaaSResourceType    ProjectResourceType = "Dameng_SaaS"
	DamengResourceType        ProjectResourceType = "Dameng"
)

type ProjectEnvType string

func (env ProjectEnvType) IsDevelopment() bool {
	return env != ProjectEnvProd
}

func (env ProjectEnvType) GetEnvName() string {
	if env.IsDevelopment() {
		return "dev"
	}
	return "prod"
}

func (env ProjectEnvType) IsProduction() bool {
	return env == ProjectEnvProd
}

const (
	ProjectEnvDev     ProjectEnvType = "开发"
	ProjectEnvProd    ProjectEnvType = "生产"
	ProjectEnvHistory ProjectEnvType = "历史"
)

type IndicatorFieldSource string

const (
	Default IndicatorFieldSource = ""
	Custom  IndicatorFieldSource = "custom"
)

type ProjectResource struct {
	ResourceID          string              `json:"resource_id" db:"resource_id"`
	ResourceType        ProjectResourceType `json:"resource_type" db:"resource_type"`
	ProjectCode         db.NullString       `json:"project_code,omitempty" db:"project_code"`
	ProjectName         db.NullString       `json:"project_name,omitempty" db:"project_name"`
	ResourceProjectName db.NullString       `json:"resource_project_name,omitempty" db:"resource_project_name"`
	Content             []byte              `json:"content" db:"content"`
	ConnConfig          interface{}
	ModifiedOn          db.NullTime `json:"modified_on,omitempty" db:"modified_on"`
}

func (projectResource *ProjectResource) LoadConnectionConfig() error {
	switch projectResource.ResourceType {
	case MaxComputeResourceType:
		projectResource.ConnConfig = &MaxComputeConnConfig{}
	case ADBMySQLResourceType:
		projectResource.ConnConfig = &ADBMySQLConnConfig{}
	case RDSResourceType:
		projectResource.ConnConfig = &MysqlDataSourceConn{}
	case DremioResourceType:
		projectResource.ConnConfig = &DremioConnConfig{}
	case PrestoResourceType:
		projectResource.ConnConfig = &PrestoConnConfig{}
	case KuduResourceType:
		projectResource.ConnConfig = &KuduConnConfig{}
	case StarRocksSaaSResourceType:
		projectResource.ConnConfig = &StarRocksSaaSConfig{}
	case RDSSaaSResourceType:
		projectResource.ConnConfig = &MysqlDataSourceConn{}
	case DamengSaaSResourceType:
		projectResource.ConnConfig = &DamengDataSourceConn{}
	default:
		return fmt.Errorf("未知的项目资源类型: %s", projectResource.ResourceType)
	}
	if err := json.Unmarshal(projectResource.Content, projectResource.ConnConfig); err != nil {
		return errors.Wrap(err, "反序列化连接信息")
	}
	return nil
}

type MaxComputeConnConfig struct {
	ProjectName string `json:"project_name"`
	AccessID    string `json:"access_id"`
	AccessKey   string `json:"access_key"`
	Endpoint    string `json:"endpoint"`
}

type ADBMySQLConnConfig struct {
	AccessKeyID     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
	Host            string `json:"host"`
	Port            string `json:"port"`
	Database        string `json:"database"`
	Version         string `json:"version"`
}

type MysqlDataSourceConn struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Database string `json:"database"`
	User     string `json:"user"`
	Password string `json:"password" sensitive:"true"`
	Code     string `json:"code" env:"-"`
}

type DamengDataSourceConn struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Database string `json:"database"`
	User     string `json:"user"`
	Password string `json:"password" sensitive:"true"`
	Code     string `json:"code" env:"-"`
}

type DremioConnConfig struct {
	Host         string             `json:"host" validate:"required"`
	UserName     string             `json:"user" validate:"required"`
	Password     string             `json:"password" validate:"required" sensitive:"true"`
	ProjectName  string             `json:"project_name" validate:"required"`
	ExtraContent DremioExtraContent `json:"extra_content"`
}

type DremioExtraContent struct {
	AccessKey    string `json:"access_key"`
	AccessSecret string `json:"access_secret" sensitive:"true"`
	Host         string `json:"host"`
	Bucket       string `json:"bucket"`
}

// PrestoConnConfig  resource content的json结构.
type PrestoConnConfig struct {
	Host string `json:"host"`
	Port string `json:"port"`
	User string `json:"user"`
	HDFSConnConfig
}

type HDFSConnConfig struct {
	Host        string `json:"hdfs_host"`
	Port        string `json:"hdfs_port"`
	User        string `json:"hdfs_user"`
	Warehouse   string `json:"hdfs_warehouse"`
	ProjectName string `json:"project_name"`
}

type KuduConnConfig struct {
	Addresses string `json:"addresses"`
}

type SystemParamName string

const (
	BizDate    SystemParamName = "bdp.system.bizdate"
	CycTime    SystemParamName = "bdp.system.cyctime"
	TenantCode SystemParamName = "bdp.system.tenantcode"
	PulsarTime SystemParamName = "bdp.system.pulsartime"
)

type ProjectParam struct {
	Code           string `json:"code" db:"code"`
	Type           string `json:"type" db:"type"`
	ParameterValue string `json:"parameter_value" db:"parameter_value"`
	Description    string `json:"description" db:"description"`
	Modifiable     bool   `json:"modifiable" db:"modifiable"`
}

type StarRocksSaaSConfig struct {
	Host              string `json:"host" db:"host"`
	TenantDBPassword  string `json:"tenant_db_password" db:"tenant_db_password"`
	TenantDBUser      string `json:"tenant_db_user" db:"tenant_db_user"`
	TenantApiBizParam string `json:"tenant_api_biz_param" db:"tenant_api_biz_param"`
	TenantApiSecret   string `json:"tenant_api_secret" db:"tenant_api_secret"`
	TenantDBName      string `json:"tenant_db_name" db:"tenant_db_name"`
	TenantListSql     string `json:"tenant_list_sql" db:"tenant_list_sql"`
	TenantGetType     int    `json:"tenant_get_type" db:"tenant_get_type"`
	Database          string `json:"database" db:"database"`
	Password          string `json:"password" db:"password"`
	User              string `json:"user" db:"user"`
	TenantDBHost      string `json:"tenant_db_host" db:"tenant_db_host"`
	TenantApiHost     string `json:"tenant_api_host" db:"tenant_api_host"`
	TenantDBPort      string `json:"tenant_db_port" db:"tenant_db_port"`
	Port              string `json:"port" db:"port"`
}
