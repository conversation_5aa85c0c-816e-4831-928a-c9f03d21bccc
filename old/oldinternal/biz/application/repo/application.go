package repo

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"

	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/context_helper"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"

	pkg "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/proto"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/base"

	"github.com/defval/inject/v2"
	"github.com/jmoiron/sqlx"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	baseStore "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/base/store"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

func init() {
	global.InjectContext = append(global.InjectContext, inject.Provide(NewApplicationStore, inject.As(new(ApplicationRepo))))
}

type ApplicationRepo interface {
	// 获取Tx，支持外层做事务
	GetTx(ctx context.Context, project string) (tx *sql.Tx, err error)
	// 获取应用列表
	ApplicationList(ctx context.Context, projectCode string, req *proto.ApplicationListReq) (appList []*proto.AppListResp, total int, err error)
	ApplicationListTx(ctx context.Context, tx *sql.Tx, req *proto.ApplicationListReq) (appList []*proto.AppListResp, err error)
	// 添加应用
	ApplicationAdd(ctx context.Context, tx *sql.Tx, req *proto.ApplicationAddReq) (appID string, err error)
	// 删除应用
	ApplicationDelete(ctx context.Context, tx *sql.Tx, appID string) (err error)
	// 更新应用
	ApplicationUpdate(ctx context.Context, projectCode string, req *proto.ApplicationUpdateReq) (err error)
	// 更新密钥
	ApiSecretUpdate(ctx context.Context, tx *sql.Tx, req *proto.ApiSecretUpdateReq) (err error)
	// 更新应用状态
	AppUpdateIsEnable(ctx context.Context, tx *sql.Tx, appID string, isEnable base.AppEnableStatus) (err error)
	//获取应用详情
	ApplicationDetail(ctx context.Context, projectCode, appID string) (appDetail *base.Application, err error)
	// 根据应用名称获取详情
	CheckAppExist(ctx context.Context, projectCode, appID, name string) (app *base.Application, err error)
	// 根据应用ID获取详情
	ApplicationListByIDs(ctx context.Context, projectCode string, appIDs []string, visibleOnly bool) (appList []*proto.AppListResp, err error)
	// 注册应用到kong
	ApplicationRegister(ctx context.Context, tx *sql.Tx, req *proto.ApplicationRegisterReq) (err error)
	//获取所有项目的应用key-secret信息
	GetAllProjectAppSecrets(ctx context.Context) (appSecrets map[string]string, err error)
}

type ApplicationStore struct {
	*baseStore.Repo
}

func NewApplicationStore(saas db.SaaS) *ApplicationStore {
	return &ApplicationStore{
		Repo: baseStore.NewRepo(saas),
	}
}

func getQueryWhereStr(req *proto.ApplicationListReq) (where string, args map[string]interface{}) {
	args = map[string]interface{}{
		"skip": req.GetSkip(),
		"size": req.PageSize,
	}
	var whereSQLs []string
	if req.Keyword != "" {
		kw := req.GetEscapeKeyword()
		args["keyword"] = kw
		whereSQLs = append(whereSQLs, "(name like :keyword or description like :keyword)")
	}
	if req.ApiCode != "" {
		args["api_code"] = req.ApiCode
		whereSQLs = append(whereSQLs, "(id in (select app_id from dap_m_application_api where api_code=:api_code))")
	}
	if req.IDGTE != "" {
		args["id"] = req.IDGTE
		whereSQLs = append(whereSQLs, "(id >= :id)")
	}

	whereSQLs = append(whereSQLs, "`visible` = 1")
	if len(whereSQLs) == 0 {
		return
	}
	where = "where " + strings.Join(whereSQLs, " and ")
	return
}

func (s *ApplicationStore) GetTx(ctx context.Context, project string) (tx *sql.Tx, err error) {
	// GetTx
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	return sess.Begin()
}

func (s *ApplicationStore) applicationListQuery(req *proto.ApplicationListReq) (query string, queryArgs []interface{}, err error) {
	sqlText := "select id,name,developer,`type`,icon,description,is_enable, api_key, api_secret,created_on,modified_on,created_by,created_by from dap_m_application "
	whereStr, args := getQueryWhereStr(req)

	var orderBy string
	// 默认created_on
	if req.Sorts != "" {
		// 适配达梦
		sorts := strings.ReplaceAll(req.Sorts, "\"", "'")
		orderBy = fmt.Sprintf(" order by %s ", sorts)
	} else {
		orderBy = " order by created_on DESC"
	}
	limitSQL := " limit :skip, :size "
	sqlText += whereStr + orderBy + limitSQL

	query, queryArgs, err = sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *ApplicationStore) applicationCountQuery(req *proto.ApplicationListReq) (query string, args map[string]interface{}) {
	query = "select count(id) from dap_m_application "
	countWhereStr, args := getQueryWhereStr(req)
	query += countWhereStr
	return
}

// ApplicationListTx
func (s *ApplicationStore) ApplicationListTx(ctx context.Context, tx *sql.Tx, req *proto.ApplicationListReq) (appList []*proto.AppListResp, err error) {
	query, queryArgs, err := s.applicationListQuery(req)
	if err != nil {
		return
	}
	rows, err := tx.QueryContext(ctx, query, queryArgs...)
	if err != nil {
		return
	}

	appList = make([]*proto.AppListResp, 0)
	for rows.Next() {
		var app proto.AppListResp
		if err = rows.Scan(&app.ID, &app.Name, &app.Developer, &app.Type, &app.Icon, &app.Description, &app.IsEnable,
			&app.AppKey, &app.AppSecret, &app.CreatedOn, &app.ModifiedOn, &app.CreatedBy, &app.ModifiedBy); err != nil {
			return
		}
		appList = append(appList, &app)
	}
	return
}

// ApplicationList
func (s *ApplicationStore) ApplicationList(ctx context.Context, projectCode string, req *proto.ApplicationListReq) (appList []*proto.AppListResp, total int, err error) {
	sess, err := s.GetProjDB(ctx, projectCode)
	if err != nil {
		return
	}
	appList = []*proto.AppListResp{}
	query, queryArgs, err := s.applicationListQuery(req)
	if err != nil {
		return
	}
	if err = sess.SelectContext(ctx, &appList, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}

	// 查询条目数
	sqlTextCount, args := s.applicationCountQuery(req)
	count, err := sess.NamedQueryContext(ctx, sqlTextCount, args)
	if err != nil {
		return
	}
	defer func() {
		errCountClose := count.Close()
		if errCountClose != nil {
			log.Print(errCountClose)
		}
	}()
	for count.Next() {
		err = count.Scan(&total)
		if err != nil {
			return
		}
	}
	return
}

// ApplicationAdd
func (s *ApplicationStore) ApplicationAdd(ctx context.Context, tx *sql.Tx, req *proto.ApplicationAddReq) (appID string, err error) {
	account := context_helper.MustGetSessionAccount(ctx)
	sqlText := "insert into dap_m_application (id,name,description,developer,`type`,icon,`visible`,registered,is_enable,api_key, " +
		"api_secret,created_by,modified_by) values " +
		"(:id,:name,:description,:developer,:type,:icon,:visible,:registered,:isEnable,:apiKey,:apiSecret,:createdBy,:modifiedBy) "

	newAppID := pkg.NewMysqlID()
	args := map[string]interface{}{
		"id":          newAppID,
		"name":        req.Name,
		"type":        base.CustomApp,
		"icon":        req.Icon,
		"visible":     base.EnableVisit,
		"registered":  req.Registered,
		"description": req.Description,
		"developer":   req.Developer,
		"isEnable":    int(req.IsEnable),
		"apiKey":      req.ApiKey,
		"apiSecret":   req.ApiSecret,
		"createdBy":   account,
		"modifiedBy":  account,
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	stmt, err := tx.Prepare(query)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, queryArgs...)
	if err != nil {
		return
	}
	appID = newAppID
	return
}

// ApplicationDelete
func (s *ApplicationStore) ApplicationDelete(ctx context.Context, tx *sql.Tx, appID string) (err error) {
	sqlText := "delete from dap_m_application where id = :id"
	args := map[string]interface{}{
		"id": appID,
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	stmt, err := tx.Prepare(query)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, queryArgs...)
	if err != nil {
		return
	}
	return
}

func (s *ApplicationStore) ApplicationRegister(ctx context.Context, tx *sql.Tx, req *proto.ApplicationRegisterReq) (err error) {
	sqlText := "update dap_m_application set api_key = :api_key,api_secret = :api_secret, registered = 1 where id = :id"
	args := map[string]interface{}{
		"id":         req.ID,
		"api_key":    req.ApiKey,
		"api_secret": req.ApiSecret,
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	stmt, err := tx.Prepare(query)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, queryArgs...)
	if err != nil {
		return
	}
	return
}

// ApplicationUpdate
func (s *ApplicationStore) ApplicationUpdate(ctx context.Context, projectCode string, req *proto.ApplicationUpdateReq) (err error) {
	sess, err := s.GetProjDB(ctx, projectCode)
	if err != nil {
		return
	}
	sqlText := "update dap_m_application set name = :name,description = :description,developer = :developer,icon = :icon " +
		"where id = :id "
	args := map[string]interface{}{
		"id":          req.ID,
		"name":        req.Name,
		"description": req.Description,
		"developer":   req.Developer,
		"icon":        req.Icon,
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	_, err = sess.ExecContext(ctx, query, queryArgs...)
	if err != nil {
		return
	}
	return
}

// ApiSecretUpdate
func (s *ApplicationStore) ApiSecretUpdate(ctx context.Context, tx *sql.Tx, req *proto.ApiSecretUpdateReq) (err error) {
	sqlText := "update dap_m_application set api_secret = :apiSecret where id = :id"
	args := map[string]interface{}{
		"id":        req.ID,
		"apiSecret": req.ApiSecret,
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	stmt, err := tx.Prepare(query)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, queryArgs...)
	if err != nil {
		return
	}
	return
}

// AppUpdateIsEnable
func (s *ApplicationStore) AppUpdateIsEnable(ctx context.Context, tx *sql.Tx, appID string, isEnable base.AppEnableStatus) (err error) {
	sqlText := "update dap_m_application set is_enable = :isEnable where id = :id"
	args := map[string]interface{}{
		"id":       appID,
		"isEnable": int(isEnable),
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	stmt, err := tx.Prepare(query)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, queryArgs...)
	if err != nil {
		return
	}
	return

}

// ApplicationDetail
func (s *ApplicationStore) ApplicationDetail(ctx context.Context, projectCode, appID string) (appDetail *base.Application, err error) {
	sess, err := s.GetProjDB(ctx, projectCode)
	if err != nil {
		return
	}
	var obj base.Application
	sqlText := "select id,name,developer,`type`,icon,`visible`,registered,description,is_enable,api_key,api_secret, " +
		"created_on,created_by,modified_on,modified_on from dap_m_application where id = :appID limit 1 "
	args := map[string]interface{}{"appID": appID}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.GetContext(ctx, &obj, query, queryArgs...); err != nil {
		if err == sql.ErrNoRows {
			err = utils.NewUserError("应用%s不存在", appID)
			return
		}
		return nil, err
	}
	return &obj, nil
}

// CheckAppExist
func (s *ApplicationStore) CheckAppExist(ctx context.Context, projectCode, appID, name string) (app *base.Application, err error) {
	sess, err := s.GetProjDB(ctx, projectCode)
	if err != nil {
		return
	}

	var obj base.Application
	sqlQuery := "select id,name,developer,description,is_enable,api_key,api_secret, " +
		"created_on,created_by,modified_on,modified_on from dap_m_application "
	var whereSQLs []string
	args := make(map[string]interface{})
	if appID != "" {
		args["appID"] = appID
		whereSQLs = append(whereSQLs, " id != :appID ")
	}
	if name != "" {
		args["name"] = name
		whereSQLs = append(whereSQLs, " name = :name ")
	}
	if len(whereSQLs) != 0 {
		whereStr := "where " + strings.Join(whereSQLs, " and ")
		sqlQuery += whereStr
	}
	limitStr := "limit 1 "
	sqlQuery += limitStr
	query, queryArgs, err := sqlx.Named(sqlQuery, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.GetContext(ctx, &obj, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}
	return &obj, nil
}

// ApplicationListByIDs
func (s *ApplicationStore) ApplicationListByIDs(ctx context.Context, projectCode string, appIDs []string, visibleOnly bool) (appList []*proto.AppListResp, err error) {
	sess, err := s.GetProjDB(ctx, projectCode)
	if err != nil {
		return
	}
	appList = []*proto.AppListResp{}
	sqlText := "select id,name,developer,`type`,`visible`,description,is_enable,created_on,modified_on,created_by,created_by " +
		"from dap_m_application "

	var whereSQLs []string
	args := make(map[string]interface{})
	if len(appIDs) != 0 {
		args["appIDs"] = appIDs
		whereSQLs = append(whereSQLs, " id in (:appIDs) ")
	}
	if visibleOnly {
		args["visible"] = base.EnableVisit
		whereSQLs = append(whereSQLs, " `visible` = :visible")
	}
	if len(whereSQLs) != 0 {
		whereStr := "where " + strings.Join(whereSQLs, " and ")
		sqlText += whereStr
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.SelectContext(ctx, &appList, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}
	return
}

func (s *ApplicationStore) GetAllProjectAppSecrets(ctx context.Context) (appSecrets map[string]string, err error) {
	appSecrets = map[string]string{}
	sess := s.GetDB()
	sqlText, err := s.getAllProjectAppSecretsSql(ctx, sess)
	if err != nil {
		return
	}
	type row struct {
		ApiKey    string `db:"api_key"`
		ApiSecret string `db:"api_secret"`
	}
	var rows []row
	err = sess.SelectContext(ctx, &rows, sqlText)
	if err != nil {
		return
	}
	appSecrets = lo.SliceToMap(rows, func(item row) (string, string) {
		return item.ApiKey, item.ApiSecret
	})

	return
}

func (s *ApplicationStore) getAllProjectAppSecretsSql(ctx context.Context, sess *sqlx.DB) (sqlText string, err error) {
	var projects []string
	sqlText = "select code from dap_p_project_main where status = 1"
	err = sess.SelectContext(ctx, &projects, sqlText)
	if err != nil {
		return
	}
	dbs := lo.FilterMap(projects, func(item string, _ int) (string, bool) {
		return global.AppConfig.GetBizDatabase(item), true
	})
	if len(dbs) == 0 {
		return
	}
	sqlText = "select api_key, api_secret from (%s) a"
	sqlSlices := lo.Map(dbs, func(db string, _ int) string {
		return fmt.Sprintf("select api_key, api_secret from `%s`.`dap_m_application` where is_enable=1 \n", db)
	})
	sqlText = fmt.Sprintf(sqlText, strings.Join(sqlSlices, " union all "))
	return
}
