package proto

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/base"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type ApplicationAuthEntity struct {
	ID        string               `json:"id" db:"id"`
	Name      string               `json:"name" db:"name"`
	Developer string               `json:"developer" db:"developer"`
	IsEnable  base.AppEnableStatus `json:"is_enable" db:"is_enable"`
	ApiKey    db.NullString        `json:"api_key" db:"api_key"`
	ApiSecret db.NullString        `json:"api_secret" db:"api_secret"`

	CreatedBy string `json:"created_by" db:"created_by"`
	CreatedOn string `json:"created_on" db:"created_on"`
}

type ApplicationListReq struct {
	Sorts   string `form:"sorts" json:"sorts" binding:"validateQuerySorts=type created_on name id"`
	ApiCode string `form:"api_code" json:"api_code"`
	IDGTE   string
	cBase.QueryBaseRequest
}

type ApplicationAddReq struct {
	Name        string `form:"name" json:"name" db:"name" binding:"required"`
	Developer   string `form:"developer" json:"developer" db:"developer" binding:"required"`
	Description string `form:"description" json:"description" db:"description"`

	IsEnable   base.AppEnableStatus     `json:"is_enable" db:"is_enable"`
	Registered base.AppRegisteredStatus `json:"registered" db:"registered"`
	ApiKey     string                   `json:"api_key" db:"api_key"`
	ApiSecret  string                   `json:"api_secret" db:"api_secret"`
	Icon       string                   `form:"icon" json:"icon" db:"icon"`
}

type ApplicationRegisterReq struct {
	ID        string `json:"id" db:"id"`
	ApiKey    string `json:"api_key" db:"api_key"`
	ApiSecret string `json:"api_secret" db:"api_secret"`
}

type ApplicationUpdateReq struct {
	ID          string `form:"id" json:"id" db:"id" binding:"required"`
	Name        string `form:"name" json:"name" db:"name" binding:"required"`
	Developer   string `form:"developer" json:"developer" db:"developer" binding:"required"`
	Description string `form:"description" json:"description" db:"description"`
	Icon        string `form:"icon" json:"icon" db:"icon"`
}

type AppListResp struct {
	ID          string                `json:"id" db:"id"`
	Name        string                `json:"name" db:"name"`
	Developer   string                `json:"developer" db:"developer"`
	Icon        string                `json:"icon" db:"icon"`
	Type        base.AppType          `json:"type" db:"type"`
	Description db.NullString         `form:"description" json:"description"`
	IsEnable    base.AppEnableStatus  `json:"is_enable" db:"is_enable"`
	Visible     base.AppVisibleStatus `json:"visible" db:"visible"`
	AppKey      string                `json:"api_key" db:"api_key"`
	AppSecret   string                `json:"api_secret" db:"api_secret"`

	CreatedBy  db.NullString `json:"created_by" db:"created_by"`
	CreatedOn  db.NullTime   `json:"created_on" db:"created_on"`
	ModifiedBy db.NullString `json:"modified_by" db:"modified_by"`
	ModifiedOn db.NullTime   `json:"modified_on" db:"modified_on"`
}

type PaginationAppListResp struct {
	Items []*AppListResp `json:"items"`
	Total int            `json:"total"`
}

type ApiSecretUpdateReq struct {
	ID        string `form:"id" json:"id" db:"id" binding:"required"`
	ApiSecret string `json:"api_secret" db:"api_secret"`
}
