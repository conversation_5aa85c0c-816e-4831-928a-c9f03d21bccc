package base

import "gitlab.mypaas.com.cn/dmp/gopkg/db"

type AppEnableStatus int

const (
	Enable  AppEnableStatus = 1
	Disable AppEnableStatus = 0
)

type AppType string

const (
	CdpApp    AppType = "cdp"
	DmpApp    AppType = "dmp"
	CustomApp AppType = "custom"
)

type AppVisibleStatus int

const (
	EnableVisit  AppVisibleStatus = 1
	DisableVisit AppVisibleStatus = 0
)

type AppRegisteredStatus int

const (
	Registered    AppRegisteredStatus = 1
	NotRegistered AppRegisteredStatus = 0
)

type Application struct {
	ID        string `json:"id" db:"id"`
	Name      string `json:"name" db:"name"`
	Developer string `json:"developer" db:"developer"`
	// 区分应用类型
	// legacy表示旧的不区分租户的全局应用
	// cdp/dmp对应默认的cdp和dmp系统应用
	// custom表示用户创建的自定义应用
	Type    AppType          `json:"type" db:"type"`
	Icon    string           `json:"icon" db:"icon"`
	Visible AppVisibleStatus `json:"visible" db:"visible"` // 表示用户是否可见
	// 是否已经注册到空, 默认创建的都是默认注册到kong的, 通过数据升级创建的初始未注册到kong
	Registered  AppRegisteredStatus `json:"registered" db:"registered"`
	Description db.NullString       `json:"description" db:"description"`
	IsEnable    AppEnableStatus     `json:"is_enable" db:"is_enable"`
	ApiKey      db.NullString       `json:"api_key" db:"api_key"`
	ApiSecret   db.NullString       `json:"api_secret" db:"api_secret"`

	CreatedBy   db.NullString `json:"created_by" db:"created_by"`
	CreatedOn   db.NullTime   `json:"created_on" db:"created_on"`
	ModifiedBy  db.NullString `json:"modified_by" db:"modified_by"`
	ModifiedOn  db.NullTime   `json:"modified_on" db:"modified_on"`
	OpenApiHost string        `json:"openapi_host"`
}

func IsSystemApp(appType AppType) bool {
	if appType == CustomApp {
		return false
	}
	return true
}
