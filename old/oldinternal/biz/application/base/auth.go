package base

import (
	gkgStr "gitlab.mypaas.com.cn/dmp/gopkg/str"
)

const (
	KeyLen    int = 16
	SecretLen int = 32
)

type AuthManager struct{}

func NewAuthManager() *AuthManager {
	return &AuthManager{}
}

// GenApiKey
func (a *AuthManager) GenApiKey() string {
	return gkgStr.RandDigit(KeyLen)
}

// GenApiSecret
func (a *AuthManager) GenApiSecret() string {
	return gkgStr.RandString(SecretLen)
}

// GenKeyPair
func (a *AuthManager) GenKeyPair() (string, string) {
	return a.GenApiKey(), a.GenApiSecret()
}
