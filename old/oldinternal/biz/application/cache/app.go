package cache

import (
	"context"
	"github.com/defval/inject/v2"
	"github.com/go-redis/redis/v8"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"
	"time"
)

func init() {
	global.InjectContext = append(global.InjectContext, inject.Provide(NewAppCache))
}

type AppCache struct {
	r      *cache.Redis
	logger *logger.Logger
}

func NewAppCache() *AppCache {
	s := &AppCache{}
	global.Container.MustExtract(&s.r)
	global.Container.MustExtract(&s.logger)
	return s
}

const appSecretMapKey = "dap-dimensional-modeling:app_secret_map"
const appSecretMapKeyNew = "dap-dimensional-modeling:app_secret_map_new"

func (s *AppCache) SetAppSecret(ctx context.Context, key string, secret string) {
	err := s.r.Client.HSet(ctx, appSecretMapKey, key, secret).Err()
	if err != nil {
		s.logger.Errorf("缓存应用key[%s]和secret[%s]失败: %s", key, secret, err.Error())
		return
	}
}

func (s *AppCache) DelAppSecret(ctx context.Context, key string) {
	err := s.r.Client.HDel(ctx, appSecretMapKey, key).Err()
	if err != nil {
		s.logger.Errorf("缓存应用key[%s]和secret[%s]失败: %s", key, err.Error())
		return
	}
}

func (s *AppCache) SetAllAppSecret(ctx context.Context, appSecrets map[string]string) {
	err := s.r.Client.Del(ctx, appSecretMapKeyNew).Err()
	if err != nil && err != redis.Nil {
		s.logger.Errorf("删除缓存应用key[%s]失败: %s", appSecretMapKeyNew, err.Error())
	}
	err = s.r.Client.HMSet(ctx, appSecretMapKeyNew, lo.MapEntries(appSecrets,
		func(k1 string, v1 string) (k2 string, v2 interface{}) {
			return k1, v1
		})).Err()
	if err != nil {
		s.logger.Errorf("缓存应用所有key和secret失败: %s", err.Error())
		return
	}
	s.r.Client.Expire(ctx, appSecretMapKeyNew, 7*24*time.Hour)
	if err != nil {
		s.logger.Errorf("expire缓存应用key[%s]失败: %s", appSecretMapKeyNew, err.Error())
	}
	err = s.r.Client.Rename(ctx, appSecretMapKeyNew, appSecretMapKey).Err()
	if err != nil {
		s.logger.Errorf("rename缓存应用key[%s]失败: %s", appSecretMapKeyNew, err.Error())
	}
}

func (s *AppCache) GetAppSecret(ctx context.Context, key string) (secret string, err error) {
	return s.r.Client.HGet(ctx, appSecretMapKey, key).Result()
}

func (s *AppCache) GetAllAppSecret(ctx context.Context) (map[string]string, error) {
	return s.r.Client.HGetAll(ctx, appSecretMapKey).Result()
}
