package services

import (
	"context"
	"database/sql"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/errgroup"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"
	"golang.org/x/sync/singleflight"
	"log"
	"math/rand"
	"time"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"

	pkgFast "gitlab.mypaas.com.cn/dmp/gopkg/bigdata/fast"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/recorder"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/fast"

	"gitlab.mypaas.com.cn/dmp/gopkg/db"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/base"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/proto"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/repo"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewApplicationService))
}

type ApplicationService struct {
	logger      *logger.Logger
	repo        repo.ApplicationRepo
	authManager *base.AuthManager
	recorder    *recorder.AppFastRecorder
	sg          singleflight.Group
	appCache    *cache.AppCache
}

func NewApplicationService(logger *logger.Logger, repo repo.ApplicationRepo,
	recorder *recorder.AppFastRecorder, appCache *cache.AppCache) *ApplicationService {
	appService := &ApplicationService{
		logger:      logger,
		repo:        repo,
		recorder:    recorder,
		appCache:    appCache,
		authManager: base.NewAuthManager(),
	}
	return appService
}

// ApplicationList 获取应用列表
func (a *ApplicationService) ApplicationList(ctx context.Context, projectCode string, req proto.ApplicationListReq) (resp proto.PaginationAppListResp, err error) {
	appList, total, err := a.repo.ApplicationList(ctx, projectCode, &req)
	if err != nil {
		return
	}
	resp.Total = total
	resp.Items = appList
	return
}

// ApplicationAdd 添加应用
func (a *ApplicationService) ApplicationAdd(ctx context.Context, projectCode string, req proto.ApplicationAddReq) (appID string, err error) {
	app, err := a.repo.CheckAppExist(ctx, projectCode, "", req.Name)
	if err != nil {
		return
	}
	if app != nil && app.ID != "" {
		err = utils.NewUserError("应用名称已存在")
		return
	}

	// 获取TX
	tx, err := a.repo.GetTx(ctx, projectCode)
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	newApiKey, newApiSecret := a.authManager.GenKeyPair()
	req.ApiKey = newApiKey
	req.ApiSecret = newApiSecret
	req.Registered = base.Registered
	appID, err = a.repo.ApplicationAdd(ctx, tx, &req)
	if err != nil {
		return
	}
	err = tx.Commit()
	return
}

// ApplicationUpdate 更新应用
func (a *ApplicationService) ApplicationUpdate(ctx context.Context, projectCode string, req proto.ApplicationUpdateReq) (err error) {
	app, err := a.repo.CheckAppExist(ctx, projectCode, req.ID, req.Name)
	if err != nil {
		return
	}
	if app != nil && app.ID != "" {
		err = utils.NewUserError("应用名称已存在")
		return
	}
	app, err = a.repo.ApplicationDetail(ctx, projectCode, req.ID)
	if err != nil {
		return err
	}
	err = a.repo.ApplicationUpdate(ctx, projectCode, &req)
	return
}

// ApplicationDetail 获取应用详情
func (a *ApplicationService) ApplicationDetail(ctx context.Context, project, appID string) (appDetail *base.Application, err error) {
	appDetail, err = a.repo.ApplicationDetail(ctx, project, appID)
	if err != nil {
		return
	}
	appDetail.OpenApiHost = global.AppConfig.Domain.LocalHost
	return
}

// ApplicationAuthDetail 获取应用授权详情（废弃，使用Detail接口替代）
func (a *ApplicationService) ApplicationAuthDetail(ctx context.Context, project, appID string) (authDetail *proto.ApplicationAuthEntity, err error) {
	appDetail, err := a.repo.ApplicationDetail(ctx, project, appID)
	if err != nil {
		return
	}
	authDetail = &proto.ApplicationAuthEntity{
		ID:        appDetail.ID,
		Name:      appDetail.Name,
		Developer: appDetail.Developer,
		IsEnable:  appDetail.IsEnable,
		ApiKey:    appDetail.ApiKey,
		ApiSecret: appDetail.ApiSecret,
		CreatedOn: appDetail.CreatedOn.Time.Format(db.TimeFormat),
		CreatedBy: appDetail.CreatedBy.String,
	}
	return
}

// ApplicationEnable 启用应用
func (a *ApplicationService) ApplicationEnable(ctx context.Context, projectCode, account string, appID string) (err error) {

	appDetail, err := a.repo.ApplicationDetail(ctx, projectCode, appID)
	if err != nil {
		return
	}
	event := fast.StartEvent(pkgFast.EnableApp, projectCode, account)
	defer func() {
		event.EndEvent(ctx, err)
	}()
	event.RecordBizParams(a.recorder.GenerateEnableAppBizParams(ctx, appDetail.Name, appID))

	if appDetail.IsEnable == base.Enable {
		err = utils.NewUserError("无法重复启用应用")
		return
	}
	if appDetail.Registered == base.NotRegistered {
		// 对于直接在数据库中添加的默认应用, 先进行初始化, 注册到kong并生成api_key和api_secret
		err = a.registerApp(ctx, projectCode, appDetail)
		if err != nil {
			return
		}
	}

	// 获取TX
	tx, err := a.repo.GetTx(ctx, projectCode)
	if err != nil {
		return
	}
	defer func() {
		var abortErr = recover()
		if abortErr != nil {
			err = errors.UserError("出现异常: %v", abortErr)
		}
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	err = a.repo.AppUpdateIsEnable(ctx, tx, appID, base.Enable)
	if err != nil {
		return
	}
	err = tx.Commit()
	if err != nil {
		return
	}
	// 更新缓存
	a.appCache.SetAppSecret(ctx, appDetail.ApiKey.String, appDetail.ApiSecret.String)
	return
}

// ApplicationDisable 禁用应用
func (a *ApplicationService) ApplicationDisable(ctx context.Context, projectCode, account string, appID string) (err error) {
	appDetail, err := a.repo.ApplicationDetail(ctx, projectCode, appID)
	if err != nil {
		return
	}
	event := fast.StartEvent(pkgFast.DisableApp, projectCode, account)
	defer func() {
		event.EndEvent(ctx, err)
	}()
	event.RecordBizParams(a.recorder.GenerateDisableAppBizParams(ctx, appDetail.Name, appID))

	if appDetail.IsEnable == base.Disable {
		err = utils.NewUserError("无法重复禁用应用")
		return
	}

	// 获取TX
	tx, err := a.repo.GetTx(ctx, projectCode)
	if err != nil {
		return
	}
	defer func() {
		var abortErr = recover()
		if abortErr != nil {
			err = errors.UserError("出现异常: %v", abortErr)
		}
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	err = a.repo.AppUpdateIsEnable(ctx, tx, appID, base.Disable)
	if err != nil {
		return
	}
	err = tx.Commit()
	// 更新缓存
	a.appCache.DelAppSecret(ctx, appDetail.ApiKey.String)
	return
}

// ApplicationReset 重置密钥
func (a *ApplicationService) ApplicationReset(ctx context.Context, projectCode string, appID string) (err error) {
	appDetail, err := a.repo.ApplicationDetail(ctx, projectCode, appID)
	if err != nil {
		return
	}

	// 获取TX
	tx, err := a.repo.GetTx(ctx, projectCode)
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	newApiSecret := a.authManager.GenApiSecret()
	updateReq := &proto.ApiSecretUpdateReq{ID: appDetail.ID, ApiSecret: newApiSecret}
	err = a.repo.ApiSecretUpdate(ctx, tx, updateReq)
	if err != nil {
		return
	}
	err = tx.Commit()
	if err != nil {
		return
	}
	// 更新缓存
	if appDetail.IsEnable == base.Enable {
		a.appCache.DelAppSecret(ctx, appDetail.ApiKey.String)
		a.appCache.SetAppSecret(ctx, appDetail.ApiKey.String, appDetail.ApiSecret.String)
	}
	return
}

func (a *ApplicationService) ApplicationDelete(ctx context.Context, projectCode string, appID string) (err error) {
	appDetail, err := a.repo.ApplicationDetail(ctx, projectCode, appID)
	if err != nil {
		return
	}
	if base.IsSystemApp(appDetail.Type) {
		err = utils.NewUserError("系统应用无法删除")
		return
	}

	// 获取TX
	tx, err := a.repo.GetTx(ctx, projectCode)
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	err = a.repo.ApplicationDelete(ctx, tx, appID)
	if err != nil {
		return
	}
	err = tx.Commit()
	if err != nil {
		return
	}
	// 更新缓存
	if appDetail.IsEnable == base.Enable {
		a.appCache.DelAppSecret(ctx, appDetail.ApiKey.String)
	}
	return
}

func (a *ApplicationService) registerApp(ctx context.Context, projectCode string, app *base.Application) (err error) {
	tx, err := a.repo.GetTx(ctx, projectCode)
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	newApiKey, newApiSecret := a.authManager.GenKeyPair()
	app.ApiKey = db.NullString{
		sql.NullString{
			String: newApiKey,
			Valid:  true,
		},
	}
	app.ApiSecret = db.NullString{
		sql.NullString{
			String: newApiSecret,
			Valid:  true,
		},
	}
	req := &proto.ApplicationRegisterReq{
		ID:        app.ID,
		ApiKey:    newApiKey,
		ApiSecret: newApiSecret,
	}
	err = a.repo.ApplicationRegister(ctx, tx, req)
	if err != nil {
		return
	}
	err = tx.Commit()
	if err != nil {
		return
	}

	return
}

func (am *ApplicationService) LoadAppSecret(ctx context.Context) (map[string]string, error) {
	secretMapI, err, _ := am.sg.Do("load_app", func() (interface{}, error) {
		secretMap, innerErr := am.repo.GetAllProjectAppSecrets(ctx)
		if innerErr != nil {
			return nil, errors.InternalWrapf(innerErr, "获取应用失败")
		}
		if len(secretMap) == 0 {
			return nil, errors.Internal("无应用秘钥可以缓存")
		}
		am.appCache.SetAllAppSecret(ctx, secretMap)
		return secretMap, nil
	})
	if err != nil {
		return nil, err
	}
	secretMap := secretMapI.(map[string]string)
	return secretMap, nil
}

func (am *ApplicationService) LoadAppSecretCron() {
	errgroup.WithContext(context.Background()).Go(func(ctx context.Context) error {
		for {
			am.logger.Infof("缓存应用秘钥")
			_, err := am.LoadAppSecret(ctx)
			if err != nil {
				am.logger.Errorf("============缓存应用秘钥失败: %s============", err.Error())
			} else {
				am.logger.Infof("定时缓存应用秘钥成功")
			}
			time.Sleep(time.Hour * time.Duration(rand.Intn(8)+1))
		}
	})
}
