package recorder

import (
	"context"
	"time"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	apiRepo "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/repo"
	fastBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/base"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewAppFastRecorder))
}

type AppFastRecorder struct {
	apiRepo apiRepo.ApiManageRepo
}

func NewAppFastRecorder(apiRepo apiRepo.ApiManageRepo) *AppFastRecorder {
	return &AppFastRecorder{
		apiRepo: apiRepo,
	}
}

func (a *AppFastRecorder) GenerateEnableAppBizParams(ctx context.Context, appName, appId string) fastBase.BizParamsGenerator {
	return func() map[string]interface{} {
		res := map[string]interface{}{}
		res["physical_model_name"] = appName
		res["physical_model_id"] = appId
		res["release_time"] = time.Now().Format("2006-01-02 15:04:05.000000")
		return res
	}
}

func (a *AppFastRecorder) GenerateDisableAppBizParams(ctx context.Context, appName, appId string) fastBase.BizParamsGenerator {
	return func() map[string]interface{} {
		res := map[string]interface{}{}
		res["physical_model_name"] = appName
		res["physical_model_id"] = appId
		res["offline_time"] = time.Now().Format("2006-01-02 15:04:05.000000")
		return res
	}
}
