package proto

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/sqlce"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
)

const BaseCheckItemName = "SQL规范性检查"

type SqlCheckRequest struct {
	global.DialectSettingContent
	InputParams    []*base.InputRequestParam `json:"input_params"`
	RequestParams  []*base.ApiParamBase      `json:"request_params"`
	ResponseParams []*base.ApiParamBase      `json:"response_params"`
}

type AsyncRunSqlRequest struct {
	global.DialectSettingContent
	InputParams    []*base.InputRequestParam `json:"input_params"`
	RequestParams  []*base.ApiParamBase      `json:"request_params"`
	ResponseParams []*base.ApiParamBase      `json:"response_params"`
}

type GetRunSqlStatusRequest struct {
	InstanceId string `json:"instance_id" form:"instance_id"`
}

type SqlCheckItem struct {
	Name   string `json:"name"`
	Status string `json:"status"`
	ErrMsg string `json:"err_msg"`
}

type SqlCheckResponse []SqlCheckItem

type AsyncRunSqlResponse []sqlce.AsyncRunSQLScriptResult

type GetRunSqlStateItem struct {
	Name     string      `json:"name"`
	Status   string      `json:"status"`
	Progress string      `json:"progress"`
	Error    string      `json:"error"`
	Result   interface{} `json:"result"`
}

type GetRunSqlStatusResponse struct {
	Sql            string               `json:"sql"`
	InstanceStatus string               `json:"instance_status"`
	Tasks          []GetRunSqlStateItem `json:"tasks"`
}
