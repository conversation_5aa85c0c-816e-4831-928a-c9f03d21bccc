package proto

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type GroupListApiItem struct {
	Code            string                `form:"code" json:"api_code" db:"code"`
	Name            string                `form:"name" json:"name" db:"api_name"`
	Status          base.ApiStatus        `form:"status" json:"status" db:"status"`
	IsRelationModel int                   `form:"is_relation_model" json:"is_relation_model" db:"is_relation_model"`
	IsEdit          int                   `form:"is_edit" json:"is_edit" db:"is_edit"`
	IsUpdated       base.ApiUpdatedStatus `form:"is_updated" json:"is_updated" db:"is_updated"`
	RateLimit       int64                 `json:"rate_limit" db:"rate_limit"`
	TimeoutLimit    int64                 `json:"timeout_limit" db:"timeout_limit"`
	CreateMode      string                `json:"create_mode" db:"create_mode"`
	AuthApps        []*AuthAppsItem       `form:"auth_apps" json:"auth_apps"`
}

type GroupApiListResp struct {
	Code         string                `form:"code" json:"code" db:"code"`
	Name         string                `form:"name" json:"name" db:"name"`
	GroupID      db.NullString         `form:"group_id" json:"group_id" db:"group_id"`
	Status       base.ApiStatus        `form:"status" json:"status" db:"status"`
	IsUpdated    base.ApiUpdatedStatus `form:"is_updated" json:"is_updated" db:"is_updated"`
	RateLimit    int64                 `json:"rate_limit" db:"rate_limit"`
	TimeoutLimit int64                 `json:"timeout_limit" db:"timeout_limit"`
	CreateMode   string                `json:"create_mode" db:"create_mode"`
}

type GroupApiListWithOpenApiResp struct {
	Id            string `json:"id" db:"id"`
	GroupId       string `json:"group_id" db:"group_id"`
	Name          string `json:"name" db:"name"`
	RequestPath   string `json:"request_path" db:"request_path"`
	RequestMethod string `json:"request_method" db:"request_method"`
}

type GroupListResp struct {
	Id            string              `form:"id" json:"id" db:"id"`
	Name          string              `form:"name" json:"name" db:"name"`
	Description   db.NullString       `form:"description" json:"description" db:"description"`
	IsSystemGroup int                 `form:"is_system_group" json:"is_system_group"`
	SubjectId     string              `form:"subject_id" json:"subject_id" db:"subject_id"`
	Apis          []*GroupListApiItem `form:"apis" json:"apis"`
}

type GroupAddReq struct {
	Name        string `form:"name" json:"name" db:"name" binding:"required"`
	Description string `form:"description" json:"description" db:"description"`
	SubjectId   string `form:"subject_id" json:"subject_id" db:"subject_id"`
}

type ApiGroupList struct {
	SubjectId string `form:"subject_id" json:"subject_id" db:"subject_id"`
}

type GroupUpdateReq struct {
	Id          string `form:"id" json:"id" db:"id"`
	Name        string `form:"name" json:"name" db:"name" binding:"required"`
	Description string `form:"description" json:"description" db:"description"`
}

const (
	NotSystemGroup = 0
	SystemGroup    = 1
)

type IndicatorBrief struct {
	Code      string `form:"code" json:"code" db:"code" binding:"required"`
	TableName string `form:"table_name" json:"table_name" db:"table_name" binding:"required"`
}
