package proto

import (
	"time"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	appBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/base"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/apierr"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type GetParamMapReq struct {
	SourceType base.ApiSource `form:"source_type" json:"source_type" binding:"required"`
}

type ApiBaseResponse struct {
	AppID string `form:"app_id" json:"app_id" db:"api_id"`
}

type UnbindAuthApiReq struct {
	AppID       string `form:"app_id" json:"app_id" db:"api_id" binding:"required"`
	ProjectCode string `form:"project_code" json:"project_code" db:"project_code" binding:"required"`
	ApiCode     string `form:"api_code" json:"api_code" db:"api_code" binding:"required"`
}

type ApplicationAuthApiReq struct {
	AppID       string `form:"app_id" json:"app_id" db:"app_id" binding:"required"`
	ProjectCode string `form:"project_code" json:"project_code" db:"project_code"`
	Sorts       string `form:"sorts" json:"sorts" binding:"validateQuerySorts=created_on name"`
	cBase.QueryBaseRequest
}

type ApiListReq struct {
	GroupID          string `form:"group_id" json:"group_id"`
	StartTime        string `form:"start_time" json:"start_time"`
	EndTime          string `form:"end_time" json:"end_time"`
	Status           string `form:"status" json:"status" db:"status"`
	ExcludeAppBind   string `form:"exclude_app_bind" json:"exclude_app_bind"`
	Sorts            string `form:"sorts" json:"sorts" binding:"validateQuerySorts=created_on code"`
	CodeGTE          string
	IsSystemGroup    int      `form:"is_system_group" json:"is_system_group"`
	GroupType        int      `form:"group_type" json:"group_type"`
	Name             string   `form:"name" json:"name"`
	SubjectId        string   `form:"subject_id" json:"subject_id"`
	BatchSubjectList []string `form:"batch_subject_list" json:"batch_subject_list"` // 在单选subject_id为空时生效
	BatchApiList     []string `form:"batch_api_list" json:"batch_api_list"`         // 指定查询的api列表
	cBase.QueryBaseRequest
	Path string `form:"Path" json:"Path"`
}

type AuthAppsItemResp struct {
	ApiCode     string                  `form:"api_code" json:"api_code" db:"api_code"`
	AppID       string                  `form:"app_id" json:"app_id" db:"app_id"`
	Name        string                  `form:"name" json:"name" db:"name"`
	Description db.NullString           `form:"description" json:"description" db:"description"`
	IsEnable    appBase.AppEnableStatus `form:"is_enable" json:"is_enable" db:"is_enable"`
	AuthTerm    base.ApiAuthTerm        `form:"auth_term" json:"auth_term" db:"auth_term"`
	StartTime   db.NullTime             `form:"start_time" json:"start_time" db:"start_time"`
	EndTime     db.NullTime             `form:"end_time" json:"end_time" db:"end_time"`
	ModifiedBy  string                  `form:"modified_by" json:"modified_by" db:"modified_by"`
	ModifiedOn  db.NullTime             `form:"modified_on" json:"modified_on" db:"modified_on"`
}

type ApiListResp struct {
	Code            string                `form:"code" json:"code" db:"code"`
	Name            string                `form:"name" json:"name" db:"api_name"`
	Description     db.NullString         `form:"description" json:"description" db:"description"`
	IsSystemGroup   int                   `form:"is_system_group" json:"is_system_group"`
	GroupID         string                `form:"group_id" json:"group_id"`
	GroupName       string                `form:"group_name" json:"group_name"`
	SubjectID       string                `form:"subject_id" json:"subject_id"`
	RequestPath     db.NullString         `form:"request_path" json:"request_path" db:"request_path"`
	RateLimit       int64                 `json:"rate_limit" db:"rate_limit"`
	TimeoutLimit    int64                 `json:"timeout_limit" db:"timeout_limit"`
	IsRelationModel int                   `form:"is_relation_model" json:"is_relation_model"`
	IsEdit          int                   `form:"is_edit" json:"is_edit"`
	AuthApps        []*AuthAppsItemResp   `form:"auth_apps" json:"auth_apps"`
	Status          base.ApiStatus        `form:"status" json:"status" db:"status"`
	IsUpdated       base.ApiUpdatedStatus `form:"is_updated" json:"is_updated" db:"is_updated"`
	PublishOn       db.NullTime           `form:"publish_on" json:"publish_on" db:"publish_on"`
	RefTableSet     string                `form:"ref_table_set" json:"ref_table_set" db:"ref_table_set"`
	CreatedBy       db.NullString         `form:"created_by" json:"created_by" db:"created_by"`
	CreatedOn       db.NullTime           `form:"created_on" json:"created_on" db:"created_on"`
	ModifiedBy      db.NullString         `form:"modified_by" json:"modified_by" db:"modified_by"`
	ModifiedOn      db.NullTime           `form:"modified_on" json:"modified_on" db:"modified_on"`
	CreateMode      db.NullString         `form:"create_mode" json:"create_mode" db:"create_mode"`
}

type PaginationApiListResp struct {
	Items []*ApiListResp `json:"items"`
	Total int            `json:"total"`
}

type SubjectWithApiCount struct {
}

type ApiGroupTreeRsp struct {
	Items []*ApiGroupTreeItem `json:"items"`
}

type ApiGroupTreeItem struct {
	Id            string         `form:"id" json:"id" db:"id"`
	Name          string         `form:"name" json:"name" db:"name"`
	Description   db.NullString  `form:"description" json:"description" db:"description"`
	IsSystemGroup int            `form:"is_system_group" json:"is_system_group"`
	SubjectId     string         `form:"subject_id" json:"subject_id" db:"subject_id"`
	Children      []*ApiListResp `form:"children" json:"children" db:"children"`
}

type ApiListDbItem struct {
	Code         string                `form:"code" json:"code" db:"code"`
	Name         string                `form:"name" json:"name" db:"name"`
	Description  db.NullString         `form:"description" json:"description" db:"description"`
	GroupID      string                `form:"group_id" json:"group_id" db:"group_id"`
	GroupName    db.NullString         `form:"group_name" json:"group_name" db:"group_name"`
	SubjectID    db.NullString         `form:"subject_id" json:"subject_id" db:"subject_id"`
	RequestPath  db.NullString         `form:"request_path" json:"request_path" db:"request_path"`
	RateLimit    int64                 `json:"rate_limit" db:"rate_limit"`
	TimeoutLimit int64                 `json:"timeout_limit" db:"timeout_limit"`
	Status       base.ApiStatus        `form:"status" json:"status" db:"status"`
	IsUpdated    base.ApiUpdatedStatus `form:"is_updated" json:"is_updated" db:"is_updated"`
	RefTableSet  db.NullString         `form:"ref_table_set" json:"ref_table_set" db:"ref_table_set"`
	CreatedBy    db.NullString         `form:"created_by" json:"created_by" db:"created_by"`
	CreatedOn    db.NullTime           `form:"created_on" json:"created_on" db:"created_on"`
	ModifiedBy   db.NullString         `form:"modified_by" json:"modified_by" db:"modified_by"`
	ModifiedOn   db.NullTime           `form:"modified_on" json:"modified_on" db:"modified_on"`
	CurPublishOn db.NullTime           `form:"cur_publish_on" json:"cur_publish_on" db:"cur_publish_on"`
	CreateMode   db.NullString         `form:"create_mode" json:"create_mode" db:"create_mode"`
}

type SystemApiListDbItem struct {
	Code            string        `form:"code" json:"code" db:"code"`
	Name            string        `form:"name" json:"name" db:"name"`
	Description     db.NullString `form:"description" json:"description" db:"description"`
	IsRelationModel int           `form:"is_relation_model" json:"is_relation_model" db:"is_relation_model"`
	IsEdit          int           `form:"is_edit" json:"is_edit" db:"is_edit"`
	GroupID         string        `form:"group_id" json:"group_id" db:"group_id"`
	GroupName       string        `form:"group_name" json:"group_name" db:"group_name"`
	RateLimit       int64         `json:"rate_limit" db:"rate_limit"`
	TimeoutLimit    int64         `json:"timeout_limit" db:"timeout_limit"`
	RequestBasePath db.NullString `json:"request_base_path" db:"request_base_path"`
	CreatedBy       db.NullString `form:"created_by" json:"created_by" db:"created_by"`
	CreatedOn       db.NullTime   `form:"created_on" json:"created_on" db:"created_on"`
	ModifiedBy      db.NullString `form:"modified_by" json:"modified_by" db:"modified_by"`
	ModifiedOn      db.NullTime   `form:"modified_on" json:"modified_on" db:"modified_on"`
}

type ApplicationAuthApiItemResp struct {
	AppID         string           `form:"app_id" json:"app_id" db:"app_id"`
	ApiCode       string           `form:"api_code" json:"api_code" db:"api_code"`
	Name          string           `form:"name" json:"name" db:"name"`
	Description   db.NullString    `form:"description" json:"description" db:"description"`
	RequestMethod string           `form:"request_method" json:"request_method" db:"request_method"`
	RequestPath   string           `form:"request_path" json:"request_path" db:"request_path"`
	AuthTerm      base.ApiAuthTerm `form:"auth_term" json:"auth_term" db:"auth_term"`
	StartTime     db.NullTime      `form:"start_time" json:"start_time" db:"start_time"`
	EndTime       db.NullTime      `form:"end_time" json:"end_time" db:"end_time"`
	Status        base.ApiStatus   `form:"status" json:"status" db:"status"`
	CreatedBy     db.NullString    `form:"created_by" json:"created_by" db:"created_by"`
	CreatedOn     time.Time        `form:"created_on" json:"created_on" db:"created_on"`
	CurPublishOn  db.NullTime      `form:"cur_publish_on" json:"cur_publish_on" db:"cur_publish_on"`
}

type ApplicationSystemApiGroupInfo struct {
	AppId         string `form:"app_id" json:"app_id" db:"app_id"`
	ModelCodeList string `form:"model_code_list" json:"model_code_list" db:"model_code_list"`
}

type ApplicationAuthApiResp struct {
	AppID         string                `form:"app_id" json:"app_id" db:"app_id"`
	Project       string                `form:"project" json:"project" db:"project"`
	ApiCode       string                `form:"api_code" json:"api_code" db:"api_code"`
	Name          string                `form:"name" json:"name" db:"name"`
	Description   db.NullString         `form:"description" json:"description" db:"description"`
	RequestMethod string                `form:"request_method" json:"request_method" db:"request_method"`
	RequestPath   string                `form:"request_path" json:"request_path" db:"request_path"`
	AuthTerm      base.ApiAuthTerm      `form:"auth_term" json:"auth_term" db:"auth_term"`
	StartTime     db.NullTime           `form:"start_time" json:"start_time" db:"start_time"`
	EndTime       db.NullTime           `form:"end_time" json:"end_time" db:"end_time"`
	Status        base.DisplayApiStatus `form:"status" json:"status" db:"status"`
	CreatedBy     db.NullString         `form:"created_by" json:"created_by" db:"created_by"`
	CreatedOn     time.Time             `form:"created_on" json:"created_on" db:"created_on"`
	PublishOn     db.NullTime           `form:"publish_on" json:"publish_on" db:"publish_on"`
}

type ListApplicationAuthApiResp struct {
	Item  []ApplicationAuthApiResp `json:"item"`
	Total int                      `json:"total"`
}

type ApiReleaseReq struct {
	ApiCode string `form:"api_code" json:"api_code" db:"api_code" binding:"required"`
}

type ApiRollbackReq struct {
	ApiID string `form:"api_id" json:"api_id" db:"api_id" binding:"required"`
}

type ApiVersionReq struct {
	ApiCode string        `form:"api_code" json:"api_code" db:"api_code" binding:"required"`
	Stage   base.ApiStage `form:"stage" json:"stage" db:"stage"`
	cBase.QueryBaseRequest
}

type ApiVersionResp struct {
	Total int               `json:"total"`
	Items []*base.ApiDetail `json:"items"`
}

type ApiOfflineReq = ApiReleaseReq

type ApiDetailReq struct {
	ApiID   string        `form:"api_id" json:"api_id" db:"api_id"`
	ApiCode string        `form:"api_code" json:"api_code" db:"api_code"`
	Stage   base.ApiStage `form:"stage" json:"stage" db:"stage"`
}

type ApiDetailResponse struct {
	*base.ApiDetail
	*base.ApiMain
}

type ApiReleaseDetailMain struct {
	ApiCode      string                `form:"api_code" json:"api_code" db:"code"`
	Name         string                `form:"name" json:"name" db:"name"`
	Status       base.ApiStatus        `form:"status" json:"status" db:"status"`
	IsUpdated    base.ApiUpdatedStatus `form:"is_updated" json:"is_updated" db:"is_updated"`
	GroupID      db.NullString         `form:"group_id" json:"group_id" db:"group_id"`
	GroupName    db.NullString         `form:"group_name" json:"group_name" db:"group_name"`
	ApiUrl       string                `form:"api_url" json:"api_url"`
	RateLimit    int32                 `form:"rate_limit" json:"rate_limit" db:"rate_limit"`
	TimeoutLimit int32                 `form:"timeout_limit" json:"timeout_limit" db:"timeout_limit"`
}

type ApiReleaseDetailAuthAppsItem struct {
	Rank      int              `form:"rank" json:"rank" db:"rank"`
	AppID     string           `form:"app_id" json:"app_id" db:"app_id"`
	Name      string           `form:"name" json:"name" db:"name"`
	AuthTerm  base.ApiAuthTerm `form:"auth_term" json:"auth_term" db:"auth_term"`
	StartTime db.NullTime      `form:"start_time" json:"start_time" db:"start_time"`
	EndTime   db.NullTime      `form:"end_time" json:"end_time" db:"end_time"`
}

type ApiReleaseDetailAuthApps struct {
	AuthApps []*ApiReleaseDetailAuthAppsItem `form:"auth_apps" json:"auth_apps"`
}

type ApiReleaseDetailApiErrs struct {
	ApiErrs []apierr.ApiErr `form:"api_errs" json:"api_errs"`
}

type ApiReleaseDetailResp struct {
	*base.ApiDetail
	*ApiReleaseDetailMain
	*ApiReleaseDetailAuthApps
	*ApiReleaseDetailApiErrs
}

type ApiBase struct {
	RequestMethod  base.ApiRequestMethod  `form:"request_method" json:"request_method" db:"request_method" binding:"required,validateApiRequestMethod"`
	RequestPath    string                 `form:"request_path" json:"request_path" db:"request_path" binding:"required,validateApiRequestPath"`
	ResponseFormat base.ApiResponseFormat `form:"response_format" json:"response_format" db:"response_format" binding:"required,validateApiResponseFormat"`
}

type ApiAddReq struct {
	ApiBase
	Name        string `form:"name" json:"name" db:"name" binding:"required"`
	GroupID     string `form:"group_id" json:"group_id" db:"group_id" binding:"required"`
	ResourceID  string `form:"resource_id" json:"resource_id" db:"resource_id"`
	CreateMode  string `form:"create_mode" json:"create_mode" db:"create_mode" binding:"required"`
	Description string `form:"description" json:"description" db:"description" binding:"required"`
}

type ApiAddResp struct {
	ApiID   string `form:"api_id" json:"api_id" db:"api_id"`
	ApiCode string `form:"api_code" json:"api_code" db:"api_code"`
}

type ApiDeleteReq struct {
	ApiCode string `form:"api_code" json:"api_code" db:"api_code"`
}

type ApiUpdateReq struct {
	ApiBase
	ApiID       string `form:"api_id" json:"api_id" db:"api_id"`
	ApiCode     string `form:"api_code" json:"api_code" db:"api_code"`
	Description string `form:"description" json:"description" db:"description"`

	ResponseSuccessExample string `form:"response_success_example" json:"response_success_example" db:"response_success_example"`
	ResponseFailedExample  string `form:"response_failed_example" json:"response_failed_example" db:"response_failed_example"`

	RequestParams  []*base.ApiParamBase `form:"request_params" json:"request_params" db:"request_params"`
	ResponseParams []*base.ApiParamBase `form:"response_params" json:"response_params" db:"response_params"`

	ResourceID    string                `form:"resource_id" json:"resource_id" db:"resource_id"`
	CreateContent base.ApiCreateContent `form:"create_content" json:"create_content" db:"create_content"`
	CreateMode    base.ApiCreateMode    `form:"create_mode" json:"create_mode" db:"create_mode"`
	EnablePage    base.ApiEnablePage    `json:"enable_page" db:"enable_page"`
	ReleaseStatus string                `json:"release_status" db:"release_status"`
	GroupID       string                `form:"group_id" json:"group_id" db:"group_id"`
	RefTableSet   string                `form:"ref_table_set" json:"ref_table_set" db:"ref_table_set"`
}

type ApiAuthorizeItemReq struct {
	AppID     string           `form:"app_id" json:"app_id" db:"app_id"`
	AuthTerm  base.ApiAuthTerm `form:"auth_term" json:"auth_term" db:"auth_term"`
	StartTime string           `form:"start_time" json:"start_time" db:"start_time"`
	EndTime   string           `form:"end_time" json:"end_time" db:"end_time"`
}

type ApiAuthorizeReq struct {
	ApiCodes   []string              `form:"api_codes" json:"api_codes" db:"api_codes" binding:"required"`
	Apps       []ApiAuthorizeItemReq `form:"apps" json:"apps" binding:"required"`
	AuthMethod base.AuthMethod       `form:"auth_method" json:"auth_method" binding:"required"`
}

type ApiUnbindReq struct {
	ApiCode string   `form:"api_code" json:"api_code" db:"api_code" binding:"required"`
	Apps    []string `form:"apps" json:"apps" binding:"required"`
}

type ApiTestRunReq struct {
	ApiMeta     *base.ApiDetail           `json:"api_meta"`
	InputParams []*base.InputRequestParam `json:"input_params"`
}

type ApiTestResultReq struct {
	ApiID                  string `form:"api_id" json:"api_id" db:"id"`
	ResponseSuccessExample string `form:"success_example" json:"success_example" db:"response_success_example"`
	ResponseFailedExample  string `form:"failed_example" json:"failed_example" db:"response_failed_example"`
}

type ApplicationApiItemResp struct {
	Rank int `form:"rank" json:"rank" db:"rank"`
	base.ApplicationApi
}

type ApiUpSetLimitReq struct {
	ApiCode      string `form:"api_code" json:"api_code" db:"api_code" binding:"required"`
	RateLimit    int32  `form:"rate_limit" json:"rate_limit" db:"rate_limit"`
	TimeoutLimit int32  `form:"timeout_limit" json:"timeout_limit" db:"timeout_limit"`
}

type AuthAppsItem struct {
	ApiCode   string           `form:"api_code" json:"api_code" db:"api_code"`
	AppID     string           `form:"app_id" json:"app_id" db:"app_id"`
	Name      string           `form:"name" json:"name" db:"name"`
	AuthTerm  base.ApiAuthTerm `form:"auth_term" json:"auth_term" db:"auth_term"`
	StartTime db.NullTime      `form:"start_time" json:"start_time" db:"start_time"`
	EndTime   db.NullTime      `form:"end_time" json:"end_time" db:"end_time"`
}

const (
	NotEdit = 0
	Edit    = 1
)

const (
	NotRelationModel = 0
	RelationModel    = 1
)

type BatchGetApiMainReq struct {
	ApiCodes []string       `json:"api_codes"`
	Status   base.ApiStatus `json:"status"`
}

type ApiListWithOpenApiReq struct {
	SubjectId   string `form:"subject_id" json:"subject_id" db:"subject_id"`
	ProjectCode string `form:"project_code" json:"project_code" db:"project_code"`
	TenantCode  string `form:"tenant_code" json:"tenant_code" db:"tenant_code"`
}

type ApiListWithOpenApiInfo struct {
	Name           string               `form:"name" json:"name"`
	ApiUrl         string               `form:"api_url" json:"api_url"`
	Method         string               `form:"method" json:"method"`
	RequestParams  []*base.ApiParamBase `form:"request_params" json:"request_params"`
	ResponseParams []*base.ApiParamBase `form:"response_params" json:"response_params"`
}
type ApiListWithOpenApiResp struct {
	Name      string                    `form:"name" json:"name"`
	SubjectId string                    `form:"subject_id" json:"subject_id"`
	Apis      []*ApiListWithOpenApiInfo `form:"apis" json:"apis"`
}

type UpgradeApiReleaseReq struct {
	Projects []string `form:"projects" json:"projects"`
}
