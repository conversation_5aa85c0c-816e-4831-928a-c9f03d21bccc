package base

import (
	"encoding/json"
	"testing"
)

func TestUnmarshalAPI(t *testing.T) {
	const data = `{"dmsql": "", "mysql": "SELECT id, age FROM dim_liwj_test02_t4", "sorts": [], "tables": [], "origin_dmsql": "", "origin_mosql": "", "origin_mysql": "SELECT id, age FROM dim_liwj_test02_t4", "dialect_setting": "multi_sql"}`
	var cnt ApiCreateContent
	if err := json.Unmarshal([]byte(data), &cnt); err != nil {
		panic(err)
	}
	t.Logf("%+v", cnt)
}
