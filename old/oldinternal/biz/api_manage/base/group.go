package base

import "gitlab.mypaas.com.cn/dmp/gopkg/db"

type ApiGroup struct {
	Id          string        `gorm:"id" json:"id" db:"id"`
	Name        string        `gorm:"name" json:"name" db:"name" binding:"required"`
	Description db.NullString `gorm:"description" json:"description" db:"description"`
	SubjectId   string        `gorm:"subject_id" json:"subject_id" db:"subject_id"`
	CreatedBy   string        `gorm:"created_by" json:"created_by" db:"created_by"`
	CreatedOn   string        `gorm:"created_on" json:"created_on" db:"created_on"`
	ModifiedBy  string        `gorm:"modified_by" json:"modified_by" db:"modified_by"`
	ModifiedOn  string        `gorm:"modified_on" json:"modified_on" db:"modified_on"`
}
