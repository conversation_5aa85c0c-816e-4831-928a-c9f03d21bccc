package base

import (
	"encoding/json"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"

	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type ApiSource string

const (
	ApiSourceADS ApiSource = "ADS"
	ApiSourceRDS ApiSource = "MySQL_RDS"
)

const TotalNumName = "total"

const ApiMetaRedisCacheKey = "dap-dimensional-modeling:api_meta_v2:%s:%s"

type ApiStatus string

const (
	Dev       ApiStatus = "开发中"
	Published ApiStatus = "已发布"
)

type ApiStage int32

func (stage ApiStage) IsDevelopment() bool {
	return stage == StageDraft
}

func (stage ApiStage) IsProduction() bool {
	return !stage.IsDevelopment()
}

const (
	StageDraft   ApiStage = 0
	StageRelease ApiStage = 1
	StageHistory ApiStage = 2
)

// api内容是否做修改
type ApiChange int32

const (
	ChangeNo ApiChange = 0
	Change   ApiChange = 1
)

type ApiCreateMode string

const (
	ModeView      ApiCreateMode = "单表视图"
	ModeSql       ApiCreateMode = "编码"
	ModeIndicator ApiCreateMode = "指标"
)

type ApiFieldCategory string

const (
	ApiFieldReq  ApiFieldCategory = "Request"
	ApiFieldResp ApiFieldCategory = "Response"
)

type ApiRequestMethod string

const (
	MethodGet  ApiFieldCategory = "GET"
	MethodPost ApiFieldCategory = "POST"
)

type DisplayApiStatus string

const (
	ApiStatusNormalDisplay  DisplayApiStatus = "正常"
	ApiStatusOfflineDisplay DisplayApiStatus = "已下线"
	ApiStatusExpiredDisplay DisplayApiStatus = "已过期"
)

type ApiUpdatedStatus int

const (
	Positive ApiUpdatedStatus = 1
	Negative ApiUpdatedStatus = 0
)

type ApiEnablePage int32

const (
	PageEnable  ApiEnablePage = 1
	PageDisable ApiEnablePage = 0
)

type SourceField string

const (
	SourceFieldSystem SourceField = "System"
	SourceFieldUser   SourceField = "User"
)

// ApiParamDataType api参数类型
type ApiParamDataType string

const (
	ParamTypeString  ApiParamDataType = "STRING"
	ParamTypeInt     ApiParamDataType = "INT"
	ParamTypeLong    ApiParamDataType = "LONG"
	ParamTypeFloat   ApiParamDataType = "FLOAT"
	ParamTypeDouble  ApiParamDataType = "DOUBLE"
	ParamTypeBoolean ApiParamDataType = "BOOLEAN"
)

var prestoSchemaPrefix = "$#schema#"

// api授权模式
type ApiAuthTerm int

const (
	Forever ApiAuthTerm = 0
	Custom  ApiAuthTerm = 1
)

type Sort struct {
	Column        string       `json:"column"`
	ParamType     ApiParamType `json:"param_type"`
	IndicatorCode string       `json:"indicator_code"`
	Method        string       `json:"method"`
}

type Table struct {
	TableName string `json:"table_name"`
	Catalog   string `json:"catalog"`
}

func (t *Table) GenPrestoQueryTableName() string {
	if t.Catalog != "" {
		return fmt.Sprintf("%s.%s.%s", t.Catalog, prestoSchemaPrefix, t.TableName)
	}
	return t.TableName
}

type ApiCreateContent struct {
	global.DialectSettingContent
	Tables    []Table `json:"tables"`
	Sorts     []Sort  `json:"sorts"`
	ModelCode string  `json:"model_code"`
	SubjectId string  `json:"subject_id"`
}

func (c *ApiCreateContent) UnmarshalJSON(bs []byte) error {
	var data = struct {
		Tables         []interface{}             `json:"tables"`
		OriginMoSql    string                    `json:"origin_mosql"`
		OriginMysql    string                    `json:"origin_mysql"`
		OriginDmSql    string                    `json:"origin_dmsql"`
		MySql          string                    `json:"mysql"`
		DmSql          string                    `json:"dmsql"`
		DialectSetting global.DialectSettingType `json:"dialect_setting"`
		Sorts          []Sort                    `json:"sorts"`
		ModelCode      string                    `json:"model_code"`
		SubjectId      string                    `json:"subject_id"`
	}{}
	if err := json.Unmarshal(bs, &data); err != nil {
		return err
	}

	isOldVersion := false
	for _, table := range data.Tables {
		switch table.(type) {
		case string:
			isOldVersion = true
			break
		default:
			break
		}
	}
	c.OriginMoSql = data.OriginMoSql
	c.OriginMySql = data.OriginMysql
	c.OriginDmSql = data.OriginDmSql
	c.DialectSetting = data.DialectSetting
	c.MySql = data.MySql
	c.DmSql = data.DmSql
	c.Sorts = data.Sorts
	c.Tables = make([]Table, 0, len(data.Tables))
	c.ModelCode = data.ModelCode
	c.SubjectId = data.SubjectId
	if !isOldVersion {
		for _, table := range data.Tables {
			tableStruct := table.(map[string]interface{})
			var tableData Table
			if tableName, ok := tableStruct["table_name"].(string); ok {
				tableData.TableName = tableName
			}
			if catalog, ok := tableStruct["catalog"].(string); ok {
				tableData.Catalog = catalog
			}
			c.Tables = append(c.Tables, tableData)
		}
		return nil
	}

	for _, table := range data.Tables {
		tableName := table.(string)
		c.Tables = append(c.Tables, Table{
			TableName: tableName,
			Catalog:   "",
		})
	}
	return nil
}

type ApiResponseFormat string

const (
	ResponseFormatJson ApiResponseFormat = "json"
)

type ApiParamOperator string

const (
	OperatorEqual ApiParamOperator = "="
	OperatorIn    ApiParamOperator = "IN"
	OperatorLike  ApiParamOperator = "LIKE"
)

var MysqlTypeToSystemType = map[string]ApiParamDataType{
	"bool":    ParamTypeBoolean,
	"boolean": ParamTypeBoolean,

	"tinyint":  ParamTypeInt,
	"smallint": ParamTypeInt,
	"int":      ParamTypeInt,
	"integer":  ParamTypeInt,
	"bigint":   ParamTypeInt,

	"float":   ParamTypeFloat,
	"double":  ParamTypeDouble,
	"decimal": ParamTypeDouble,

	"varchar": ParamTypeString,

	"date":     ParamTypeString,
	"time":     ParamTypeString,
	"datetime": ParamTypeString,

	"timestamp": ParamTypeInt,
}

var ADSTypeToSystemType = map[string]ApiParamDataType{
	"boolean": ParamTypeBoolean,

	"tinyint":  ParamTypeInt,
	"smallint": ParamTypeInt,
	"int":      ParamTypeInt,
	"integer":  ParamTypeInt,
	"bigint":   ParamTypeInt,

	"float":   ParamTypeFloat,
	"double":  ParamTypeDouble,
	"decimal": ParamTypeDouble,

	"varchar": ParamTypeString,

	"date":     ParamTypeString,
	"time":     ParamTypeString,
	"datetime": ParamTypeString,

	"timestamp": ParamTypeInt,
}

type ApiParamType string

const (
	Default   ApiParamType = ""
	Indicator ApiParamType = "indicator"
	Dimension ApiParamType = "dimension"
	Variable  ApiParamType = "variable"
)

type ApiParamBase struct {
	ID              string       `json:"id" db:"id"`
	ApiID           string       `json:"api_id" db:"api_id"`
	Name            string       `json:"name" db:"name" binding:"required"`
	Description     string       `json:"description" db:"description"`
	DataType        string       `json:"data_type" db:"data_type" binding:"required"`
	Operator        string       `json:"operator" db:"operator"`
	IsRequired      int32        `json:"is_required" db:"is_required" binding:"required"`
	DefaultValue    string       `json:"default_value" db:"default_value"`
	ExampleValue    string       `json:"example_value" db:"example_value"`
	Source          string       `json:"source" db:"source"`
	ParamType       ApiParamType `json:"param_type" db:"param_type"`
	IndicatorCode   string       `json:"indicator_code" db:"indicator_code"`
	VariableContent string       `json:"variable_content" db:"variable_content"`
}

var PageNumPublicParam = ApiParamBase{
	Name:         "pageNum",
	DataType:     "INT",
	IsRequired:   1,
	Operator:     "=",
	DefaultValue: "1",
	ExampleValue: "1",
	Source:       string(SourceFieldSystem),
}

var PageSizePublicParam = ApiParamBase{
	Name:         "pageSize",
	DataType:     "INT",
	IsRequired:   1,
	Operator:     "=",
	DefaultValue: "100",
	ExampleValue: "100",
	Source:       string(SourceFieldSystem),
}

var DynamicColumnsPublicParam = ApiParamBase{
	Name:         "dynamicColumns",
	DataType:     "STRING",
	IsRequired:   0,
	Operator:     "=",
	DefaultValue: "",
	ExampleValue: "col1,col2,col3",
	Source:       string(SourceFieldSystem),
}

var BigdataTenantCodePublicParam = ApiParamBase{
	Name:       "bdp_tenantcode",
	DataType:   "STRING",
	IsRequired: 1,
	Operator:   "=",
	Source:     string(SourceFieldSystem),
}

type ApiMain struct {
	Code         string    `json:"code" db:"code"`
	Name         string    `json:"name" db:"name"`
	GroupID      string    `json:"group_id" db:"group_id"`
	Status       ApiStatus `json:"status" db:"status"`
	IsUpdated    int32     `json:"is_updated" db:"is_updated"`
	RateLimit    int64     `json:"rate_limit" db:"rate_limit"`
	TimeoutLimit int64     `json:"timeout_limit" db:"timeout_limit"`
	ApiUrl       string    `json:"api_url"`
}

type ApiDetail struct {
	ID    string   `json:"api_id" db:"id"`
	Code  string   `json:"api_code" db:"code"`
	Stage ApiStage `json:"stage" db:"stage"`

	Description    string `json:"description" db:"description"`
	Version        int32  `json:"version" db:"version"`
	ReleaseComment string `json:"release_comment" db:"release_comment"`

	RequestMethod          ApiRequestMethod  `json:"request_method" db:"request_method" form:"request_method"`
	RequestPath            string            `json:"request_path" db:"request_path" form:"request_path"`
	ResponseFormat         ApiResponseFormat `json:"response_format" db:"response_format" form:"response_format"`
	ResponseSuccessExample string            `json:"response_success_example" db:"response_success_example"`
	ResponseFailedExample  string            `json:"response_failed_example" db:"response_failed_example"`

	ResourceID    string           `json:"resource_id" db:"resource_id" form:"resource_id"`
	CreateMode    ApiCreateMode    `json:"create_mode" db:"create_mode" form:"create_mode"`
	CreateContent ApiCreateContent `json:"create_content" db:"create_content" form:"create_content"`
	EnablePage    ApiEnablePage    `json:"enable_page" db:"enable_page" form:"enable_page"`

	RequestParams     []*ApiParamBase `form:"request_params" json:"request_params" db:"request_params"`
	ResponseParams    []*ApiParamBase `form:"response_params" json:"response_params" db:"response_params"`
	RefTableSetResult string          `json:"ref_table_set_result"`

	CreatedBy  string `json:"created_by" db:"created_by"`
	CreatedOn  string `json:"created_on" db:"created_on"`
	ModifiedBy string `json:"modified_by" db:"modified_by"`
	ModifiedOn string `json:"modified_on" db:"modified_on"`
}

type InputRequestParam struct {
	Name  string      `json:"name" form:"name"`
	Value interface{} `json:"value" form:"value"`
}

type ApplicationApi struct {
	AppID     string      `gorm:"app_id" json:"app_id" db:"app_id"`
	ApiKey    string      `gorm:"api_key" json:"api_key" db:"api_key"`
	ApiCode   string      `gorm:"api_code" json:"api_code" db:"api_code"`
	AuthTerm  ApiAuthTerm `gorm:"auth_term" json:"auth_term" db:"auth_term"`
	StartTime db.NullTime `gorm:"start_time" json:"start_time" db:"start_time"`
	EndTime   db.NullTime `gorm:"end_time" json:"end_time" db:"end_time"`

	CreatedBy  db.NullString `gorm:"created_by" json:"created_by" db:"created_by"`
	CreatedOn  db.NullTime   `gorm:"created_on" json:"created_on" db:"created_on"`
	ModifiedBy db.NullString `gorm:"modified_by" json:"modified_by" db:"modified_by"`
	ModifiedOn db.NullTime   `gorm:"modified_on" json:"modified_on" db:"modified_on"`
}
type ApplicationSystemApiGroup struct {
	AppID      string        `json:"app_id" db:"app_id"`
	Name       string        `json:"app_name" db:"app_name"`
	GroupID    string        `json:"group_id" db:"group_id"`
	CreatedBy  db.NullString `json:"created_by" db:"created_by"`
	CreatedOn  db.NullTime   `json:"created_on" db:"created_on"`
	ModifiedBy db.NullString `json:"modified_by" db:"modified_by"`
	ModifiedOn db.NullTime   `json:"modified_on" db:"modified_on"`
}

type AuthMethod string

const (
	AuthMethodAppend  AuthMethod = "app_append"
	AuthMethodReplace AuthMethod = "app_replace"
)

type MiniAuthApp struct {
	AppId string `json:"app_id" db:"app_id"`
	Name  string `json:"name" db:"name"`
}

type Resource struct {
	Id   string `json:"id" db:"id"`
	Type string `json:"type" db:"type"`
}
