package parser

import (
	"fmt"
	"strings"

	"github.com/dlclark/regexp2"
)

const (
	tmpApiParamEscapeFormat = "____api_param_escape_%s____"

	tmpXmlLessThanEscape = "____less__than____"
	tmpXmlNewlineEscape  = "____newline____"
	apiParamFormat       = "${%s}"
)

var mybatisTagLessThanEscapePattern = regexp2.MustCompile(fmt.Sprintf(`<(?!/?(%s))`, strings.Join(mybatisTagNames, "|")), 0)

type ApiParamEscape struct {
}

func (e *ApiParamEscape) EscapeParam(sqlText string, paramNames []string) (escapedSqlText string) {
	escapedSqlText = sqlText
	for _, paramName := range paramNames {
		placeholderParam := fmt.Sprintf(apiParamFormat, paramName)
		tmpApiParamEscape := fmt.Sprintf(tmpApiParamEscapeFormat, paramName)
		escapedSqlText = strings.ReplaceAll(escapedSqlText, placeholderParam, tmpApiParamEscape)
	}
	return
}

func (e *ApiParamEscape) RecoverParam(escapedSqlText string, paramNames []string) (sqlText string) {
	sqlText = escapedSqlText
	for _, paramName := range paramNames {
		placeholderParam := fmt.Sprintf(apiParamFormat, paramName)
		tmpApiParamEscape := fmt.Sprintf(tmpApiParamEscapeFormat, paramName)
		sqlText = strings.ReplaceAll(sqlText, tmpApiParamEscape, placeholderParam)
	}
	return
}

// XMLLessThanEscape Xml 非转义的小于号替换为特殊符号，防止xml解析失败
type XMLEscape struct {
}

func (e *XMLEscape) EscapeParam(sqlText string, paramNames []string) (escapedSqlText string) {
	escapedSqlText, err := mybatisTagLessThanEscapePattern.Replace(sqlText, tmpXmlLessThanEscape, -1, -1)
	if err != nil {
		return sqlText
	}
	escapedSqlText = strings.ReplaceAll(escapedSqlText, "\n", tmpXmlNewlineEscape)
	return
}

func (e *XMLEscape) RecoverParam(escapedSqlText string, paramNames []string) (sqlText string) {
	sqlText = strings.ReplaceAll(escapedSqlText, tmpXmlLessThanEscape, "<")
	sqlText = strings.ReplaceAll(sqlText, tmpXmlNewlineEscape, "\n")
	return
}
