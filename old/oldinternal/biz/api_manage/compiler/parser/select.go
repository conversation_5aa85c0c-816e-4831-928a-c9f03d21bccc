package parser

import (
	"log"
	"regexp"
	"strings"

	"github.com/jinzhu/copier"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors/biz"

	"gitlab.mypaas.com.cn/bigdata/vitess/go/vt/sqlparser"
)

var withStarPattern = regexp.MustCompile(`^(\w\.)*\*$`)
var widowComp = regexp.MustCompile(`(?is)\b(?:ROW_NUMBER|RANK|DENSE_RANK|NTILE|LAG|LEAD|FIRST_VALUE|LAST_VALUE|COUNT|SUM|MIN|MAX|AVG)\b\s*\([^()]*\)\s*OVER\s*\([^()]*\)\s*`)

type SelectParser struct {
	Parser *sqlparser.Select
}

func NewSelectParser(sql string) (*SelectParser, error) {
	sql = filterSQL(sql)
	stmt, err := sqlparser.Parse(sql)
	if err != nil {
		return nil, errors.New(biz.API_SQL_PARSE_ERR, err.Error())
	}
	switch parser := stmt.(type) {
	case *sqlparser.Select:
		return &SelectParser{Parser: parser}, nil
	case *sqlparser.Union:
		buf := sqlparser.NewTrackedBuffer(nil)
		// todo union的场景目前只取的左边的sql，可能获取order by，group by会有问题，后续使用的时候注意bug
		parser.Left.Format(buf)
		return NewSelectParser(buf.String())
	default:
		return nil, errors.New(biz.API_SQL_PARSE_ERR, "非法的查询语句，请参考SQL编写提示的规则修改语句")
	}
}

func (s *SelectParser) GetSelect() (fragment string) {
	selectBuf := sqlparser.NewTrackedBuffer(nil)
	s.Parser.SelectExprs.Format(selectBuf)
	return selectBuf.String()
}

func (s *SelectParser) GetOrderBy() (fragment string) {
	orderByBuf := sqlparser.NewTrackedBuffer(nil)
	s.Parser.OrderBy.Format(orderByBuf)
	return orderByBuf.String()
}

// GetReplaceTableNamedOrderBy 获取替换表名为t后的order by语句
func (s *SelectParser) GetReplaceTableNamedOrderBy() string {
	var newOrderBy = sqlparser.OrderBy{}
	for _, order := range s.Parser.OrderBy {
		var newOrder sqlparser.Order
		var err = copier.Copy(&newOrder, order)
		if err != nil {
			log.Printf("GetReplaceTableNamedOrderBy: copy order by err: %v", err)
			return ""
		}
		// 断言order表达式为列
		if colName, ok := newOrder.Expr.(*sqlparser.ColName); ok {
			var newOrderExpr = &sqlparser.ColName{}
			err = copier.Copy(newOrderExpr, colName)
			if err != nil {
				log.Printf("GetReplaceTableNamedOrderBy: copy order by ColName err: %v", err)
				return ""
			}
			newOrderExpr.Qualifier.Name = sqlparser.NewIdentifierCS("t")
			newOrder.Expr = sqlparser.ReplaceExpr(newOrder.Expr, newOrder.Expr, newOrderExpr)
		}
		newOrderBy = append(newOrderBy, &newOrder)
	}
	var buffer = sqlparser.NewTrackedBuffer(nil)
	newOrderBy.Format(buffer)
	return buffer.String()
}

func (s *SelectParser) GetGroupBy() (fragment string) {
	groupByBuf := sqlparser.NewTrackedBuffer(nil)
	s.Parser.GroupBy.Format(groupByBuf)
	return groupByBuf.String()
}

func (s *SelectParser) GetHaving() (fragment string) {
	havingBuf := sqlparser.NewTrackedBuffer(nil)
	s.Parser.Having.Format(havingBuf)
	return havingBuf.String()
}

func (s *SelectParser) CheckStarInSelect() (isIn bool) {
	selectFragments := s.GetSelect()
	fragments := strings.Split(selectFragments, ",")
	for _, fragment := range fragments {
		// 去除空格
		fragment = strings.Replace(fragment, " ", "", -1)
		// 去除换行符
		fragment = strings.Replace(fragment, "\n", "", -1)
		if withStarPattern.MatchString(fragment) {
			return true
		}
	}
	return
}

func filterSQL(sql string) string {
	sql = widowComp.ReplaceAllLiteralString(sql, " 1 ")
	return sql
}
