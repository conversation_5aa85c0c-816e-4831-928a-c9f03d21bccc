package parser

import (
	"fmt"
	"regexp"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/mybatis"
)

var (
	escapes       = []Escape{&ApiParamEscape{}, &XMLEscape{}}
	mybatisParser = mybatis.NewGoMybatisParser("mysql", true)
)

var (
	paramPattern          = regexp.MustCompile(`(\S+)\s*\(*\$\{([a-zA-Z_\d\.]+)\}\)*`)
	mybatisTagPatternList []*regexp.Regexp
	mybatisTagNames       = []string{
		"if", "where", "foreach", "trim", "choose", "include", "bind", "otherwise", "when", "set",
	}
)

func init() {
	mybatisTagPatternList = make([]*regexp.Regexp, 0, len(mybatisTagNames))
	for _, tagName := range mybatisTagNames {
		mybatisTagPatternList = append(mybatisTagPatternList, regexp.MustCompile(fmt.Sprintf(`<%s[\s+][\s\S]*>[\s\S]*</%s>`, tagName, tagName)))
	}
}

type Escape interface {
	EscapeParam(sqlText string, paramNames []string) (escapedSqlText string)
	RecoverParam(escapedSqlText string, paramNames []string) (sqlText string)
}

type ParsedParam struct {
	ParamName string `json:"param_name"`
	Operator  string `json:"operator"`
	IsField   bool   `json:"is_field"`
}

func ReplaceMyBatisTag(sqlText string, inputParamMap map[string]interface{}) (replacedSql string, err error) {
	if !IsContainMybatisTag(sqlText) {
		// 如果不包含mybatis标签语法，则不执行替换流程
		return sqlText, nil
	}
	params := ParseParams(sqlText)
	var paramNames []string
	for _, p := range params {
		paramNames = append(paramNames, p.ParamName)
	}

	escapeSql := sqlText
	for _, e := range escapes {
		escapeSql = e.EscapeParam(escapeSql, paramNames)
	}
	defer func() {
		if err == nil {
			for i := len(escapes) - 1; i >= 0; i-- {
				e := escapes[i]
				replacedSql = e.RecoverParam(replacedSql, paramNames)
			}
		}
	}()
	replacedSql, err = mybatisParser.ReplaceIfTag(escapeSql, inputParamMap)
	return
}

func ParseParams(sqlText string) (params []*ParsedParam) {
	result := paramPattern.FindAllStringSubmatch(sqlText, -1)
	for _, matchItem := range result {
		if len(matchItem) > 2 {
			params = append(params, &ParsedParam{ParamName: matchItem[2], Operator: matchItem[1]})
		}
	}
	return
}

func IsContainMybatisTag(sqlText string) bool {
	for _, pt := range mybatisTagPatternList {
		matches := pt.FindAllString(sqlText, -1)
		if len(matches) > 0 {
			return true
		}
	}

	return false
}
