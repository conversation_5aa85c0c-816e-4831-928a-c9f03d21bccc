package parser

import (
	"fmt"
	"regexp"
	"strings"
	"testing"

	pkgSqlparse "gitlab.mypaas.com.cn/dmp/gopkg/sqlparse"
)

func TestReplaceMyBatisTag(t *testing.T) {
	sql := `
WITH kx as (select * from kudu.$#schema#.dwd_h_member_wide_realtime_gutest)
SELECT
     --gutest
     id  -- 会员id
    ,max(modified_on) as modified_on
FROM (
    SELECT
      m.id
     ,m.modified_on
    FROM kx m
 <where>
m.db_name1 = concat('mycommunity_',${tenantCode})
        AND m.is_deleted = 0 
            -- 公司
<if test="corpId != null and test=corpId != ''" >
and ( m.m_corp_id = ${corpId} OR m.mr_corp_id = ${corpId} )
</if>

        -- 客户类型
        <if test="type != null and test=type !=''" >
         and m.type=${type}
</if>

        -- 是否认证
<if test="isAuthenticated != null and test=isAuthenticated !=''" >
         and m.is_authenticated  = ${isAuthenticated}
</if>
 <if test="isAuthenticated != null and test=isAuthenticated !=''" >
         and m.is_authenticated  = ${isAuthenticated}
</if>

        -- 微信是否登录
       AND if(${keepLoginWechat}='1', m.pf_member_id is not null
           ,1=1
       )
        AND if(${keepLoginWechat}='0', m.pf_member_id is null
          --  ,1=1
     )
         -- 业主更新时间
 <if test="modifiedOnStart != null and modifiedOnStart !=''" >
        and m.modified_on >= ${modifiedOnStart} AND m.modified_on < ${modifiedOnEnd}
</if>
        -- 项目
         <if test="projectId != null and projectId !=''" >
        and m.proj_id = ${projectId}
</if>
        -- 业主姓名/业主身份证号/业主手机号
                 <if test="id != null and id !=''" >
        and m.id in (${id})
</if>
 </where>


) k
GROUP BY id
ORDER By modified_on DESC
`
	inputParamMap := map[string]interface{}{
		"c_custkey": 22,
	}

	sqls, err := pkgSqlparse.NewSqlParser().SplitStatements(sql)
	if err != nil {
		panic(err)
	}
	sql = strings.Join(sqls, ";")
	sql2, err := ReplaceMyBatisTag(sql, inputParamMap)
	if err != nil {
		panic(err)
	}
	params := ParseParams(sql2)
	println(sql2)
	// 获取select解析器对象
	// sqlparser不支持${p}形式，用反引号来避免，`${p}`
	tmpSql := sql2
	const tempBackQuotePatternStr = `%s*\$\{%s\}%s*`
	for _, p := range params {
		placeholder := fmt.Sprintf("`$${%s}`", p.ParamName)
		replacePatternStr := fmt.Sprintf(tempBackQuotePatternStr, "`", p.ParamName, "`")
		replacePattern := regexp.MustCompile(replacePatternStr)
		tmpSql = replacePattern.ReplaceAllString(tmpSql, placeholder)
	}

	// 处理presto带schema前缀的场景
	var prestoSchemaQueryPathPattern = regexp.MustCompile(`\.\$#schema#\.`)
	tmpSql = prestoSchemaQueryPathPattern.ReplaceAllString(tmpSql, ".")

	p, err := NewSelectParser(tmpSql)
	if err != nil {
		panic(err)
	}
	println(p.CheckStarInSelect())
}

func TestWithSqls(t *testing.T) {
	rawSqls := []string{
		`
		SELECT a.CgTypeGUID,
			   COUNT(*) OVER ( PARTITION BY a.CgTypeGUID, a.CgFormGUID, a.SolutionStepCode ) AS 'num'
		     FROM a
		`,
		`SELECT ActualFinishTime, SUM(RmbAmount) OVER ( ORDER BY ActualFinishTime rows UNBOUNDED PRECEDING ) as AccRMB FROM temp`,
		`
SELECT A.CgTypeGUID,
       A.CgFormGUID,
       A.Duration,
       A.SolutionStepCode,
       A.CgSolutionName,
       SolutionStepName
FROM (SELECT a.CgTypeGUID,
             a.CgFormGUID,
             a.CgSolutionName,
             a.SolutionStepCode,
             a.Duration,
             a.SolutionStepName,
             ROW_NUMBER() OVER ( PARTITION BY a.CgTypeGUID, a.CgFormGUID, a.SolutionStepCode ORDER BY a.Duration ) AS 'RowNum' ,
           	 COUNT(*) OVER ( PARTITION BY a.CgTypeGUID, a.CgFormGUID, a.SolutionStepCode ) AS 'num'
      FROM (SELECT t.CgTypeGUID,
                   t.CgFormGUID,
                   t.CgSolutionName,
                   t.SolutionStepCode,
                   t.SolutionStepName,
                   t.Duration,
                   t.StepState
            FROM (SELECT s.CgTypeGUID,
                         s.CgFormGUID,
                         s.SolutionName AS 'CgSolutionName',
                         t.SolutionStepCode,
                         t.SolutionStepName,
                         IFNULL(DATEDIFF(n.RealEndDate, CASE
                                                            WHEN n.SolutionStepCode = 'Solution' THEN s.PlanStartDate
                                                            ELSE preNode.RealEndDate
                             END), 0)   AS 'Duration',
                         CASE
                             WHEN n.NodeState = 0 THEN 0
                             WHEN n.NodeState = 1
                                 AND DATEDIFF(IFNULL(n.PlanEndDate, '9999-01-01'), Now()) >= 0 THEN 1
                             WHEN n.NodeState = 1
                                 AND DATEDIFF(IFNULL(n.PlanEndDate, '9999-01-01'), Now()) < 0 THEN 2
                             WHEN n.NodeState = 2
                                 AND n.IsDelay = 0
                                 AND n.SolutionStepCode <> s.CurrentStepCode THEN 3
                             WHEN n.NodeState = 2
                                 AND n.IsDelay = 1
                                 AND n.SolutionStepCode <> s.CurrentStepCode THEN 4
                             WHEN n.NodeState = 2
                                 AND n.IsDelay = 0
                                 AND n.SolutionStepCode = s.CurrentStepCode THEN 5
                             WHEN n.NodeState = 2
                                 AND n.IsDelay = 1
                                 AND n.SolutionStepCode = s.CurrentStepCode THEN 6
                             END        AS 'StepState'
                  FROM dwd_cg_CgSolutionNode AS n
                           LEFT JOIN dwd_cg_CgSolutionNode AS preNode ON preNode.SolutionGUID = n.SolutionGUID
                      AND preNode.OrderNum = n.OrderNum - 1
                           INNER JOIN dwd_cg_SolutionStep AS t ON t.SolutionStepGUID = n.SolutionStepGUID
                           INNER JOIN dwd_cg_CgSolution AS s ON n.SolutionGUID = s.CgSolutionGUID
                           INNER JOIN dwd_cg_CgSolutionProject AS p ON p.SolutionGUID = n.SolutionGUID
                  WHERE s.CgTypeGUID = ''
                    AND s.CgFormGUID = ''
                    AND LOCATE('', s.SolutionName) > 0) AS t
            WHERE t.StepState IN (3, 4, 5, 6)) AS a) AS a
WHERE RowNum = (CONCAT(num, 1)) / 2
`,
		`
WITH  c1 AS (
			SELECT
			BUGUID,
			NULL AS TotalAmount,
			NULL AS SignCount,
			NULL AS BidCount,
			NULL AS WinBidCount,
			NULL AS WinBidAmount,
			'XY' AS TypeName
			FROM
			data_wide_cg_TacticCgAgreement a
			WHERE
			a.YfProviderGUID = @pid
			AND a.BusinessStatus > 1
			AND (
			(
			@periodenum = 0
			OR @periodenum = 4
			)
			OR EXISTS (
			SELECT
			1
			FROM
			data_wide_cg_TacticCgAgreement b
			WHERE
			b.TacticCgAgreementGUID = a.TacticCgAgreementGUID
			AND b.SignDate BETWEEN @startdate
			AND @enddate
			)
			)
			UNION ALL
			SELECT  BUGUID ,
                    SUM(ccr.ContractAmount) AS TotalAmount,
                    COUNT(1) AS SignCount,
					NULL AS BidCount,
					NULL AS WinBidCount,
					NULL AS WinBidAmount,
                    'FYHT' AS TypeName
                FROM    data_wide_cg_ContractRegister ccr
                               WHERE   ccr.YfProviderGUID = @pid
                        AND ( ( @periodenum = 0
                                OR @periodenum = 4
                              )
                              OR EXISTS ( SELECT    1
                                          FROM      data_wide_cg_ContractRegister cr
                                          WHERE     cr.ContractRegisterGUID = ccr.ContractRegisterGUID
                                                    AND cr.SignDate BETWEEN @startdate AND @enddate )
                            )
						AND ccr.SignType = 1
			GROUP BY BUGUID
			UNION ALL
			SELECT  BUGUID ,
                    SUM(ccr.ContractAmount) AS TotalAmount,
                    COUNT(1) AS SignCount,
					NULL AS BidCount,
					NULL AS WinBidCount,
					NULL AS WinBidAmount,
                    'CBHT' AS TypeName
                FROM    data_wide_cg_ContractRegister ccr
                               WHERE   ccr.YfProviderGUID = @pid
                        AND ( ( @periodenum = 0
                                OR @periodenum = 4
                              )
                              OR EXISTS ( SELECT    1
                                          FROM      data_wide_cg_ContractRegister cr
                                          WHERE     cr.ContractRegisterGUID = ccr.ContractRegisterGUID
                                                    AND cr.SignDate BETWEEN @startdate AND @enddate )
                            )
						AND ccr.SignType = 0
			GROUP BY BUGUID

			UNION
			SELECT
			csp.BUGUID,
			null as TotalAmount,
			NULL AS SignCount,
			COUNT(1) AS BidCount,
			SUM(
			CASE
			WHEN csp.IsWinBid = 1 THEN
			1
			ELSE
			0
			END
			) AS WinBidCount,
			SUM(
			CASE
			WHEN csp.IsWinBid = 1 THEN
			csp.WinBidAmount
			ELSE
			0
			END
			) AS WinBidAmount,
			'CZWinBid' AS TypeName
			FROM
			data_wide_cg_CgSolutionProvider csp
			INNER JOIN data_wide_cg_CgSolution cs ON csp.CgSolutionGUID = cs.CgSolutionGUID
			WHERE
			csp.ProviderGUID = @pid
			AND cs.CgSolutionState = 2
			AND (
			(
			@periodenum = 0
			OR @periodenum = 4
			)
			OR EXISTS (
			SELECT
			1
			FROM
			data_wide_cg_CgSolution cs
			INNER JOIN data_wide_cg_CgSolutionProvider cp ON cp.CgSolutionGUID = cs.CgSolutionGUID
			WHERE
			cp.CgSolutionProviderGUID = csp.CgSolutionProviderGUID
			AND cs.RealEndDate BETWEEN @startdate
			AND @enddate
			)
			)
			GROUP BY
			csp.BUGUID
			UNION
			SELECT DISTINCT
			pr.BUGUID,
			NULL AS TotalAmount,
			NULL AS SignCount,
			NULL AS BidCount,
			NULL AS WinBidCount,
			NULL AS WinBidAmount,
			'CZProvider' AS TypeName
			FROM
			data_wide_cg_ProviderRecord pr
			LEFT JOIN data_wide_cg_Provider p ON pr.ProviderGUID = p.ProviderGUID
			WHERE
			Status = 2
			AND IsBlackList = 0
			AND KcIsCanHZ = 1
			AND (PgGUID IS NULL OR PgIsCanHZ = 1)
			AND pr.ProviderGUID = @pid


			) SELECT
			a.ProviderGUID,
			a.BUGUID,
			a.BUName,
			a.BidCount,
			a.WinBidCount,
			a.WinBidAmount,
			a.SignCount,
			a.SignAmount,
			a.WinBidRate
			FROM
			(
			SELECT
			@pid AS ProviderGUID,
			main.BUGUID,
			unit.BUName,
			bid.BidCount,
			bid.WinBidCount,
			bid.WinBidAmount / 10000 AS WinBidAmount,
			ht.SignCount,
			ht.SignAmount / 10000 AS SignAmount,
			(
			CASE
			WHEN bid.BidCount = 0 THEN
			0
			ELSE
			(bid.WinBidCount * 1.0) / bid.BidCount
			END
			) * 100 AS WinBidRate
			FROM
			(
			SELECT
			a.BUGUID
			FROM
			c1 a
			WHERE
			(
			(
			@conditiontype = 'HT'
			AND TypeName IN ('CBHT', 'FYHT', 'XY')
			)
			OR (
			@conditiontype = 'WinBid'
			AND TypeName = 'CZWinBid'
			)
			OR (
			@conditiontype = 'Provider'
			AND TypeName = 'CZProvider'
			)
			)
			GROUP BY
			a.BUGUID
			) main
			INNER JOIN data_wide_mdm_BusinessUnit unit ON unit.BUGUID = main.BUGUID
			LEFT JOIN c1 bid ON bid.BUGUID = main.BUGUID
			AND bid.TypeName = N'CZWinBid'
			LEFT JOIN (
			SELECT
			BUGUID,
			SUM(TotalAmount) AS SignAmount,
			SUM(SignCount) AS SignCount
			FROM
			c1
			WHERE
			TypeName IN (N'CBHT', N'FYHT')
			GROUP BY
			BUGUID
			) ht ON ht.BUGUID = main.BUGUID
			) a
			ORDER BY
			a.SignAmount DESC
`,
		`
with KeyNodeTask
as
( select *
        from    ( select  ROW_NUMBER()over(partition by EndBUGUID,ProjGUID,PlanObjectGUID,MonthIndex order by FinishTime asc)as RowNumber,
                                  t.EndBUGUID,
                                  t.EndBUName,
                                  t.ProjGUID,
                                  t.PlanObjectGUID,
                                  t.PlanObjectFullName,
                                  case
                                           when timestampdiff(MONTH,t.FinishTime,Now()) = 0 then t.TaskName
                                           else null
                                  END as CurrentMonth,
                                  case
                                           when timestampdiff(MONTH,t.FinishTime,Now()) = -1 then t.TaskName
                                           else null
                                  END as NextMonth,
                                  case
                                           when timestampdiff(MONTH,t.FinishTime,Now()) = -2 then t.TaskName
                                           else null
                                  END as NextNextMonth,
                                  t.FinishTime,
                                  t.TaskName
                         from     ( select BUGUID        as EndBUGUID,
                                                  BUName as EndBUName,
                                                  ProjGUID,
                                                  PlanObjectGUID,
                                                  PlanObjectFullName,
                                                  TaskState,
                                                  PlanTaskExecuteGUID,
                                                  FinishTime,
                                                  timestampdiff(MONTH,FinishTime,Now()) as MonthIndex,
                                                          CONCAT(CONCAT(CONCAT(CONCAT(CONCAT(CONCAT(CONCAT('「',
                                                  case
                                                          when TaskState = 0 then '未开始'
                                                          when TaskState = 10 then '正常进行'
                                                          when TaskState = 11 then '延期'
                                                          when TaskState = 20 then '按期完成'
                                                          when TaskState = 21 then '延期完成'
                                                          else ''
                                                  END),'」'),convert(MONTH(FinishTime),char character set utf8mb4) ),'-'),convert(DAY(FinishTime),char character set utf8mb4) ),'  '),TaskName) as TaskName
                                          from    data_wide_jh_TaskDetail
                                          where   PlanObjectState = 1
                                                  and PlanType = 8
                                                  and timestampdiff(MONTH,FinishTime,Now()) <= 0
                                                  and timestampdiff(MONTH,FinishTime,Now()) >= -2
                                                  and IFNULL(IsEnd,1) = 1 )t )tt
        where   tt.RowNumber <= 5 )
select    proj.BUGUID,
          proj.BUName,
          proj.ParentBUGUID,
          tt.EndBUGUID,
          tt.EndBUName,
          tt.ProjGUID,
          tt.PlanObjectGUID,
          tt.PlanObjectFullName,
          tt.CurrentMonth,
          tt.NextMonth,
          tt.NextNextMonth
from      ( select t.BUGUID,
                          t.BUName,
                          t.ParentBUGUID,
                          p.ProjGUID
                  from    ( select qybu.BUGUID,
                                          qybu.BUName,
                                          qybu.ParentGUID as ParentBUGUID,
                                          endBu.BUGUID    as EndBUGUID,
                                          null            as ProjGUID
                                  from    data_wide_mdm_BusinessUnit qybu
                                  join    data_wide_mdm_BusinessUnit endBu
                                  on      (endBu.HierarchyCode = qybu.HierarchyCode
                                                  or endBu.HierarchyCode like CONCAT(qybu.HierarchyCode,'.%'))
                                          and endBu.IsEndCompany = 1
                                  union all
                                  select p_projectId as BUGUID,
                                         ProjName    as BUName,
                                         BUGUID      as ParentBUGUID,
                                         null        as EndBUGUID,
                                         p_projectId as ProjGUID
                                  from   (select *
                                                 from    data_wide_mdm_project
                                                 where   p_projectId in
                                                         (select ProjGUID
                                                                 from    data_wide_jh_PlanObject
                                                                 where   PlanObjectState = 1))pro )t
                  join
                          ( select p_projectId as ProjGUID,
                                          BUGUID
                                  from    data_wide_mdm_project
                                  where   p_projectId in
                                          (select ProjGUID
                                                  from    data_wide_jh_PlanObject
                                                  where   PlanObjectState = 1) )p
                  on      p.BUGUID = t.EndBUGUID
                          or p.ProjGUID = t.ProjGUID )proj
left join
          ( select  EndBUGUID,
                            EndBUName,
                            ProjGUID,
                            PlanObjectGUID,
                            PlanObjectFullName,
                            (select  group_concat(CONCAT(CurrentMonth,'；') separator '')
                                     from     KeyNodeTask
                                     where    PlanObjectGUID = A.PlanObjectGUID
                                     order by FinishTime asc,
                                              TaskName asc ) as CurrentMonth,
                            (select  group_concat(CONCAT(NextMonth,'；') separator '')
                                     from     KeyNodeTask
                                     where    PlanObjectGUID = A.PlanObjectGUID
                                     order by FinishTime asc,
                                              TaskName asc ) as NextMonth,
                            (select  group_concat(CONCAT(NextNextMonth,'；') separator '')
                                     from     KeyNodeTask
                                     where    PlanObjectGUID = A.PlanObjectGUID
                                     order by FinishTime asc,
                                              TaskName asc ) as NextNextMonth
                   from     KeyNodeTask A
                   group by EndBUGUID,
                            EndBUName,
                            PlanObjectFullName,
                            ProjGUID,
                            PlanObjectGUID )tt
on        proj.ProjGUID = tt.ProjGUID
where     tt.EndBUGUID is not null
`,
	}
	for _, sql := range rawSqls {
		sqls, err := pkgSqlparse.NewSqlParser().SplitStatements(sql)
		if err != nil {
			panic(err)
		}
		sql = strings.Join(sqls, ";")
		params := ParseParams(sql)
		println(sql)
		const tempBackQuotePatternStr = `%s*\$\{%s\}%s*`
		for _, p := range params {
			placeholder := fmt.Sprintf("`$${%s}`", p.ParamName)
			replacePatternStr := fmt.Sprintf(tempBackQuotePatternStr, "`", p.ParamName, "`")
			replacePattern := regexp.MustCompile(replacePatternStr)
			sql = replacePattern.ReplaceAllString(sql, placeholder)
		}
		_, err = NewSelectParser(sql)
		if err != nil {
			panic(err)
		}
	}
}

func TestOrderByReplace(t *testing.T) {
	orderBySql := `
		select * from dim_cb_contractproj order by ContractName asc, ContractCode desc
	`
	parser, err := NewSelectParser(orderBySql)
	if err != nil {
		t.Error(err)
	}
	orderByExpr := parser.GetReplaceTableNamedOrderBy()
	t.Log(orderByExpr)
}
