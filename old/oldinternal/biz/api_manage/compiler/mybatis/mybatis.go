package mybatis

import (
	"fmt"
	"runtime/debug"

	"github.com/pkg/errors"

	"github.com/zhuxiujia/GoMybatis"
	"github.com/zhuxiujia/GoMybatis/lib/github.com/beevik/etree"
	"github.com/zhuxiujia/GoMybatis/stmt"
)

type GoMybatisParser struct {
	engine      *GoMybatis.GoMybatisEngine
	driver      string
	stmtConvert stmt.StmtIndexConvert
}

func NewGoMybatisParser(driver string, logEnable bool) *GoMybatisParser {
	engine := GoMybatis.GoMybatisEngine{}.New()
	engine.SetLogEnable(logEnable)
	stmtConvert, err := stmt.BuildStmtConvert(driver)
	if err != nil {
		panic(err)
	}
	return &GoMybatisParser{
		engine:      &engine,
		driver:      driver,
		stmtConvert: stmtConvert,
	}
}

func (p *GoMybatisParser) ReplaceIfTag(sqlText string, paramMap map[string]interface{}) (sql string, err error) {
	defer func() {
		// GoMybatis库存在很多panic，需要捕获错误
		if err1 := recover(); err1 != nil {
			debug.PrintStack()
			if err2, ok := err.(error); ok {
				err = err2
			} else {
				err = errors.Errorf("panic: %+v", err)
			}
		}
	}()
	selectNode, err := p.parseXml(sqlText)
	if err != nil {
		return
	}
	nodes := p.engine.SqlBuilder().NodeParser().Parser(selectNode.Child)
	arrayArg := &[]interface{}{}
	sql, err = p.engine.SqlBuilder().BuildSql(paramMap, nodes, arrayArg, p.stmtConvert)
	return
}

func (p *GoMybatisParser) parseXml(sqlText string) (selectNode *etree.Element, err error) {
	xmlData := []byte(fmt.Sprintf(`<select>%s</select>`, sqlText))
	doc := etree.NewDocument()
	if err = doc.ReadFromBytes(xmlData); err != nil {
		err = errors.Wrapf(err, "解析MyBatis标签xml语法失败")
		return
	}

	selectNode = doc.SelectElement("select")
	return
}
