package mybatis

import "testing"

func TestGoMybatisParser_ReplaceIfTag(t *testing.T) {
	p := NewGoMybatisParser("mysql", true)
	sqlText := `
	select id, name from test
	<where>
		<if test="id != null and id != ''">
			and id = '${id}'
		</if>
		<if test="name != null and name != ''">
			and name in (${names})
		</if>
		<if test="age <= 25">
			and age = ${age}
		</if>
	</where>
	`
	type Case struct {
		paramMap  map[string]interface{}
		expectSql string
	}
	cases := []Case{
		{map[string]interface{}{
			"id":    "",
			"names": nil,
			"age":   10,
		},
			"",
		},
		{map[string]interface{}{
			"id":    "tanzhiying",
			"names": []string(nil),
			"age":   55,
		},
			"",
		},
		{map[string]interface{}{
			"id":    "tanzhiying",
			"names": []string{"tanzy"},
			"age":   55,
		},
			"",
		},
	}
	for _, c := range cases {
		sql, err := p.ReplaceIfTag(sqlText, c.paramMap)
		if err != nil {
			panic(err)
		}
		println(sql)
		if sql != c.expectSql {
			panic("replace error. result is not correct")
		}
	}
}
