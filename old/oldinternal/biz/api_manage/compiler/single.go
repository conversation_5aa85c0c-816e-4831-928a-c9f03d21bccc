package compiler

import (
	"context"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/trace_utils"
	"strings"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	compBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/base"
)

var _ Compiler = (*ApiSingleMode)(nil)

type ApiSingleMode struct {
	baseCompiler *BaseCompiler
	OrigQuery    compBase.ApiQuery
}

func (m ApiSingleMode) Compile(ctx context.Context) (compiledApi *compBase.CompiledApi, err error) {
	// 判断是否需要取总数
	compiledApi = &compBase.CompiledApi{IsPaginate: m.baseCompiler.IsPaginate()}

	compiledApi.Query, err = m.GenSql(ctx)
	if m.OrigQuery.SQL == "" {
		compiledApi.OrigQuery = compiledApi.Query
	}
	if err != nil {
		return nil, err
	}
	// 不需要分页直接返回
	if !compiledApi.IsPaginate {
		return
	}
	compiledApi.TotalQuery, err = m.GenTotalSql(ctx)
	if err != nil {
		return nil, err
	}
	return
}

// GenSql 生成sql
func (m *ApiSingleMode) GenSql(ctx context.Context) (query compBase.ApiQuery, err error) {
	defer trace_utils.StartSpan(ctx, "生成明细sql").End()
	// 1.生成select 字段部分
	// 2.生成表名
	// 3.where 条件
	// 4.order by 排序
	// 5.limit 分页信息
	tableObj := m.baseCompiler.Meta.CreateContent.Tables[0]
	tableName := tableObj.GenPrestoQueryTableName()

	// select
	columns := m.baseCompiler.getFilteredColumns()
	quotedColumns := []string{}
	for _, column := range columns {
		quotedColumns = append(quotedColumns, m.baseCompiler.Engine.GenQuotedField(column))
	}
	query.SQL = fmt.Sprintf("select %s from %s ", strings.Join(quotedColumns, ","), tableName)

	// where
	whereStr, whereArgs := m.genWhere()
	if whereStr != "" {
		query.SQL += whereStr
		query.Args = append(query.Args, whereArgs...)
		m.baseCompiler.whereSql = whereStr
		m.baseCompiler.whereArgs = whereArgs
	}

	// order by
	orderSql := m.genOrderBy()
	if orderSql != "" {
		query.SQL += orderSql
	}

	// limit
	limitSql, limitArgs := m.genLimit()
	// 没有分页的情况，默认返回2000条
	if limitSql != "" {
		query.SQL += limitSql
		query.Args = append(query.Args, limitArgs...)
	} else {
		query.SQL += fmt.Sprintf(" limit %d", compBase.MaxPageSize)
	}
	return
}

// GenTotalSql 生成求总数sql
func (m *ApiSingleMode) GenTotalSql(ctx context.Context) (totalQuery compBase.ApiQuery, err error) {
	defer trace_utils.StartSpan(ctx, "生成取总数sql").End()
	tableObj := m.baseCompiler.Meta.CreateContent.Tables[0]
	tableName := tableObj.GenPrestoQueryTableName()
	totalQuery.SQL = fmt.Sprintf("select count(*) as %s from %s ", base.TotalNumName, tableName)
	if m.baseCompiler.whereSql != "" {
		totalQuery.SQL += m.baseCompiler.whereSql
		totalQuery.Args = append(totalQuery.Args, m.baseCompiler.whereArgs...)
	}
	return
}

func (m *ApiSingleMode) genWhere() (whereStr string, whereArgs []interface{}) {
	// where的字段必须是dbParam的参数
	whereList := []string{}

	for _, input := range m.baseCompiler.InputParams {
		v, ok := m.baseCompiler.reqParamMap[input.Name]
		// 用户本身没有定义,忽略
		if !ok || v.Source == string(base.SourceFieldSystem) {
			continue
		}
		// 特殊处理in
		if v.Operator != string(base.OperatorIn) {
			whereList = append(whereList, m.baseCompiler.Engine.GenWhereFragment(v.Name, v.Operator, "?"))
			whereArgs = append(whereArgs, input.Value)
			continue
		}
		// 用,号分割值
		valueList := strings.Split(input.Value.(string), ",")
		placeholderStr := ""
		for _, value := range valueList {
			whereArgs = append(whereArgs, value)
			if placeholderStr == "" {
				placeholderStr = "?"
				continue
			}
			placeholderStr += ",?"
		}
		whereList = append(whereList, m.baseCompiler.Engine.GenWhereInFragment(v.Name, v.Operator, placeholderStr))
	}

	if len(whereList) == 0 {
		return
	}
	whereStr = fmt.Sprintf(" where %s", strings.Join(whereList, " and "))
	return
}

func (m *ApiSingleMode) genOrderBy() (orderByStr string) {
	// 根据create_content中的值判断
	sorts := m.baseCompiler.Meta.CreateContent.Sorts
	if len(sorts) == 0 {
		return
	}
	for _, sort := range sorts {
		if orderByStr == "" {
			orderByStr = fmt.Sprintf(" order by %s %s", m.baseCompiler.Engine.GenQuotedField(sort.Column), sort.Method)
			continue
		}
		orderByStr += fmt.Sprintf(", %s %s", m.baseCompiler.Engine.GenQuotedField(sort.Column), sort.Method)
	}
	return
}

func (m *ApiSingleMode) genLimit() (limitStr string, limitArgs []interface{}) {
	var pageNum, pageSize int64
	// 检测参数逻辑放在入口位置
	for _, input := range m.baseCompiler.InputParams {
		v, ok := m.baseCompiler.reqParamMap[input.Name]
		// 用户本身没有定义,忽略
		if !ok || v.Source != string(base.SourceFieldSystem) {
			continue
		}
		if v.Name == base.PageNumPublicParam.Name {
			pageNum = input.Value.(int64)
		} else if v.Name == base.PageSizePublicParam.Name {
			pageSize = input.Value.(int64)
		}
	}
	if pageSize == 0 {
		return
	}
	// 第0页没有意义，所以默认第一页
	if pageNum == 0 {
		pageNum = 1
	}
	limitStr, limitArgs = m.baseCompiler.Engine.GenLimitFragment(pageSize, pageNum)
	return
}
