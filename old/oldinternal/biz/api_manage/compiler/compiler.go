package compiler

import (
	"context"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/moql"
	"regexp"
	"strings"

	dap_indicator_modeling "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-indicator-modeling"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	compBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/engine"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"
	"gitlab.mypaas.com.cn/bigdata/vitess/go/vt/sqlparser"
	pkgSqlparse "gitlab.mypaas.com.cn/dmp/gopkg/sqlparse"
)

var questionMarkPattern = regexp.MustCompile(`(:v\d+)`)

type Compiler interface {
	// Compile
	Compile(ctx context.Context) (compiledApi *compBase.CompiledApi, err error)
}

// NewApiCompiler
func NewApiCompiler(project string, resourceType entities.ProjectResourceType, meta *base.ApiDetail, inputParams []*base.InputRequestParam) (Compiler, error) {
	e, err := engine.NewEngine(resourceType)
	if err != nil {
		return nil, err
	}

	baseCompiler := NewBaseCompiler(e, meta, inputParams)
	switch meta.CreateMode {
	case base.ModeView:
		return &ApiSingleMode{baseCompiler: baseCompiler}, nil
	case base.ModeSql:
		return &ApiSqlMode{baseCompiler: baseCompiler, moqlService: moql.NewMoqlServiceS()}, nil
	case base.ModeIndicator:
		apiIndicatorMode := ApiIndicatorMode{
			project:           project,
			resourceType:      resourceType,
			baseCompiler:      baseCompiler,
			indicatorModeling: dap_indicator_modeling.NewIndicatorModelingServiceS(),
		}
		global.Container.MustExtract(&apiIndicatorMode.repo)
		return &apiIndicatorMode, nil
	default:
		return nil, errors.UserError("不支持的api模式")
	}
}

type BaseCompiler struct {
	Engine engine.Engine

	Meta        *base.ApiDetail
	InputParams []*base.InputRequestParam

	// 条件需要共用
	whereSql      string
	whereArgs     []interface{}
	reqParamMap   map[string]*base.ApiParamBase
	inputParamMap map[string]*base.InputRequestParam
}

func NewBaseCompiler(e engine.Engine, meta *base.ApiDetail, inputParams []*base.InputRequestParam) *BaseCompiler {
	baseCompiler := &BaseCompiler{
		Engine:        e,
		Meta:          meta,
		InputParams:   inputParams,
		whereSql:      "",
		whereArgs:     []interface{}{},
		reqParamMap:   map[string]*base.ApiParamBase{},
		inputParamMap: map[string]*base.InputRequestParam{},
	}
	baseCompiler.init()
	return baseCompiler
}

func (bc *BaseCompiler) init() {
	for _, reqParam := range bc.Meta.RequestParams {
		bc.reqParamMap[reqParam.Name] = reqParam
	}
	for _, inputParam := range bc.InputParams {
		bc.inputParamMap[inputParam.Name] = inputParam
	}
}

func (bc *BaseCompiler) removeSQLComments(sql string) string {
	sqls, err := pkgSqlparse.NewSqlParser().SplitStatements(sql)
	if err != nil {
		return sql
	}
	return strings.Join(sqls, ";")
}

func (bc *BaseCompiler) stripSQL(sql string) string {
	sqls := strings.Split(sql, ";")
	var newSqls []string
	for _, sqlFragment := range sqls {
		if sqlFragment == "" {
			continue
		}
		parseStmt, err := sqlparser.Parse(sqlFragment)
		if err != nil {
			return sql
		}
		buf := sqlparser.NewTrackedBuffer(nil)
		parseStmt.Format(buf)
		newSql := buf.String()
		// 某些场景下format可能会把原sql中的with删除, 尝试检测该场景, 此时不使用format后的sql
		if (strings.HasPrefix(strings.TrimSpace(sqlFragment), "with") || strings.HasPrefix(strings.TrimSpace(sqlFragment), "WITH")) && !(strings.HasPrefix(newSql, "WITH") || strings.HasPrefix(newSql, "with")) {
			newSql = sqlFragment
		}
		newSqls = append(newSqls, newSql)
	}

	stripCommentsSql := strings.Join(newSqls, ";")
	// 解决?问号占位符被替换为 :v1, :v2 ... 的问题
	stripCommentsSql = questionMarkPattern.ReplaceAllString(stripCommentsSql, "?")
	return stripCommentsSql
}

// Validate
func (bc *BaseCompiler) Validate() error {
	var LackParams []string
	inputParamMap := map[string]*base.InputRequestParam{}
	for _, inputParam := range bc.InputParams {
		inputParamMap[inputParam.Name] = inputParam
	}
	for _, dbParam := range bc.Meta.RequestParams {
		// 有非必填、默认值不检测
		if dbParam.IsRequired == 0 || dbParam.DefaultValue != "" {
			continue
		}
		if _, ok := inputParamMap[dbParam.Name]; !ok {
			LackParams = append(LackParams, dbParam.Name)
		}
	}
	if len(LackParams) != 0 {
		return utils.NewUserError(fmt.Sprintf("缺少必填参数(且未设置默认值): %s。", strings.Join(LackParams, ",")))
	}
	return nil
}

func (bc *BaseCompiler) getFilteredColumns() []string {
	columns := []string{}
	for _, field := range bc.Meta.ResponseParams {
		if field.Source == string(base.SourceFieldSystem) {
			continue
		}
		columns = append(columns, field.Name)
	}

	// 如果没有设置过滤返回字段，则返回所有字段
	if _, ok := bc.reqParamMap[base.DynamicColumnsPublicParam.Name]; !ok {
		return columns
	}

	var filteredColumns []string
	for _, input := range bc.InputParams {
		if input.Name != base.DynamicColumnsPublicParam.Name {
			continue
		}
		filteredColumns = input.Value.([]string)
	}
	if len(filteredColumns) == 0 {
		return columns
	}
	return filteredColumns
}

// IsPaginate
func (bc *BaseCompiler) IsPaginate() bool {
	return bc.Meta.EnablePage == base.PageEnable
}
