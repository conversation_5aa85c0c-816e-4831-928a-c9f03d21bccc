package engine

import (
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"strings"

	compBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/base"
)

type Mysql struct{}

func (m *Mysql) GetDialectType() global.DialectType {
	return global.MysqlDialect
}

func (m *Mysql) GenQuotedField(fieldName string) (fragment string) {
	return fmt.Sprintf("`%s`", strings.Trim(fieldName, "`"))
}

func (m *Mysql) GenLimitFragment(pageSize, pageNum int64) (fragment string, args []interface{}) {
	fragment = " limit ? offset ?"
	args = append(args, pageSize, (pageNum-1)*pageSize)
	return
}

func (m *Mysql) GenSelectAllWithLimit(querySql string, filterColumns []string, pageSize, pageNum int64, orderBy string) (fragment string, args []interface{}) {
	selectColumns := "*"
	if len(filterColumns) > 0 {
		// 如果由过滤字段，则采用子查询
		quotedColumns := []string{}
		for _, column := range filterColumns {
			quotedColumns = append(quotedColumns, m.GenQuotedField(column))
		}
		selectColumns = strings.Join(quotedColumns, ",")
	}
	fragment = fmt.Sprintf("select %s from (%s) t %s limit ? offset ?", selectColumns, querySql, orderBy)
	args = append(args, pageSize, (pageNum-1)*pageSize)
	return
}

func (m *Mysql) GenSelectAllWithSysLimit(querySql string, filterColumns []string, orderBy string) (fragment string, args []interface{}) {
	selectColumns := "*"
	if len(filterColumns) > 0 {
		// 如果由过滤字段，则采用子查询
		quotedColumns := []string{}
		for _, column := range filterColumns {
			quotedColumns = append(quotedColumns, m.GenQuotedField(column))
		}
		selectColumns = strings.Join(quotedColumns, ",")
	}
	fragment = fmt.Sprintf("select %s from (%s) t %s limit %d", selectColumns, querySql, orderBy, compBase.MaxPageSize)
	return
}

func (m *Mysql) GenWhereFragment(fieldName, operator, placeholder string) (fragment string) {
	return fmt.Sprintf("%s %s %s", m.GenQuotedField(fieldName), operator, placeholder)
}

func (m *Mysql) GenWhereInFragment(fieldName, operator, placeholder string) (fragment string) {
	return fmt.Sprintf("%s %s (%s)", m.GenQuotedField(fieldName), operator, placeholder)
}
