package engine

import (
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	compBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/base"
	"strings"
)

type DmSql struct{}

func (m *DmSql) GetDialectType() global.DialectType {
	return global.DmsqlDialect
}

func (m *DmSql) GenQuotedField(fieldName string) (fragment string) {
	return fmt.Sprintf("\"%s\"", strings.Trim(fieldName, "\""))
}

func (m *DmSql) GenLimitFragment(pageSize, pageNum int64) (fragment string, args []interface{}) {
	fragment = " limit ? offset ?"
	args = append(args, pageSize, (pageNum-1)*pageSize)
	return
}

func (m *DmSql) GenSelectAllWithLimit(querySql string, filterColumns []string, pageSize, pageNum int64, orderBy string) (fragment string, args []interface{}) {
	selectColumns := "*"
	if len(filterColumns) > 0 {
		// 如果由过滤字段，则采用子查询
		quotedColumns := []string{}
		for _, column := range filterColumns {
			quotedColumns = append(quotedColumns, m.GenQuotedField(column))
		}
		selectColumns = strings.Join(quotedColumns, ",")
	}
	fragment = fmt.Sprintf("select %s from (%s) t %s limit ? offset ?", selectColumns, querySql, orderBy)
	args = append(args, pageSize, (pageNum-1)*pageSize)
	return
}

func (m *DmSql) GenSelectAllWithSysLimit(querySql string, filterColumns []string, orderBy string) (fragment string, args []interface{}) {
	selectColumns := "*"
	if len(filterColumns) > 0 {
		// 如果由过滤字段，则采用子查询
		quotedColumns := []string{}
		for _, column := range filterColumns {
			quotedColumns = append(quotedColumns, m.GenQuotedField(column))
		}
		selectColumns = strings.Join(quotedColumns, ",")
	}
	fragment = fmt.Sprintf("select %s from (%s) t %s limit %d", selectColumns, querySql, orderBy, compBase.MaxPageSize)
	return
}

func (m *DmSql) GenWhereFragment(fieldName, operator, placeholder string) (fragment string) {
	return fmt.Sprintf("%s %s %s", m.GenQuotedField(fieldName), operator, placeholder)
}

func (m *DmSql) GenWhereInFragment(fieldName, operator, placeholder string) (fragment string) {
	return fmt.Sprintf("%s %s (%s)", m.GenQuotedField(fieldName), operator, placeholder)
}
