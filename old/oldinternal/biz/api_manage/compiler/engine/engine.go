package engine

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/apierr"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

type Engine interface {
	// GenQuotedField生成带占位符的字段名
	GenQuotedField(fieldName string) (fragment string)
	// GenLimitFragment
	GenLimitFragment(pageSize, pageNum int64) (fragment string, args []interface{})
	// GenSelectAllWithLimit
	GenSelectAllWithLimit(querySql string, filterColumns []string, pageSize, pageNum int64, orderBy string) (fragment string, args []interface{})
	// GenSelectAllWithSysLimit
	GenSelectAllWithSysLimit(querySql string, filterColumns []string, orderBy string) (fragment string, args []interface{})
	// GenWhereFragment
	GenWhereFragment(fieldName, operator, placeholder string) (fragment string)
	// GenWhereInFragment
	GenWhereInFragment(fieldName, operator, placeholder string) (fragment string)
	// 获取方言类型
	GetDialectType() global.DialectType
}

// NewEngine
func NewEngine(resourceType entities.ProjectResourceType) (engine Engine, err error) {
	switch resourceType {
	case entities.RDSResourceType, entities.RDSSaaSResourceType, entities.StarRocksResourceType, entities.StarRocksSaaSResourceType:
		engine = &Mysql{}
	case entities.DamengResourceType, entities.DamengSaaSResourceType:
		engine = &DmSql{}
	default:
		return nil, apierr.NewCommonUserErrf("资源类型不存在")
	}
	return
}

func NewEngineByDialectType(dialectType global.DialectType) Engine {
	switch dialectType {
	case global.DmsqlDialect:
		return &DmSql{}
	default:
		return &Mysql{}
	}
}
