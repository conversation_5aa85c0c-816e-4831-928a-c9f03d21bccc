package compiler

import (
	"context"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/moql"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	compBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/dialect"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/trace_utils"
	"strings"
)

var _ Compiler = (*ApiSqlMode)(nil)

type ApiSqlMode struct {
	baseCompiler *BaseCompiler
	Query        compBase.ApiQuery
	OrigQuery    compBase.ApiQuery
	TotalQuery   compBase.ApiQuery
	moqlService  moql.MoqlService
}

func (m *ApiSqlMode) Compile(ctx context.Context) (compiledApi *compBase.CompiledApi, err error) {
	// 判断是否需要取总数
	compiledApi = &compBase.CompiledApi{IsPaginate: m.baseCompiler.IsPaginate()}

	compiledApi.Query, err = m.GenSql(ctx)
	if m.OrigQuery.SQL == "" {
		compiledApi.OrigQuery = m.Query
	} else {
		compiledApi.OrigQuery = m.OrigQuery
	}

	if err != nil {
		return
	}

	// 不需要分页直接返回
	if !compiledApi.IsPaginate {
		return
	}
	compiledApi.TotalQuery, err = m.GenTotalSql(ctx)
	if err != nil {
		return
	}
	return
}

// GenSql 生成普通sql
func (m *ApiSqlMode) GenSql(ctx context.Context) (query compBase.ApiQuery, err error) {
	defer trace_utils.StartSpan(ctx, "生成明细sql").End()
	// 抽取当前环境需要使用的sql方言
	var querySql string
	var finalDialectType = m.baseCompiler.Engine.GetDialectType()
	var engineDialectType = finalDialectType
	var dialectSetting = m.baseCompiler.Meta.CreateContent.DialectSetting
	switch dialectSetting {
	case global.MoSqlDialectSetting:
		querySql = m.baseCompiler.Meta.CreateContent.OriginMoSql
		engineDialectType = global.MysqlDialect
	case global.MultiSqlDialectSetting:
		if finalDialectType == global.DmsqlDialect {
			querySql = m.baseCompiler.Meta.CreateContent.OriginDmSql
		} else {
			querySql = m.baseCompiler.Meta.CreateContent.OriginMySql
		}
	default:
		err = errors.UserError("不支持的方言类型")
		return
	}

	// sql不能为空
	if querySql == "" {
		err = errors.UserError("查询语句不能为空")
		return
	}
	var dialectCtx = &dialect.Context{
		DialectType:    engineDialectType,
		OriginSql:      querySql,
		InputParams:    m.baseCompiler.InputParams,
		RequestParams:  m.baseCompiler.Meta.RequestParams,
		ResponseParams: m.baseCompiler.Meta.ResponseParams,
	}
	var options = make([]dialect.Option, 0)
	options = append(options, dialect.WithEnablePaginate(m.baseCompiler.IsPaginate()))
	if m.Query.SQL, m.Query.Args, err = dialect.NewSqlDialectCompiler(dialectCtx, options...).Compile(ctx); err != nil {
		return
	}
	// 如果是moql，则还需要翻译一次
	if dialectSetting == global.MoSqlDialectSetting {
		var translateDialect moql.MoqlDialect
		switch finalDialectType {
		case global.DmsqlDialect:
			translateDialect = moql.DmDialect
		default:
			translateDialect = moql.OtherDialect
		}
		// 先检测sql
		var validateResult *moql.ValidateResult
		if validateResult, err = m.moqlService.Validate(ctx, m.Query.SQL, false); err != nil {
			return
		}
		if !validateResult.Success {
			err = errors.UserError(strings.Join(validateResult.Errors, "\n"))
			return
		}
		// 再翻译sql
		var translateResult *moql.TranslateResult
		if translateResult, err = m.moqlService.Translate(ctx, translateDialect, m.Query.SQL); err != nil {
			return
		}
		if !translateResult.Success {
			err = errors.UserError(strings.Join(translateResult.Errors, "\n"))
			return
		}
		m.Query.SQL = translateResult.Sql
	}
	m.OrigQuery = m.Query
	// 返回最终查询query对象
	query = m.Query
	return
}

// GenTotalSql 生成取总数sql
func (m *ApiSqlMode) GenTotalSql(ctx context.Context) (totalQuery compBase.ApiQuery, err error) {
	defer trace_utils.StartSpan(ctx, "生成取总数sql").End()
	totalQuery.SQL = fmt.Sprintf("select count(*) as %s from (%s) tmp", base.TotalNumName, m.OrigQuery.SQL)
	totalQuery.Args = m.OrigQuery.Args
	return
}
