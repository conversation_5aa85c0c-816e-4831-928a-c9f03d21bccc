package compiler

var rawsqls = []string{`
WITH temp as
         (select sf.ContractType, sf.RmbAmount, sf.BcxyTotal, tf.ContractQsDate AS ActualFinishTime, room.DjTotal
          from data_wide_mdm_building as building
                   join data_wide_s_room room on room.MasterBldGUID = building.BuildingGUID
                   left join data_wide_s_salehsdata sf on sf.RoomGUID = room.RoomGUID and sf.STATUS = '激活'
                   left join data_wide_s_BaseTrade tf on tf.RoomGUID = room.RoomGUID and tf.TradeStatus = '激活'
          where building.ManagementAttributes = '可售'
            and building.StageGuid is not null
            and (room.DjTotal > 0 OR room.BcxyTotal > 0)
            and building.ProjectGuid is not null
            and BuildingGUID IN ('')),
     acc AS (SELECT ActualFinishTime,
                    SUM(RmbAmount) OVER (ORDER BY ActualFinishTime rows UNBOUNDED PRECEDING) as AccRMB
             FROM temp
             WHERE ContractType = '网签'),
     overall AS (SELECT SUM(DjTotal) + SUM(BcxyTotal) as OverDJ_BCXY
                 FROM temp),
     a AS (SELECT CONVERT(AccRMB / OverDJ_BCXY, decimal(18, 4)) AS rate,
                  ActualFinishTime
           FROM acc,
                overall)
SELECT a.ActualFinishTime, a.Rate, null ContractType, null RmbAmount, null BcxyTotal, null DjTotal
FROM a
WHERE a.Rate >= ifnull(1, 100)
ORDER BY a.Rate ASC
LIMIT 0,1;
`,
	`
with sub1 as (SELECT dcp.ProviderGUID,
                           dcp.ProviderName,
                           dcp.Corporation,
                           dcp.RegisterFund,
                           dcp.WorkStartDate,
                           dcp.LogoFile,
                           IFNULL(main.WinBidRate, 0)   AS WinBidRate,
                           IFNULL(main.WinBidAmount, 0) AS WinBidAmount,
                           IFNULL(main.WinBidCount, 0)  AS WinBidCount
                    FROM (SELECT c.ProviderGUID,
                                 CASE
                                     WHEN c.FinalistTimes = 0 THEN
                                         0
                                     ELSE
                                         (c.WinBidCount * 1.0) / c.FinalistTimes
                                     END AS WinBidRate,
                                 c.WinBidAmount,
                                 c.WinBidCount
                          FROM (SELECT a.ProviderGUID,
                                       sum(
                                               CASE
                                                   WHEN a.WinBidCount > 0 THEN
                                                       1
                                                   ELSE
                                                       0
                                                   END
                                           )                  WinBidCount,
                                       sum(
                                               CASE
                                                   WHEN a.FinalistTimes > 0 THEN
                                                       1
                                                   ELSE
                                                       0
                                                   END
                                           )               AS FinalistTimes,
                                       sum(a.WinBidAmount) AS WinBidAmount
                                FROM (SELECT dcsp.ProviderGUID,
                                             SUM(dcsp.IsWinBid)             AS WinBidCount,
                                             COUNT(1)                       AS FinalistTimes,
                                             SUM(dcsp.WinBidAmount) / 10000 AS WinBidAmount
                                      FROM dwd_cg_cgsolutionprovider dcsp
                                      WHERE dcsp.CgSolutionGUID in (SELECT dws.CgSolutionGUID
                                                                    FROM dwd_cg_cgsolution dws
                                                                    WHERE dws.CgSolutionState = 2
                                                                      AND dws.IsTacticCg = 0)
                                      GROUP BY dcsp.ProviderGUID,
                                               dcsp.CgSolutionGUID) a
                                GROUP BY a.ProviderGUID) c) main
                             RIGHT JOIN dwd_cg_provider dcp ON dcp.ProviderGUID = main.ProviderGUID),
    t1 as (SELECT dcp.ProviderGUID,
                   dcp.ProviderName,
                   dcp.Corporation,
                   dcp.RegisterFund,
                   dcp.WorkStartDate,
                   dcp.LogoFile,
                   IFNULL(main.WinBidRate, 0)   AS WinBidRate,
                   IFNULL(main.WinBidAmount, 0) AS WinBidAmount,
                   IFNULL(main.WinBidCount, 0)  AS WinBidCount
            FROM (SELECT c.ProviderGUID,
                         CASE
                             WHEN c.FinalistTimes = 0 THEN
                                 0
                             ELSE
                                 (c.WinBidCount * 1.0) / c.FinalistTimes
                             END AS WinBidRate,
                         c.WinBidAmount,
                         c.WinBidCount
                  FROM (SELECT a.ProviderGUID,
                               sum(
                                       CASE
                                           WHEN a.WinBidCount > 0 THEN
                                               1
                                           ELSE
                                               0
                                           END
                                   )                  WinBidCount,
                               sum(
                                       CASE
                                           WHEN a.FinalistTimes > 0 THEN
                                               1
                                           ELSE
                                               0
                                           END
                                   )               AS FinalistTimes,
                               sum(a.WinBidAmount) AS WinBidAmount
                        FROM (SELECT dcsp.ProviderGUID,
                                     SUM(dcsp.IsWinBid)             AS WinBidCount,
                                     COUNT(1)                       AS FinalistTimes,
                                     SUM(dcsp.WinBidAmount) / 10000 AS WinBidAmount
                              FROM dwd_cg_cgsolutionprovider dcsp
                              WHERE dcsp.CgSolutionGUID in (SELECT dws.CgSolutionGUID
                                                            FROM dwd_cg_cgsolution dws
                                                            WHERE dws.CgSolutionState = 2
                                                              AND dws.IsTacticCg = 0)
                              GROUP BY dcsp.ProviderGUID,
                                       dcsp.CgSolutionGUID) a
                        GROUP BY a.ProviderGUID) c) main
                     RIGHT JOIN dwd_cg_provider dcp ON dcp.ProviderGUID = main.ProviderGUID),
    sub2 as (SELECT dcp.ProviderGUID,
                           dcp.ProviderName,
                           dcp.Corporation,
                           dcp.RegisterFund,
                           dcp.WorkStartDate,
                           dcp.LogoFile,
                           IFNULL(main.WinBidRate, 0)   AS WinBidRate,
                           IFNULL(main.WinBidAmount, 0) AS WinBidAmount,
                           IFNULL(main.WinBidCount, 0)  AS WinBidCount
                    FROM (SELECT c.ProviderGUID,
                                 c.WinBidRate,
                                 c.WinBidAmount,
                                 c.WinBidCount
                          FROM (SELECT a.ProviderGUID,
                                       CASE
                                           WHEN a.FinalistTimes = 0 THEN
                                               0
                                           ELSE
                                               (a.WinBidCount * 1.0) / a.FinalistTimes
                                           END AS WinBidRate,
                                       a.WinBidAmount,
                                       a.WinBidCount
                                FROM (SELECT dcsp.ProviderGUID,
                                             SUM(dcsp.IsWinBid)             AS WinBidCount,
                                             COUNT(1)                       AS FinalistTimes,
                                             SUM(dcsp.WinBidAmount) / 10000 AS WinBidAmount
                                      FROM dwd_cg_cgsolutionprovider dcsp
                                      WHERE dcsp.CgSolutionGUID in (SELECT dws.CgSolutionGUID
                                                                    FROM dwd_cg_cgsolution dws
                                                                    WHERE dws.CgSolutionState = 2
                                                                      AND dws.IsTacticCg = 0)
                                      GROUP BY dcsp.ProviderGUID) a) c) main
                             RIGHT JOIN dwd_cg_provider dcp ON dcp.ProviderGUID = main.ProviderGUID),
    t2 as (SELECT dcp.ProviderGUID,
                   dcp.ProviderName,
                   dcp.Corporation,
                   dcp.RegisterFund,
                   dcp.WorkStartDate,
                   dcp.LogoFile,
                   IFNULL(main.WinBidRate, 0)   AS WinBidRate,
                   IFNULL(main.WinBidAmount, 0) AS WinBidAmount,
                   IFNULL(main.WinBidCount, 0)  AS WinBidCount
            FROM (SELECT c.ProviderGUID,
                         c.WinBidRate,
                         c.WinBidAmount,
                         c.WinBidCount
                  FROM (SELECT a.ProviderGUID,
                               CASE
                                   WHEN a.FinalistTimes = 0 THEN
                                       0
                                   ELSE
                                       (a.WinBidCount * 1.0) / a.FinalistTimes
                                   END AS WinBidRate,
                               a.WinBidAmount,
                               a.WinBidCount
                        FROM (SELECT dcsp.ProviderGUID,
                                     SUM(dcsp.IsWinBid)             AS WinBidCount,
                                     COUNT(1)                       AS FinalistTimes,
                                     SUM(dcsp.WinBidAmount) / 10000 AS WinBidAmount
                              FROM dwd_cg_cgsolutionprovider dcsp
                              WHERE dcsp.CgSolutionGUID in (SELECT dws.CgSolutionGUID
                                                            FROM dwd_cg_cgsolution dws
                                                            WHERE dws.CgSolutionState = 2
                                                              AND dws.IsTacticCg = 0)
                              GROUP BY dcsp.ProviderGUID) a) c) main
                     RIGHT JOIN dwd_cg_provider dcp ON dcp.ProviderGUID = main.ProviderGUID)
SELECT ProviderGUID,
       ProviderName,
       Corporation,
       RegisterFund,
       WorkStartDate,
       WinBidRate,
       WinBidAmount,
       WinBidCount,
       LogoFile
FROM (SELECT ProviderGUID,
             ProviderName,
             Corporation,
             RegisterFund,
             WorkStartDate,
             WinBidRate,
             t.WinBidAmount,
             WinBidCount,
             LogoFile,
             sum(count_WinBidAmount) as num -- ROW_NUMBER () OVER (ORDER BY WinBidAmount DESC) AS num
      FROM t1 as t left join (select count(*) as count_WinBidAmount, WinBidAmount from sub1 group by WinBidAmount) sub on sub.WinBidAmount >= t.WinBidAmount
      WHERE t.Corporation LIKE CONCAT('%', '', '%')
         OR t.ProviderName LIKE CONCAT('%', '', '%')
      group by t.ProviderGUID
      ) s
WHERE num BETWEEN ''
    AND ''
  AND '' = 'Cg'
UNION
SELECT ProviderGUID,
       ProviderName,
       Corporation,
       RegisterFund,
       WorkStartDate,
       WinBidRate,
       WinBidAmount,
       WinBidCount,
       LogoFile
FROM (SELECT ProviderGUID,
             ProviderName,
             Corporation,
             RegisterFund,
             WorkStartDate,
             WinBidRate,
             t.WinBidAmount,
             WinBidCount,
             LogoFile,
             sum(count_WinBidAmount) as num -- ROW_NUMBER () OVER (ORDER BY WinBidAmount DESC) AS num
      FROM t2 as t left join (select count(*) as count_WinBidAmount, WinBidAmount from sub2 group by WinBidAmount) sub on sub.WinBidAmount >= t.WinBidAmount
      WHERE t.Corporation LIKE CONCAT('%', '', '%')
         OR t.ProviderName LIKE CONCAT('%', '', '%')
      group by t.ProviderGUID
      ) s
WHERE num BETWEEN 1
    AND 2
  AND 'Cg' <> 'Cg'
`}

//func TestSqlParserFormat(t *testing.T) {
//	e, err := engine.NewEngine(entities.StarRocksResourceType)
//	if err != nil {
//		fmt.Println(err.Error())
//	}
//
//	for _, rawSql := range rawsqls {
//		c := NewBaseCompiler(e, &base.ApiDetail{
//			CreateContent: base.ApiCreateContent{MySql: rawSql},
//		}, []*base.InputRequestParam{})
//
//		rv := c.stripSQL(rawSql)
//		fmt.Println(rv)
//	}
//}
