package compiler

import (
	"context"
	"encoding/json"
	"fmt"
	dap_indicator_modeling "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-indicator-modeling"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/apierr"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/trace_utils"
	"strings"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_query"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	compBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/repo"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"
)

var _ Compiler = (*ApiIndicatorMode)(nil)

type ApiIndicatorMode struct {
	baseCompiler      *BaseCompiler
	project           string
	resourceType      entities.ProjectResourceType
	OrigQuery         compBase.ApiQuery
	indicatorModeling dap_indicator_modeling.IndicatorModeling
	repo              repo.IndicatorRepo
	TableName         string
}

func (m *ApiIndicatorMode) Compile(ctx context.Context) (compiledApi *compBase.CompiledApi, err error) {
	// 判断是否需要取总数
	tableName, err := m.getTableName(ctx)
	if err != nil {
		return
	}

	m.TableName = tableName
	compiledApi = &compBase.CompiledApi{IsPaginate: m.baseCompiler.IsPaginate()}

	compiledApi.Query, err = m.GenSql(ctx)
	if m.OrigQuery.SQL == "" {
		compiledApi.OrigQuery = compiledApi.Query
	}
	if err != nil {
		return nil, err
	}
	// 不需要分页直接返回
	if !compiledApi.IsPaginate {
		return
	}
	compiledApi.TotalQuery, err = m.GenTotalSql(ctx)
	if err != nil {
		return nil, err
	}
	return
}

func (m *ApiIndicatorMode) genSelectIndicators() (items []*indicator_query.QueryItem) {
	returnColumns := []string{}
	if _, ok := m.baseCompiler.reqParamMap[base.DynamicColumnsPublicParam.Name]; ok {
		for _, input := range m.baseCompiler.InputParams {
			if input.Name == base.DynamicColumnsPublicParam.Name {
				returnColumns = input.Value.([]string)
			}
		}
	}
	for _, param := range m.baseCompiler.Meta.ResponseParams {
		if param.ParamType == base.Indicator {
			if len(returnColumns) > 0 && !utils.Contain(param.Name, returnColumns) {
				continue
			}
			items = append(items, &indicator_query.QueryItem{
				Expression: &indicator_query.Expression{
					Type: indicator_query.ExpressionType_Field,
					FieldContent: &indicator_query.FieldContent{
						FieldType: indicator_query.FieldType_Indicator,
						Code:      param.IndicatorCode,
					},
				},
				Alias: param.Name,
			})
		}
	}
	return
}

func (m *ApiIndicatorMode) genSelectDimensions() (items []*indicator_query.QueryItem) {
	returnColumns := []string{}
	if _, ok := m.baseCompiler.reqParamMap[base.DynamicColumnsPublicParam.Name]; ok {
		for _, input := range m.baseCompiler.InputParams {
			if input.Name == base.DynamicColumnsPublicParam.Name {
				returnColumns = input.Value.([]string)
			}
		}
	}
	for _, param := range m.baseCompiler.Meta.ResponseParams {
		if param.ParamType == base.Dimension {
			if len(returnColumns) > 0 && !utils.Contain(param.Name, returnColumns) {
				continue
			}

			items = append(items, &indicator_query.QueryItem{
				Expression: &indicator_query.Expression{
					Type: indicator_query.ExpressionType_Field,
					FieldContent: &indicator_query.FieldContent{
						FieldType: indicator_query.FieldType_Dimension,
						TableName: m.TableName,
						FieldName: param.Name,
					},
				},
				Alias: param.Name,
			})
		}
	}
	return
}

// GenSql 生成sql
func (m *ApiIndicatorMode) GenSql(ctx context.Context) (query compBase.ApiQuery, err error) {
	defer trace_utils.StartSpan(ctx, "生成明细sql").End()
	limit, offset := m.getLimit()
	// 生成聚合sql
	sql, err := m.indicatorModeling.GenQueryDataSql(ctx, &indicator_query.QueryDataRequest{
		Query: &indicator_query.Query{
			Indicators:   m.genSelectIndicators(),
			Dimensions:   m.genSelectDimensions(),
			Sorts:        m.genSorts(),
			Filter:       m.genWhere(),
			HavingFilter: m.genHaving(),
			Limit: &indicator_query.Limit{
				Offset: offset,
				Limit:  limit,
			},
			TableName: m.TableName,
			Mode:      indicator_query.QueryMode_Agg,
		},
		Engine:        string(m.resourceType),
		ProjectCode:   m.project,
		TenantCode:    m.getTenantCode(),
		WithVariables: m.genVariable(),
	})
	if err != nil {
		return
	}
	query.SQL = sql
	return
}

func (m *ApiIndicatorMode) getTenantCode() string {
	if m.resourceType != entities.StarRocksSaaSResourceType {
		return m.project
	}
	for _, input := range m.baseCompiler.InputParams {
		if input.Name == base.BigdataTenantCodePublicParam.Name {
			return input.Value.(string)
		}
	}
	return ""
}

// GenTotalSql 生成求总数sql
func (m *ApiIndicatorMode) GenTotalSql(ctx context.Context) (totalQuery compBase.ApiQuery, err error) {
	defer trace_utils.StartSpan(ctx, "生成取总数sql").End()
	sql, err := m.indicatorModeling.GenQueryDataSql(ctx, &indicator_query.QueryDataRequest{
		Query: &indicator_query.Query{
			Indicators:   m.genSelectIndicators(),
			Dimensions:   m.genSelectDimensions(),
			Filter:       m.genWhere(),
			HavingFilter: m.genHaving(),
			TableName:    m.TableName,
			Mode:         indicator_query.QueryMode_Count,
		},
		Engine:        string(m.resourceType),
		ProjectCode:   m.project,
		TenantCode:    m.getTenantCode(),
		WithVariables: m.genVariable(),
	})
	if err != nil {
		return
	}
	totalQuery.SQL = sql
	return
}

func (m *ApiIndicatorMode) genHaving() (filter *indicator_query.Filter) {
	subFilters := []*indicator_query.Filter{}

	for _, input := range m.baseCompiler.InputParams {
		v, ok := m.baseCompiler.reqParamMap[input.Name]
		// 用户本身没有定义,忽略
		if !ok || v.Source == string(base.SourceFieldSystem) || v.ParamType != base.Indicator {
			continue
		}
		subject := &indicator_query.Expression{
			Type: indicator_query.ExpressionType_Field,
			FieldContent: &indicator_query.FieldContent{
				FieldType: indicator_query.FieldType_Indicator,
				Code:      v.IndicatorCode,
			},
		}

		switch v.Operator {
		case string(base.OperatorIn):
			subFilter := &indicator_query.Filter{
				Type:      indicator_query.FilterType_Predicate,
				Subject:   subject,
				Predicate: m.genInListPredicate(input.Value.(string)),
			}
			subFilters = append(subFilters, subFilter)
		case string(base.OperatorLike):
			subFilter := &indicator_query.Filter{
				Type:      indicator_query.FilterType_Predicate,
				Subject:   subject,
				Predicate: m.genLikePredicate(input.Value.(string)),
			}
			subFilters = append(subFilters, subFilter)
		default:
			subFilter := &indicator_query.Filter{
				Type:      indicator_query.FilterType_Predicate,
				Subject:   subject,
				Predicate: m.genEqPredicate(v.DataType, input.Value),
			}
			subFilters = append(subFilters, subFilter)
		}
	}

	if len(subFilters) > 0 {
		filter = &indicator_query.Filter{
			Type:          indicator_query.FilterType_FilterGroup,
			AssociateType: indicator_query.AssociateType_And,
			SubFilters:    subFilters,
		}
	}

	return
}

func (m *ApiIndicatorMode) genWhere() (filter *indicator_query.Filter) {
	subFilters := []*indicator_query.Filter{}

	for _, input := range m.baseCompiler.InputParams {
		v, ok := m.baseCompiler.reqParamMap[input.Name]
		// 用户本身没有定义,忽略
		if !ok || v.Source == string(base.SourceFieldSystem) || v.ParamType != base.Dimension {
			continue
		}

		subject := &indicator_query.Expression{
			Type: indicator_query.ExpressionType_Field,
			FieldContent: &indicator_query.FieldContent{
				FieldType: indicator_query.FieldType_Dimension,
				TableName: m.TableName,
				FieldName: input.Name,
			},
		}

		switch v.Operator {
		case string(base.OperatorIn):
			subFilter := &indicator_query.Filter{
				Type:      indicator_query.FilterType_Predicate,
				Subject:   subject,
				Predicate: m.genInListPredicate(input.Value.(string)),
			}
			subFilters = append(subFilters, subFilter)
		case string(base.OperatorLike):
			subFilter := &indicator_query.Filter{
				Type:      indicator_query.FilterType_Predicate,
				Subject:   subject,
				Predicate: m.genLikePredicate(input.Value.(string)),
			}
			subFilters = append(subFilters, subFilter)
		default:
			subFilter := &indicator_query.Filter{
				Type:      indicator_query.FilterType_Predicate,
				Subject:   subject,
				Predicate: m.genEqPredicate(v.DataType, input.Value),
			}
			subFilters = append(subFilters, subFilter)
		}
	}
	if len(subFilters) > 0 {
		filter = &indicator_query.Filter{
			Type:          indicator_query.FilterType_FilterGroup,
			AssociateType: indicator_query.AssociateType_And,
			SubFilters:    subFilters,
		}
	}

	return
}

func (m *ApiIndicatorMode) genEqPredicate(_ string, dataValue interface{}) *indicator_query.PredicateContent {
	valueType := indicator_query.ValueType_StringValue
	value := ""
	switch v := dataValue.(type) {
	case string:
		valueType = indicator_query.ValueType_StringValue
		value = v
	case float32, float64:
		valueType = indicator_query.ValueType_FloatValue
		value = fmt.Sprintf("%f", v)
	case int, int64, int32, int16, int8:
		valueType = indicator_query.ValueType_IntegerValue
		value = fmt.Sprintf("%d", v)
	case bool:
		valueType = indicator_query.ValueType_IntegerValue
		if v {
			value = "1"
		} else {
			value = "0"
		}
	}

	return &indicator_query.PredicateContent{
		Type: indicator_query.PredicateType_Comparison,
		Comparison: &indicator_query.ComparisonContent{
			Op: indicator_query.ComparisonOpType_Eq,
			Right: &indicator_query.Expression{
				Type: indicator_query.ExpressionType_Value,
				ValueContent: &indicator_query.ValueContent{
					Type:  valueType,
					Value: value,
				},
			},
		},
	}
}

func (m *ApiIndicatorMode) genLikePredicate(value string) *indicator_query.PredicateContent {
	return &indicator_query.PredicateContent{
		Type: indicator_query.PredicateType_Like,
		Like: &indicator_query.LikeContent{
			Value: &indicator_query.Expression{
				Type: indicator_query.ExpressionType_Value,
				ValueContent: &indicator_query.ValueContent{
					Type:  indicator_query.ValueType_StringValue,
					Value: value,
				},
			},
		},
	}
}

func (m *ApiIndicatorMode) genInListPredicate(value string) *indicator_query.PredicateContent {
	valueList := strings.Split(value, ",")
	values := []*indicator_query.Expression{}
	for _, v := range valueList {
		values = append(values, &indicator_query.Expression{
			Type: indicator_query.ExpressionType_Value,
			ValueContent: &indicator_query.ValueContent{
				Type:  indicator_query.ValueType_StringValue,
				Value: v,
			},
		})
	}

	return &indicator_query.PredicateContent{
		Type: indicator_query.PredicateType_InList,
		InList: &indicator_query.InListContent{
			Values: values,
		},
	}
}

func (m *ApiIndicatorMode) genSorts() (sort []*indicator_query.Sort) {
	// 根据create_content中的值判断
	dynamicColumns := m.getDynamicInputParam()
	if len(dynamicColumns) > 0 {
		ordering := indicator_query.Ordering_Asc
		for _, column := range dynamicColumns {
			if columnNames, ok := column.Value.([]string); ok {
				for _, columnName := range columnNames {
					sort = append(sort, &indicator_query.Sort{
						QueryItem: &indicator_query.QueryItem{
							Alias: columnName,
							Expression: &indicator_query.Expression{
								Type: indicator_query.ExpressionType_Field,
								FieldContent: &indicator_query.FieldContent{
									TableName: m.TableName,
									FieldName: columnName,
								},
							},
						},
						Ordering: ordering,
						ShowType: 1,
					})
				}
			}
		}

		if len(sort) > 0 {
			return
		}
	}

	sorts := m.baseCompiler.Meta.CreateContent.Sorts
	if len(sorts) == 0 {
		return
	}
	for _, item := range sorts {
		if item.Column == "" {
			continue
		}
		ordering := indicator_query.Ordering_NoOrdering
		if strings.ToUpper(item.Method) == "ASC" {
			ordering = indicator_query.Ordering_Asc
		} else {
			ordering = indicator_query.Ordering_Desc
		}
		sort = append(sort, &indicator_query.Sort{
			QueryItem: &indicator_query.QueryItem{
				Alias: item.Column,
				Expression: &indicator_query.Expression{
					Type: indicator_query.ExpressionType_Field,
					FieldContent: &indicator_query.FieldContent{
						TableName: m.TableName,
						FieldName: item.Column,
					},
				},
			},
			Ordering: ordering,
			ShowType: 1,
		})
	}
	return
}

func (m *ApiIndicatorMode) getLimit() (limit int32, offset int32) {
	var pageNum, pageSize int64
	// 检测参数逻辑放在入口位置
	for _, input := range m.baseCompiler.InputParams {
		v, ok := m.baseCompiler.reqParamMap[input.Name]
		// 用户本身没有定义,忽略
		if !ok || v.Source != string(base.SourceFieldSystem) {
			continue
		}
		if v.Name == base.PageNumPublicParam.Name {
			pageNum = input.Value.(int64)
		} else if v.Name == base.PageSizePublicParam.Name {
			pageSize = input.Value.(int64)
		}
	}
	if pageSize <= 0 {
		pageSize = 20
	} else if pageSize >= compBase.MaxPageSize {
		pageSize = compBase.MaxPageSize
	}
	if pageNum <= 0 {
		pageNum = 1
	}
	return int32(pageSize), int32((pageNum - 1) * pageSize)
}

func (m *ApiIndicatorMode) convertResourceTypeToEngineType(resourceType cBase.ProjectResourceType) indicator_query.QueryEngine {
	// 协议转换
	switch resourceType {
	case cBase.StarRocksSaaSResourceType:
		return indicator_query.QueryEngine_StarRocksSaaS
	case cBase.StarRocksResourceType:
		return indicator_query.QueryEngine_StarRocks
	case cBase.RDSResourceType:
		return indicator_query.QueryEngine_RDS
	case cBase.RDSSaaSResourceType:
		return indicator_query.QueryEngine_RDSSaaS
	default:
		return indicator_query.QueryEngine_StarRocks
	}
}

func (m *ApiIndicatorMode) getDynamicInputParam() (items []*base.InputRequestParam) {
	for _, input := range m.baseCompiler.InputParams {
		if input.Name == base.DynamicColumnsPublicParam.Name {
			items = append(items, input)
		}
	}

	return
}

func (m *ApiIndicatorMode) genVariable() []*indicator_query.VariableValue {
	variables := make([]*indicator_query.VariableValue, 0)

	for _, param := range m.baseCompiler.Meta.RequestParams {
		if param.ParamType == base.Variable {
			variable := indicator_query.VariableValue{}
			if inputParam, ok := m.baseCompiler.inputParamMap[param.Name]; ok {
				if param.DataType == string(base.ParamTypeString) {
					variableContent := rpc_call.Variable{}
					if err := json.Unmarshal([]byte(param.VariableContent), &variableContent); err == nil {
						variable.Name = inputParam.Name
						if variableContent.ScopeType == rpc_call.SingleValue {
							variable.ScopeType = indicator_query.ScopeType_single
							if rangeValue, ok := inputParam.Value.(string); ok {
								variable.SingleValueContent = &indicator_query.SingleValueContent{Value: rangeValue}
								variables = append(variables, &variable)
							}
						} else {
							if rangeValue, ok := inputParam.Value.(string); ok {
								values := strings.Split(rangeValue, ",")
								if len(values) == 2 {
									variable.ScopeType = indicator_query.ScopeType_range
									variable.RangeValueContent = &indicator_query.RangeValueContent{Left: values[0], Right: values[1]}
									variables = append(variables, &variable)
								}
							}
						}
					}
				}
			}
		}
	}

	return variables
}

func (m *ApiIndicatorMode) getTableName(ctx context.Context) (tableName string, err error) {
	if len(m.baseCompiler.Meta.CreateContent.ModelCode) == 0 {
		err = apierr.NewCommonUserErrf("视图dws为空")
		return
	}

	indicator, err := m.repo.GetIndicator(ctx, m.project, m.baseCompiler.Meta.CreateContent.ModelCode)
	if err != nil {
		return
	}

	tableName = indicator.TableName
	return
}
