package dialect

import (
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/parser"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors/biz"
	bizPkg "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/interface_utils"
	pkgSqlparse "gitlab.mypaas.com.cn/dmp/gopkg/sqlparse"
	"regexp"
	"strconv"
	"strings"
)

const tempInPatternStr = `\(\$\{%s\}\)`
const tempNormalPatternStr = `\$\{%s\}`
const tempBackQuotePatternStr = `%s*\$\{%s\}%s*`

var validParamPattern = regexp.MustCompile(`^[a-zA-Z_\d\.]+$`)
var withSemicolonPattern = regexp.MustCompile(`^.+;$`)

// RemoveSqlComments 移除SQL注释
func RemoveSqlComments(sql string) (string, error) {
	sqls, err := pkgSqlparse.NewSqlParser().SplitStatements(sql)
	if err != nil {
		return sql, err
	}
	return strings.Join(sqls, ";"), nil
}

// ParseMybatisTags 替换Mybatis Tag标签
func ParseMybatisTags(sql string, inputParams []*base.InputRequestParam) (replacedSql string, err error) {
	paramMap := map[string]interface{}{}
	for _, inputParam := range inputParams {
		paramMap[inputParam.Name] = inputParam.Value
	}

	var tagReplacedSql string
	tagReplacedSql, err = parser.ReplaceMyBatisTag(sql, paramMap)
	if err != nil {
		err = errors.Wrap(biz.API_MYBATIS_PARSE_ERR, err)
		return
	}
	replacedSql = tagReplacedSql
	return
}

// GetParams 获取解析参数
func GetParams(sql string) (params []*parser.ParsedParam, err error) {
	params = parser.ParseParams(sql)
	return
}

// ValidateParams 校验参数
func ValidateParams(inputParams []*base.InputRequestParam, params []*parser.ParsedParam) (err error) {
	parsedParamMap := make(map[string]*parser.ParsedParam)
	for _, p := range params {
		parsedParamMap[p.ParamName] = p
	}

	// 校验参数值，防止字段名称sql注入
	for _, input := range inputParams {
		param, ok := parsedParamMap[input.Name]
		if ok && param.IsField {
			if !validParamPattern.MatchString(input.Value.(string)) {
				return errors.New(biz.API_ILLEGAL_PARAM_ERR, input.Value.(string))
			}
		}
	}
	return
}

// ReplaceParams 替换参数
func ReplaceParams(sql string, params []*parser.ParsedParam, inputParams []*base.InputRequestParam, reqParams []*base.ApiParamBase) (
	querySql string, args []interface{}, err error) {
	inputValueMap := make(map[string]interface{})
	inputTypeMap := make(map[string]string)
	for _, param := range reqParams {
		inputTypeMap[param.Name] = param.DataType
	}
	for _, p := range inputParams {
		inputValueMap[p.Name] = p.Value
	}
	args = make([]interface{}, 0)
	for _, p := range params {
		value, ok := inputValueMap[p.ParamName]

		var placeholder, replacePatternStr string
		if strings.EqualFold(strings.ToLower(p.Operator), "in") {
			// 特殊处理IN操作符
			var qmark []string
			realParamType, paramNameExist := inputTypeMap[p.ParamName]
			if !ok {
				args = append(args, getNullElement(realParamType))
				qmark = append(qmark, "?")
			} else {
				valueString, _ := bizPkg.ValueToString(value)
				splitValue := strings.Split(valueString, ",")

				needTransferType := false
				if paramNameExist && (realParamType != string(bizPkg.StringType)) {
					needTransferType = true
				}
				for _, v := range splitValue {
					args = append(args, transferParamType(needTransferType, realParamType, v))
					qmark = append(qmark, "?")
				}
			}
			placeholder = fmt.Sprintf("(%s)", strings.Join(qmark, ","))
			replacePatternStr = fmt.Sprintf(tempInPatternStr, p.ParamName)
		} else if p.IsField {
			// 参数识别为字段，则需要反引号
			placeholder, replacePatternStr = GetBackQuoteReplaceStr(p.ParamName, value)
		} else {
			// 正常场景
			if !ok {
				value = ""
			}
			args = append(args, value)
			placeholder = "?"
			replacePatternStr = fmt.Sprintf(tempNormalPatternStr, p.ParamName)
		}

		sql = replacePatternOnce(sql, replacePatternStr, placeholder)
	}
	querySql = sql
	return
}

func GetBackQuoteReplaceStr(param string, value interface{}) (placeholder, replacePatternStr string) {
	placeholder = fmt.Sprintf("`%s`", value)
	replacePatternStr = fmt.Sprintf(tempBackQuotePatternStr, "`", param, "`")
	return
}

func getNullElement(realParamType string) interface{} {
	switch realParamType {
	case string(bizPkg.IntType), string(bizPkg.LongType):
		return 0
	case string(bizPkg.FloatType), string(bizPkg.DoubleType):
		return 0
	case string(bizPkg.BooleanType):
		return false
	default:
		return ""
	}
}

func transferParamType(needTransferType bool, realParamType, v string) interface{} {
	if !needTransferType {
		return v
	}
	var vi interface{}
	vi = v
	switch realParamType {
	case string(bizPkg.IntType), string(bizPkg.LongType):
		vi, _ = strconv.ParseInt(v, 10, 64)
	case string(bizPkg.FloatType), string(bizPkg.DoubleType):
		vi, _ = strconv.ParseFloat(v, 64)
	case string(bizPkg.BooleanType):
		vi, _ = strconv.ParseBool(v)
	default:
		vi = ""
	}

	return vi
}

func replacePatternOnce(sqlStr, replacePatternStr, placeholder string) string {
	singleLineSql := strings.Split(sqlStr, "\n")
	for i := range singleLineSql {
		pat := regexp.MustCompile("^(.*?)" + replacePatternStr + "(.*)$")
		repl := "${1}" + placeholder + "$2"
		output := pat.ReplaceAllString(singleLineSql[i], repl)
		if output != singleLineSql[i] {
			singleLineSql[i] = output
			break
		}
	}
	return strings.Join(singleLineSql, "\n")
}
