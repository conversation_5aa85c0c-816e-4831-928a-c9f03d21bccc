package dialect

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/parser"
	"regexp"
	"strings"
)

type DmDiffImpl struct {
	sql    string
	params []*parser.ParsedParam
}

func NewDmDiffImpl(sql string, params []*parser.ParsedParam) (*DmDiffImpl, error) {
	return &DmDiffImpl{
		sql:    sql,
		params: params,
	}, nil
}

func (i *DmDiffImpl) GetOrderByFragmentToWrapSql() string {
	return ""
}

func (i *DmDiffImpl) StripSQL() (string, error) {
	sqls := strings.Split(i.sql, ";")
	var newSqls []string
	for _, sqlFragment := range sqls {
		if sqlFragment == "" {
			continue
		}
		newSqls = append(newSqls, sqlFragment)
	}
	stripCommentsSql := strings.Join(newSqls, ";")
	// 解决?问号占位符被替换为 :v1, :v2 ... 的问题
	stripCommentsSql = questionMarkPattern.ReplaceAllString(stripCommentsSql, "?")
	return stripCommentsSql, nil
}

func (i *DmDiffImpl) CheckStarInSelect() bool {
	re := regexp.MustCompile(`(?i)\bselect\b\s+\*\s+\bfrom\b`)
	return re.MatchString(i.sql)
}
