package dialect

import (
	"context"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/engine"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/parser"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors/biz"
	"strings"
)

type Context struct {
	DialectType    global.DialectType
	OriginSql      string
	InputParams    []*base.InputRequestParam
	RequestParams  []*base.ApiParamBase
	ResponseParams []*base.ApiParamBase
}

type SqlDialectCompiler struct {
	dialectCtx    *Context
	reqParamMap   map[string]*base.ApiParamBase
	inputParamMap map[string]*base.InputRequestParam
	isPaginate    bool
	Engine        engine.Engine
}

type Option func(m *SqlDialectCompiler)

func NewSqlDialectCompiler(dialectCtx *Context, options ...Option) *SqlDialectCompiler {
	var ans = &SqlDialectCompiler{
		dialectCtx:    dialectCtx,
		reqParamMap:   make(map[string]*base.ApiParamBase),
		inputParamMap: make(map[string]*base.InputRequestParam),
	}
	ans.setup()
	// 应用选项
	for _, option := range options {
		option(ans)
	}
	return ans
}

func WithEnablePaginate(isEnable bool) Option {
	return func(m *SqlDialectCompiler) {
		m.isPaginate = isEnable
	}
}

func (c *SqlDialectCompiler) setup() {
	// 构建映射
	for _, reqParam := range c.dialectCtx.RequestParams {
		c.reqParamMap[reqParam.Name] = reqParam
	}
	for _, inputParam := range c.dialectCtx.InputParams {
		c.inputParamMap[inputParam.Name] = inputParam
	}
	// 创建引擎
	c.Engine = engine.NewEngineByDialectType(c.dialectCtx.DialectType)
}

func (c *SqlDialectCompiler) Validate(ctx context.Context) (err error) {
	if strings.TrimSpace(c.dialectCtx.OriginSql) == "" {
		err = errors.New(biz.API_SQL_PARSE_ERR, "SQL不能为空")
		return
	}
	if parser.IsContainMybatisTag(c.dialectCtx.OriginSql) {
		// 如果是mybatis标签sql则不做校验
		return
	}
	var params []*parser.ParsedParam
	if params, err = GetParams(c.dialectCtx.OriginSql); err != nil {
		return
	}
	err = c.ValidateSql(c.dialectCtx.OriginSql, params)
	return
}

func (c *SqlDialectCompiler) Compile(ctx context.Context) (sql string, args []interface{}, err error) {
	if strings.TrimSpace(c.dialectCtx.OriginSql) == "" {
		err = errors.New(biz.API_SQL_PARSE_ERR, "SQL不能为空")
		return
	}
	// 1、首先对sql去注释
	if sql, err = RemoveSqlComments(c.dialectCtx.OriginSql); err != nil {
		return
	}
	// 2、解析Mybatis标签
	if sql, err = ParseMybatisTags(sql, c.dialectCtx.InputParams); err != nil {
		return
	}
	// 3、获取sql中的参数占位符
	var params []*parser.ParsedParam
	if params, err = GetParams(sql); err != nil {
		return
	}
	// 4、校验sql合法性
	if err = c.ValidateSql(sql, params); err != nil {
		return
	}
	// 5、校验参数
	if err = ValidateParams(c.dialectCtx.InputParams, params); err != nil {
		return
	}
	// 6、替换参数
	if sql, args, err = ReplaceParams(sql, params, c.dialectCtx.InputParams, c.dialectCtx.RequestParams); err != nil {
		return
	}
	if sql, err = c.stripSQL(sql, params); err != nil {
		return
	}
	// 生成分页sql
	var appendArgs []interface{}
	if sql, appendArgs, err = c.genSqlWithLimit(sql, params); err != nil {
		return
	}
	args = append(args, appendArgs...)
	return
}

// ValidateSql 校验sql
func (c *SqlDialectCompiler) ValidateSql(sql string, params []*parser.ParsedParam) (err error) {
	if withSemicolonPattern.MatchString(sql) {
		return errors.New(biz.API_SQL_PARSE_ERR, "单个SQL语句无需以分号结尾")
	}
	// 不能用*查询
	var differ Difference
	if differ, err = c.getDialectDiffer(sql, params); err != nil {
		return
	}
	if differ.CheckStarInSelect() {
		return errors.New(biz.API_SQL_PARSE_ERR, "不支持使用*查询")
	}
	return
}

func (c *SqlDialectCompiler) getDialectDiffer(sql string, params []*parser.ParsedParam) (differ Difference, err error) {
	switch c.dialectCtx.DialectType {
	case global.DmsqlDialect:
		differ, err = NewDmDiffImpl(sql, params)
	default:
		differ, err = NewMysqlDiffImpl(sql, params)
	}
	return
}

func (c *SqlDialectCompiler) stripSQL(sql string, params []*parser.ParsedParam) (ans string, err error) {
	var selectParser Difference
	if selectParser, err = c.getDialectDiffer(sql, params); err != nil {
		return
	}
	ans, err = selectParser.StripSQL()
	return
}

func (c *SqlDialectCompiler) genSqlWithLimit(sourceSql string, params []*parser.ParsedParam) (sql string, appendArgs []interface{}, err error) {
	var selectParser Difference
	if selectParser, err = c.getDialectDiffer(sourceSql, params); err != nil {
		return
	}
	filterColumns := c.getFilteredColumns()
	if c.isPaginate {
		pageNum, pageSize := c.GetPaginateArgs()
		sql, appendArgs = c.Engine.GenSelectAllWithLimit(sourceSql, filterColumns, pageSize, pageNum, selectParser.GetOrderByFragmentToWrapSql())
	} else {
		// 返回结果限制小于2000
		sql, appendArgs = c.Engine.GenSelectAllWithSysLimit(sourceSql, filterColumns, selectParser.GetOrderByFragmentToWrapSql())
	}
	return
}

func (c *SqlDialectCompiler) GetPaginateArgs() (pageNum, pageSize int64) {
	// 检测参数逻辑放在入口位置
	for _, input := range c.dialectCtx.InputParams {
		v, ok := c.reqParamMap[input.Name]
		// 用户本身没有定义,忽略
		if !ok || v.Source != string(base.SourceFieldSystem) {
			continue
		}
		if v.Name == base.PageNumPublicParam.Name {
			pageNum = input.Value.(int64)
		} else if v.Name == base.PageSizePublicParam.Name {
			pageSize = input.Value.(int64)
		}
	}
	// 第0页没有意义，所以默认第一页
	if pageNum == 0 {
		pageNum = 1
	}
	return
}

func (c *SqlDialectCompiler) getFilteredColumns() []string {
	columns := []string{}
	for _, field := range c.dialectCtx.ResponseParams {
		if field.Source == string(base.SourceFieldSystem) {
			continue
		}
		columns = append(columns, field.Name)
	}

	// 如果没有设置过滤返回字段，则返回所有字段
	if _, ok := c.reqParamMap[base.DynamicColumnsPublicParam.Name]; !ok {
		return columns
	}

	var filteredColumns []string
	for _, input := range c.dialectCtx.InputParams {
		if input.Name != base.DynamicColumnsPublicParam.Name {
			continue
		}
		filteredColumns = input.Value.([]string)
	}
	if len(filteredColumns) == 0 {
		return columns
	}
	return filteredColumns
}

func (c *SqlDialectCompiler) GetBackQuoteReplaceStr(param string, value interface{}) (placeholder, replacePatternStr string) {
	placeholder = fmt.Sprintf("`%s`", value)
	replacePatternStr = fmt.Sprintf(tempBackQuotePatternStr, "`", param, "`")
	return
}
