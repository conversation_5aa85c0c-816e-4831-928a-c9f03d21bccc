package dialect

import (
	"fmt"
	"regexp"
	"strings"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/parser"
)

type MysqlDiffImpl struct {
	selectParser *parser.SelectParser
	sql          string
	params       []*parser.ParsedParam
}

func NewMysqlDiffImpl(sql string, params []*parser.ParsedParam) (*MysqlDiffImpl, error) {
	for _, p := range params {
		placeholder := fmt.Sprintf("`$${%s}`", p.ParamName)
		replacePatternStr := fmt.Sprintf(tempBackQuotePatternStr, "`", p.ParamName, "`")
		replacePattern := regexp.MustCompile(replacePatternStr)
		sql = replacePattern.ReplaceAllString(sql, placeholder)
	}
	selectParser, err := parser.NewSelectParser(sql)
	if err != nil {
		return nil, err
	}
	return &MysqlDiffImpl{
		sql:          sql,
		params:       params,
		selectParser: selectParser,
	}, nil
}

func (i *MysqlDiffImpl) GetOrderByFragmentToWrapSql() string {
	return i.selectParser.GetReplaceTableNamedOrderBy()
}

func (i *MysqlDiffImpl) StripSQL() (string, error) {
	sqls := strings.Split(i.sql, ";")
	var newSqls []string
	for _, sqlFragment := range sqls {
		if sqlFragment == "" {
			continue
		}
		// vitess无法识别cross join和join, moql场景下会有问题, 这里不做格式化
		//parseStmt, err := sqlparser.Parse(sqlFragment)
		//if err != nil {
		//	return i.sql, nil
		//}
		//buf := sqlparser.NewTrackedBuffer(nil)
		//parseStmt.Format(buf)
		//newSql := buf.String()
		//// 某些场景下format可能会把原sql中的with删除, 尝试检测该场景, 此时不使用format后的sql
		//if (strings.HasPrefix(strings.TrimSpace(sqlFragment), "with") || strings.HasPrefix(strings.TrimSpace(sqlFragment), "WITH")) && !(strings.HasPrefix(newSql, "WITH") || strings.HasPrefix(newSql, "with")) {
		//	newSql = sqlFragment
		//}
		newSqls = append(newSqls, sqlFragment)
	}

	stripCommentsSql := strings.Join(newSqls, ";")
	// 解决?问号占位符被替换为 :v1, :v2 ... 的问题
	stripCommentsSql = questionMarkPattern.ReplaceAllString(stripCommentsSql, "?")
	return stripCommentsSql, nil
}

func (i *MysqlDiffImpl) CheckStarInSelect() bool {
	return i.selectParser.CheckStarInSelect()
}
