package services

import (
	"context"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	commonProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/moql"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/sqlce"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/dialect"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/db_helper/gorm_db"
	"strings"
)

func (s *ApiManageService) SqlCheck(ctx context.Context, req *proto.SqlCheckRequest) (
	rsp *proto.SqlCheckResponse, err error) {
	switch req.DialectSetting {
	case global.MoSqlDialectSetting:
		rsp, err = s.moSqlCheck(ctx, req.OriginMoSql, req.InputParams)
	case global.MultiSqlDialectSetting:
		rsp = &proto.SqlCheckResponse{
			{
				Name:   proto.BaseCheckItemName,
				Status: proto.CheckStatusNameCNMap[proto.CheckNormal],
			},
		}
	default:
		err = errors.Errorf("Invalid Dialect Setting: %s", req.DialectSetting)
	}
	return
}

func (s *ApiManageService) AsyncRunSql(ctx context.Context, project string, req *proto.AsyncRunSqlRequest) (
	rsp proto.AsyncRunSqlResponse, err error) {
	// 获取当前查询引擎
	var resource *commonProto.ResourceInfo
	resource, err = s.common.GetStorageResources(ctx, project)
	if err != nil {
		return
	}
	var originSql string
	var resourceType = global.ResourceType(resource.ResourceType)
	// 根据方言设置和引擎推算出使用的具体哪种方言
	if originSql, err = req.GetSql(resourceType); err != nil {
		return
	}
	var dialectType = req.GetDialectType(resourceType)

	// 编译sql
	var querySql string
	var queryArgs []interface{}
	var dialectContext = &dialect.Context{
		DialectType: dialectType,
		OriginSql:   originSql,
		InputParams: req.InputParams,
	}
	var dialectCompiler = dialect.NewSqlDialectCompiler(dialectContext)
	if querySql, queryArgs, err = dialectCompiler.Compile(ctx); err != nil {
		return
	}

	// 将sql与sql参数合并为完整sql
	var dialector = gorm_db.GetDialector(dialectType)
	var explainSql = dialector.Explain(querySql, queryArgs...)

	// 如果是mosql，则翻译为具体执行引擎对应的语言
	if req.DialectSetting == global.MoSqlDialectSetting {
		// 先检测sql
		var validateResult *moql.ValidateResult
		if validateResult, err = s.moqlService.Validate(ctx, explainSql, true); err != nil {
			return
		}
		if !validateResult.Success {
			err = errors.New(strings.Join(validateResult.Errors, "\n"))
			return
		}
		// 再翻译sql
		var translateResult *moql.TranslateResult
		if translateResult, err = s.moqlService.Translate(ctx, getTranslateTypeByResourceType(resourceType), explainSql); err != nil {
			return
		}
		if !translateResult.Success {
			err = errors.New(strings.Join(translateResult.Errors, "\n"))
			return
		}
		explainSql = translateResult.Sql
	}

	// 通过引擎执行对应的sql
	var answer sqlce.AsyncRunSqlScriptResponse
	if answer, err = s.sqlCeEngine.AsyncRunSQLScriptAction(ctx, sqlce.AsyncRunSqlScriptRequest{
		BaseRequest: sqlce.BaseRequest{
			ResourceId: resource.Id,
		},
		Project:   project,
		SqlScript: explainSql,
	}); err != nil {
		return
	}
	rsp = answer.Results
	return
}

func (s *ApiManageService) GetRunSqlStatus(ctx context.Context, project string, req *proto.GetRunSqlStatusRequest) (
	rsp *proto.GetRunSqlStatusResponse, err error) {

	// 获取当前查询引擎
	var resource *commonProto.ResourceInfo
	resource, err = s.common.GetStorageResources(ctx, project)
	if err != nil {
		return
	}
	// 查询结果
	var answer sqlce.GetAsyncRunSQLResultResponse
	if answer, err = s.sqlCeEngine.GetAsyncRunSQLResultAction(ctx, sqlce.GetAsyncRunSQLResultRequest{
		BaseRequest: sqlce.BaseRequest{
			ResourceId:   resource.Id,
			ResourceType: resource.ResourceType,
		},
		Project:    project,
		InstanceId: req.InstanceId,
	}); err != nil {
		return
	}
	// 拼接后返回
	rsp = &proto.GetRunSqlStatusResponse{}
	rsp.InstanceStatus = answer.InstanceStatus
	rsp.Tasks = lo.Map(answer.Tasks, func(item sqlce.MaxCptTaskResult, index int) proto.GetRunSqlStateItem {
		return proto.GetRunSqlStateItem{
			Name:     item.Name,
			Status:   item.Status,
			Progress: item.Progress,
			Error:    item.Error,
			Result:   item.Result,
		}
	})
	return
}

func (s *ApiManageService) moSqlCheck(ctx context.Context, sql string, inputParams []*base.InputRequestParam) (
	rsp *proto.SqlCheckResponse, err error) {
	// 编译sql(mosql默认为mysql方言)
	var querySql string
	var queryArgs []interface{}
	var dialectContext = &dialect.Context{
		DialectType: global.MysqlDialect,
		OriginSql:   sql,
		InputParams: inputParams,
	}
	var dialectCompiler = dialect.NewSqlDialectCompiler(dialectContext)
	if querySql, queryArgs, err = dialectCompiler.Compile(ctx); err != nil {
		err = errors.Wrap(err, "Compile sql error")
		return
	}

	// 捏合sql
	var dialector = gorm_db.GetDialector(global.MysqlDialect)
	var explainSql = dialector.Explain(querySql, queryArgs...)

	// 检查sql
	var validateResult *moql.ValidateResult
	if validateResult, err = s.moqlService.Validate(ctx, explainSql, true); err != nil {
		err = errors.Wrap(err, "Validate sql error")
		return
	}

	// 组装返回结果
	if validateResult.Success {
		rsp = &proto.SqlCheckResponse{
			{
				Name:   proto.BaseCheckItemName,
				Status: proto.CheckStatusNameCNMap[proto.CheckNormal],
			},
		}
		return
	}
	rsp = &proto.SqlCheckResponse{
		{
			Name:   proto.BaseCheckItemName,
			Status: proto.CheckStatusNameCNMap[proto.CheckAbNormal],
			ErrMsg: strings.Join(validateResult.Errors, "\n"),
		},
	}
	return
}

func getTranslateTypeByResourceType(resourceType global.ResourceType) moql.MoqlDialect {
	switch resourceType {
	case global.DamengResourceType, global.DamengSaaSResourceType:
		return moql.DmDialect
	default:
		return moql.OtherDialect
	}
}
