package services

import (
	"context"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"

	appProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/proto"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/repo"
	appRepo "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/repo"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/proto"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewApiGroupService))
}

type ApiGroupService struct {
	logger        *logger.Logger
	apiManageRepo repo.ApiManageRepo
	appRepo       appRepo.ApplicationRepo
	repo          repo.ApiGroupRepo
}

func NewApiGroupService(logger *logger.Logger, repo repo.ApiGroupRepo, apiManageRepo repo.ApiManageRepo, appRepo appRepo.ApplicationRepo) *ApiGroupService {
	return &ApiGroupService{
		logger:        logger,
		repo:          repo,
		apiManageRepo: apiManageRepo,
		appRepo:       appRepo,
	}
}

// GenAuthList
func (am *ApiGroupService) GenAuthList(ctx context.Context, project, apiCode string, apiCodes []string) (authList []*proto.AuthAppsItem, err error) {
	var appIDs []string
	authApps, err := am.apiManageRepo.ApplicationApiRelationListWithRank(ctx, project, apiCode, apiCodes)
	if err != nil {
		return
	}
	for _, item := range authApps {
		appIDs = append(appIDs, item.AppID)
	}
	appDetailMap := make(map[string]*appProto.AppListResp)
	var appList []*appProto.AppListResp
	if len(appIDs) != 0 {
		appList, err = am.appRepo.ApplicationListByIDs(ctx, project, appIDs, true)
		if err != nil {
			return
		}
	}
	for _, app := range appList {
		_, ok := appDetailMap[app.ID]
		if !ok {
			appDetailMap[app.ID] = app
		}
	}

	// 鉴权应用详情列表
	for _, item := range authApps {
		appDetail, ok := appDetailMap[item.AppID]
		if !ok || appDetail == nil {
			continue
		}
		authApp := &proto.AuthAppsItem{
			ApiCode:   item.ApiCode,
			AppID:     item.AppID,
			Name:      appDetail.Name,
			AuthTerm:  item.AuthTerm,
			StartTime: item.StartTime,
			EndTime:   item.EndTime,
		}
		authList = append(authList, authApp)
	}
	return
}

// GetRelationMap
func (am *ApiGroupService) GetRelationMap(ctx context.Context, project string, groupApiList []*proto.GroupApiListResp) (map[string][]*proto.AuthAppsItem, error) {
	var apiCodeList []string
	for _, item := range groupApiList {
		apiCodeList = append(apiCodeList, item.Code)
	}
	relationList, err := am.GenAuthList(ctx, project, "", apiCodeList)
	if err != nil {
		return nil, err
	}
	relationMap := make(map[string][]*proto.AuthAppsItem)
	for _, item := range relationList {
		_, ok := relationMap[item.ApiCode]
		if !ok {
			relationMap[item.ApiCode] = []*proto.AuthAppsItem{}
		}
		relationMap[item.ApiCode] = append(relationMap[item.ApiCode], item)
	}
	return relationMap, nil
}

// convertToGroupApiMap
func convertToGroupApiMap(apis []*proto.GroupApiListResp, relationMap map[string][]*proto.AuthAppsItem) map[string][]*proto.GroupListApiItem {
	m := make(map[string][]*proto.GroupListApiItem)
	for _, api := range apis {
		_, ok := m[api.GroupID.String]
		if !ok {
			m[api.GroupID.String] = []*proto.GroupListApiItem{}
		}
		apiItem := &proto.GroupListApiItem{
			Code:         api.Code,
			Name:         api.Name,
			Status:       api.Status,
			IsUpdated:    api.IsUpdated,
			RateLimit:    api.RateLimit,
			TimeoutLimit: api.TimeoutLimit,
			CreateMode:   api.CreateMode,
			AuthApps:     relationMap[api.Code],
		}
		m[api.GroupID.String] = append(m[api.GroupID.String], apiItem)
	}
	return m
}

// GetGroupApiListWithApis 获取分组下的api列表
func (am *ApiGroupService) GetGroupApiListWithApis(ctx context.Context, project, subjectId string) (resp []*proto.GroupListResp, err error) {
	groupList, err := am.repo.GetGroupApiListWithApis(ctx, project, subjectId)
	if err != nil {
		return
	}
	var groupIDs []string
	for _, g := range groupList {
		groupIDs = append(groupIDs, g.Id)
	}
	groupApiList, err := am.repo.GetGroupApiList(ctx, project, groupIDs)
	if err != nil {
		return
	}
	relationMap, err := am.GetRelationMap(ctx, project, groupApiList)
	if err != nil {
		return
	}
	groupMap := convertToGroupApiMap(groupApiList, relationMap)
	resp = []*proto.GroupListResp{}
	for _, g := range groupList {
		apis := groupMap[g.Id]
		resp = append(resp, &proto.GroupListResp{
			Id:            g.Id,
			Name:          g.Name,
			Description:   g.Description,
			IsSystemGroup: proto.NotSystemGroup,
			Apis:          apis,
		})
	}
	return
}

// AddGroup 新增分组
func (am *ApiGroupService) AddGroup(ctx context.Context, project string, req proto.GroupAddReq) (groupID string, err error) {
	group, err := am.repo.CheckGroupExist(ctx, project, "", req.Name, req.SubjectId)
	if err != nil {
		return
	}
	if group != nil && group.Id != "" {
		err = utils.NewUserError("分组名称已存在")
		return
	}
	groupID, err = am.repo.AddGroup(ctx, project, req)
	return
}

// UpdateGroup 更新分组
func (am *ApiGroupService) UpdateGroup(ctx context.Context, project string, req proto.GroupUpdateReq) (err error) {
	group, err := am.repo.CheckGroupExist(ctx, project, req.Id, req.Name, "")
	if err != nil {
		return
	}
	if group != nil && group.Id != "" {
		err = utils.NewUserError("分组名称已存在")
		return
	}
	err = am.repo.UpdateGroup(ctx, project, req)
	return
}

// DeleteGroup 删除分组
func (am *ApiGroupService) DeleteGroup(ctx context.Context, project string, groupID string) (err error) {
	group, err := am.repo.GetGroupDetail(ctx, project, groupID)
	if err != nil {
		return
	}
	if group == nil || group.Id == "" {
		err = utils.NewUserError("分组不存在")
		return
	}
	// 判断分组下是否有api
	apis, err := am.repo.GetGroupApiList(ctx, project, []string{groupID})
	if err != nil {
		return
	}
	if len(apis) > 0 {
		err = utils.NewUserError("请先清空分组再删除")
		return
	}
	err = am.repo.DeleteGroup(ctx, project, groupID)
	return
}

// GetGroupList 获取分组列表
func (am *ApiGroupService) GetGroupList(ctx context.Context, project string) (groupList []*base.ApiGroup, err error) {
	groupList, err = am.repo.GetGroupList(ctx, project)
	return
}
