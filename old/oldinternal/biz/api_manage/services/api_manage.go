package services

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global/consts"
	"log"
	"regexp"
	"runtime/debug"
	"strings"
	"time"

	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/moql"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/sqlce"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/dialect"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"

	dap_common "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"

	"github.com/defval/inject/v2"
	"github.com/go-sql-driver/mysql"
	dapCommonProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common/proto"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/indicator_model"
	indicatorModelRepo "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/repo/indicator_model"
	multiDimModelRepo "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/repo/multi_dim_model"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/parser"
	bizErrors "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/recorder"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/repo"
	appBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/base"
	appProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/proto"
	appRepo "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/application/repo"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/apierr"
	response "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/service/data_api"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/fast"
	sm3Jwt "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/jwt/sm3"
	pkgBiz "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/interface_utils"
	nacosProto "gitlab.mypaas.com.cn/bigdata/nacos-sdk-go/nacos_client/proto"
	pkgFast "gitlab.mypaas.com.cn/dmp/gopkg/bigdata/fast"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewApiManageService))
}

var withSemicolonPattern = regexp.MustCompile(`^.+;$`)

const tempBackQuotePatternStr = `%s*\$\{%s\}%s*`

const (
	MaxAge = 7200
)

type ApiManageService struct {
	logger             *logger.Logger
	repo               repo.ApiManageRepo
	apiGroupRepo       repo.ApiGroupRepo
	appRepo            appRepo.ApplicationRepo
	dataApi            *data_api.DataApiService
	recorder           *recorder.ApiManagerFastRecorder
	apiCache           *cache.ApiCache
	common             dap_common.DapCommonService
	indicatorModelRepo *indicatorModelRepo.IndicatorModelRepoS
	multiDimModelRepo  multiDimModelRepo.MultiDimModelRepo
	moqlService        moql.MoqlService
	sqlCeEngine        sqlce.SqlceCallerIface
}

func NewApiManageService(logger *logger.Logger, repo repo.ApiManageRepo, appRepo appRepo.ApplicationRepo,
	dataApi *data_api.DataApiService, recorder *recorder.ApiManagerFastRecorder,
	apiCache *cache.ApiCache, apiGroupRepo repo.ApiGroupRepo) *ApiManageService {
	apiMange := &ApiManageService{
		recorder:           recorder,
		logger:             logger,
		repo:               repo,
		appRepo:            appRepo,
		dataApi:            dataApi,
		apiCache:           apiCache,
		common:             dap_common.NewDapCommonServiceS(),
		apiGroupRepo:       apiGroupRepo,
		indicatorModelRepo: indicatorModelRepo.NewIndicatorModelRepoS(),
		multiDimModelRepo:  multiDimModelRepo.NewMultiDimModelRepoS(),
		moqlService:        moql.NewMoqlServiceS(),
		sqlCeEngine:        sqlce.NewSqlceHttpCaller(),
	}

	return apiMange
}

func (am *ApiManageService) genApiUrl(project, apiPath string) string {
	apiPath = strings.TrimSuffix(GenApiPath(project, apiPath), "$")
	apiPath = strings.TrimPrefix(apiPath, "/")
	return fmt.Sprintf("%s/%s", strings.TrimSuffix(global.AppConfig.Config.GetUrlConfig("", nacosProto.DpAppCode).Url, "/"), apiPath)
}

func GenApiPath(project, apiPath string) string {
	path := strings.TrimPrefix(apiPath, "/")
	apiPath = fmt.Sprintf("/openapi/%s/%s$", project, path)
	return apiPath
}

// convertToAuthApiListResp
func convertToAuthApiListResp(apiList []*proto.ApplicationAuthApiItemResp, projectInfo *entities.Project) (apiListResp []proto.ApplicationAuthApiResp, err error) {
	apiListResp = []proto.ApplicationAuthApiResp{}
	for _, api := range apiList {
		var displayStatus base.DisplayApiStatus
		var publishOn db.NullTime
		switch api.Status {
		case base.Dev:
			displayStatus = base.ApiStatusOfflineDisplay
		case base.Published:
			displayStatus = base.ApiStatusNormalDisplay
			publishOn = api.CurPublishOn
			// 授权是否过期
			if api.AuthTerm == base.Custom && api.EndTime.Time.Before(time.Now()) {
				displayStatus = base.ApiStatusExpiredDisplay
			}
		}
		newRespApi := proto.ApplicationAuthApiResp{
			AppID:         api.AppID,
			Project:       projectInfo.Name,
			ApiCode:       api.ApiCode,
			Name:          api.Name,
			Description:   api.Description,
			RequestMethod: api.RequestMethod,
			RequestPath:   api.RequestPath,
			AuthTerm:      api.AuthTerm,
			StartTime:     api.StartTime,
			EndTime:       api.EndTime,
			CreatedBy:     api.CreatedBy,
			CreatedOn:     api.CreatedOn,
			Status:        displayStatus,
			PublishOn:     publishOn,
		}
		apiListResp = append(apiListResp, newRespApi)
	}
	return
}

func (am *ApiManageService) GetSubjectApiCount(ctx context.Context, project string) (map[string]int, error) {
	return am.repo.GetApiCountBySubjectId(ctx, project)
}

// GetParamMapByEngine 数据库类型和api参数的类型对应关系
func (am *ApiManageService) GetParamMapByEngine(ctx context.Context, req proto.GetParamMapReq) (resp map[string]base.ApiParamDataType, err error) {
	if req.SourceType == base.ApiSourceRDS {
		return base.MysqlTypeToSystemType, nil
	}
	return base.ADSTypeToSystemType, nil
}

// GetApplicationAuthApiList 获取授权关联Api列表
func (am *ApiManageService) GetApplicationAuthApiList(ctx context.Context, req proto.ApplicationAuthApiReq) (resp proto.ListApplicationAuthApiResp, err error) {
	dbApiList, total, err := am.repo.GetApplicationAuthApiList(ctx, req)
	if err != nil {
		return
	}
	projectInfo, err := am.repo.GetProject(ctx, req.ProjectCode)
	if err != nil {
		return
	}
	apiListResp, err := convertToAuthApiListResp(dbApiList, projectInfo)
	if err != nil {
		return
	}
	resp.Item = apiListResp
	resp.Total = total
	return
}

// UnbindAuthApi 应用与Api解绑
func (am *ApiManageService) UnbindAuthApi(ctx context.Context, project string, req proto.UnbindAuthApiReq) (err error) {
	// 与api解绑逻辑保持一致
	unbindReq := proto.ApiUnbindReq{ApiCode: req.ApiCode, Apps: []string{req.AppID}}
	err = am.ApiUnbind(ctx, project, unbindReq, true, nil)
	return
}

// GetAuthList
func (am *ApiManageService) GetAuthRelationList(ctx context.Context, project, apiCode string, apiCodes []string) (authList []*proto.AuthAppsItemResp, err error) {
	var appIDs []string
	authApps, err := am.repo.ApplicationApiRelationListWithRank(ctx, project, apiCode, apiCodes)
	if err != nil {
		return
	}
	for _, item := range authApps {
		appIDs = append(appIDs, item.AppID)
	}
	appDetailMap := make(map[string]*appProto.AppListResp)
	var appList []*appProto.AppListResp
	if len(appIDs) != 0 {
		appList, err = am.appRepo.ApplicationListByIDs(ctx, project, appIDs, true)
		if err != nil {
			return
		}
	}
	for _, app := range appList {
		_, ok := appDetailMap[app.ID]
		if !ok {
			appDetailMap[app.ID] = app
		}
	}

	// 鉴权应用详情列表
	for _, item := range authApps {
		appDetail, ok := appDetailMap[item.AppID]
		if !ok {
			continue
		}
		authApp := &proto.AuthAppsItemResp{
			ApiCode:     item.ApiCode,
			AppID:       item.AppID,
			Name:        appDetail.Name,
			Description: appDetail.Description,
			IsEnable:    appDetail.IsEnable,
			AuthTerm:    item.AuthTerm,
			StartTime:   item.StartTime,
			EndTime:     item.EndTime,
			ModifiedBy:  item.ModifiedBy.String,
			ModifiedOn:  item.ModifiedOn,
		}
		authList = append(authList, authApp)
	}
	return
}

// GetRelationMap
func (am *ApiManageService) GetRelationMap(ctx context.Context, project string, apiCodes []string) (map[string][]*proto.AuthAppsItemResp, error) {
	relationList, err := am.GetAuthRelationList(ctx, project, "", apiCodes)
	if err != nil {
		return nil, err
	}
	relationMap := make(map[string][]*proto.AuthAppsItemResp)
	for _, item := range relationList {
		_, ok := relationMap[item.ApiCode]
		if !ok {
			relationMap[item.ApiCode] = []*proto.AuthAppsItemResp{}
		}
		relationMap[item.ApiCode] = append(relationMap[item.ApiCode], item)
	}
	return relationMap, nil
}

// convertToApiListResp
func (am *ApiManageService) ConvertToApiListResp(apiList []*proto.ApiListDbItem, relationMap map[string][]*proto.AuthAppsItemResp) (apiListResp []*proto.ApiListResp, err error) {
	apiListResp = []*proto.ApiListResp{}
	for _, api := range apiList {
		authApps := relationMap[api.Code]
		newRespApi := &proto.ApiListResp{
			Code:         api.Code,
			Name:         api.Name,
			Description:  api.Description,
			GroupID:      api.GroupID,
			GroupName:    api.GroupName.String,
			SubjectID:    api.SubjectID.String,
			RequestPath:  api.RequestPath,
			RateLimit:    api.RateLimit,
			TimeoutLimit: api.TimeoutLimit,
			Status:       api.Status,
			IsUpdated:    api.IsUpdated,
			CreatedBy:    api.CreatedBy,
			CreatedOn:    api.CreatedOn,
			ModifiedBy:   api.ModifiedBy,
			ModifiedOn:   api.ModifiedOn,
			PublishOn:    api.CurPublishOn,
			RefTableSet:  api.RefTableSet.String,
			AuthApps:     authApps,
			CreateMode:   api.CreateMode,
		}
		apiListResp = append(apiListResp, newRespApi)
	}
	return
}

func (am *ApiManageService) GetApiGroupTree(ctx context.Context, project string, req proto.ApiListReq) (rsp proto.ApiGroupTreeRsp, err error) {
	subjectIds := []string{}
	if req.SubjectId != "" {
		subjectIds = append(subjectIds, req.SubjectId)
	}
	apiGroups, err := am.apiGroupRepo.GetGroupListWithSubjectIds(ctx, project, subjectIds)
	if err != nil {
		return
	}

	apiList, _, err := am.repo.GetApiList(ctx, project, req)
	if err != nil {
		return
	}
	var apiCodes []string
	for _, item := range apiList {
		apiCodes = append(apiCodes, item.Code)
	}
	relationMap, err := am.GetRelationMap(ctx, project, apiCodes)
	if err != nil {
		return
	}
	apis, err := am.ConvertToApiListResp(apiList, relationMap)
	if err != nil {
		return
	}

	rsp.Items = lo.Map(apiGroups, func(item *base.ApiGroup, index int) *proto.ApiGroupTreeItem {
		return &proto.ApiGroupTreeItem{
			Id:            item.Id,
			Name:          item.Name,
			Description:   item.Description,
			IsSystemGroup: 0,
			SubjectId:     item.SubjectId,
			Children: lo.Filter(apis, func(apiItem *proto.ApiListResp, _ int) bool {
				return apiItem.GroupID == item.Id
			}),
		}
	})
	return
}

// GetApiList 查询获取api列表
func (am *ApiManageService) GetApiList(ctx context.Context, project string, req proto.ApiListReq) (resp proto.PaginationApiListResp, err error) {
	apiList, total, err := am.repo.GetApiList(ctx, project, req)
	if err != nil {
		return
	}
	var apiCodes []string
	for _, item := range apiList {
		apiCodes = append(apiCodes, item.Code)
	}
	relationMap, err := am.GetRelationMap(ctx, project, apiCodes)
	if err != nil {
		return
	}
	apiListResp, err := am.ConvertToApiListResp(apiList, relationMap)
	resp.Items = apiListResp
	resp.Total = total
	return
}

const (
	RDSDataSourceID           = "00000000-1111-1111-2222-000000000000"
	RDSSaasDataSourceID       = "00000000-1111-1112-2222-000000000000"
	StarRocksDataSourceID     = "00000000-1111-1111-9999-000000000000"
	StarRocksSaaSDataSourceID = "00000000-1111-1112-1111-000000000000"
	DMDataSourceID            = "00000000-1111-3333-1111-000000000000"
	DMSaaSDataSourceID        = "00000000-1111-3333-2222-000000000000"
)

// ApiAdd api新增接口
func (am *ApiManageService) ApiAdd(ctx context.Context, project string, req proto.ApiAddReq) (resp proto.ApiAddResp, err error) {
	err = am.apiAddValidate(ctx, project, req)
	if err != nil {
		return
	}
	resource, err := am.common.GetStorageResources(ctx, project)
	if err != nil {
		return
	}

	if resource.ResourceType == string(global.RDSResourceType) {
		req.ResourceID = RDSDataSourceID
	} else if resource.ResourceType == string(global.RDSSaasResourceType) {
		req.ResourceID = RDSSaasDataSourceID
	} else if resource.ResourceType == string(global.StarRocksResourceType) {
		req.ResourceID = StarRocksDataSourceID
	} else if resource.ResourceType == string(global.StarRocksSaaSResourceType) {
		req.ResourceID = StarRocksSaaSDataSourceID
	} else if resource.ResourceType == string(global.DamengResourceType) {
		req.ResourceID = DMDataSourceID
	} else if resource.ResourceType == string(global.DamengSaaSResourceType) {
		req.ResourceID = DMSaaSDataSourceID
	} else {
		return resp, errors.Internal("未知的资源类型: %s", resource.ResourceType)
	}
	resp, err = am.repo.AddApi(ctx, project, req)
	return
}

// ApiDelete 删除api
func (am *ApiManageService) ApiDelete(ctx context.Context, project string, req proto.ApiDeleteReq) (err error) {
	// 是否为未发布状态
	apiMain, err := am.repo.GetApiMainDetail(ctx, project, req.ApiCode)
	if err != nil {
		return
	}
	if apiMain != nil && apiMain.Status == base.Published {
		return bizErrors.NewReleasedErr(req.ApiCode)
	}
	// 是否存在已授权应用
	relationList, err := am.repo.ApplicationApiRelationList(ctx, project, req.ApiCode)
	if err != nil {
		return
	}
	var apps []string
	for _, item := range relationList {
		apps = append(apps, item.AppID)
	}

	// 获取TX
	tx, err := am.repo.GetTx(ctx, project)
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	// 解绑并删除授权关联
	if len(apps) != 0 {
		unbindReq := proto.ApiUnbindReq{ApiCode: req.ApiCode, Apps: apps}
		err = am.ApiUnbind(ctx, project, unbindReq, false, tx)
		if err != nil {
			return
		}
	}
	// 删除api
	err = am.repo.DeleteApi(ctx, project, tx, req)
	if err != nil {
		return
	}
	err = tx.Commit()
	return
}

func (am *ApiManageService) apiAddValidate(ctx context.Context, project string, req proto.ApiAddReq) (err error) {
	err = am.nameValidate(ctx, project, req.Name, req.GroupID)
	if err != nil {
		return
	}
	err = am.pathValidate(ctx, project, "", req.RequestPath)
	if err != nil {
		return
	}
	return
}

func (am *ApiManageService) validateRequestParam(requestParams []*base.ApiParamBase) (err error) {
	for _, param := range requestParams {
		if param.DefaultValue == "" {
			continue
		}

		if param.ParamType == base.Variable {
			if len(param.VariableContent) == 0 {
				return bizErrors.NewUpdateReqVariableErr(param.Name)
			}

			// 变量不校验操作
			continue
		}

		_, err := pkgBiz.ValidateByValueAndOperate(param.Operator, param.DataType, param.DefaultValue)
		if err != nil {
			return bizErrors.NewValidateParamDataErr(param.Name, param.DataType, param.DefaultValue)
		}
	}
	return
}

func (am *ApiManageService) validateApiParam(reqParams, respParams []*base.ApiParamBase) (err error) {
	// 必须要有一个以上请求参数
	if len(reqParams) == 0 {
		return bizErrors.NewUpdateReqNumErr()
	}
	// 必须要有一个以上返回参数
	if len(respParams) == 0 {
		return bizErrors.NewUpdateRespNumErr()
	}

	return
}

func (am *ApiManageService) apiUpdateValidate(ctx context.Context, project string, req *proto.ApiUpdateReq) (err error) {
	resource, err := am.common.GetStorageResources(ctx, project)
	if err != nil {
		am.logger.Errorf("跳过sql解析, 获取资源失败: %s", err.Error())
		return
	}
	err = am.validateApiParam(req.RequestParams, req.ResponseParams)
	if err != nil {
		return
	}

	for _, param := range req.RequestParams {
		if param.Name == base.BigdataTenantCodePublicParam.Name &&
			cBase.ProjectResourceType(resource.ResourceType) != cBase.StarRocksSaaSResourceType &&
			cBase.ProjectResourceType(resource.ResourceType) != cBase.DamengSaaSResourceType &&
			cBase.ProjectResourceType(resource.ResourceType) != cBase.RDSSaaSResourceType {
			return utils.NewUserError("当前引擎类型<%s>不支持<%s>参数", resource.ResourceType, base.BigdataTenantCodePublicParam.Name)
		}
	}

	// 只能保存编辑态api
	dbApi, err := am.repo.GetApiDetail(ctx, project, req.ApiID)
	if err != nil {
		return
	}
	if dbApi.Stage != base.StageDraft {
		return bizErrors.NewUpdateNotDraftErr()
	}
	// 校验请求参数中用户填写的默认值
	err = am.validateRequestParam(req.RequestParams)
	if err != nil {
		return
	}
	// 校验路径唯一
	err = am.pathValidate(ctx, project, req.ApiCode, req.RequestPath)
	if err != nil {
		return
	}
	// sql模式校验
	if req.CreateMode == base.ModeSql {
		err = am.apiSqlModeValidate(ctx, project, *req)
		if err != nil {
			return
		}
	}

	if req.CreateMode == base.ModeIndicator {
		err = am.apiIndicatorModeValidate(req)
		if err != nil {
			return
		}
	}

	for _, param := range req.RequestParams {
		if param.ParamType == base.Variable {
			variable := rpc_call.Variable{}
			if iErr := json.Unmarshal([]byte(param.VariableContent), &variable); iErr == nil {
				param.Description = variable.Desc
				param.DataType = strings.ToUpper(string(variable.VariableType))
				param.DefaultValue, _ = variable.GetDefaultValue()
				param.ExampleValue = param.DefaultValue
			}
		}
	}

	return
}

type SqlParseResponse struct {
	Success      bool   `json:"success"`
	ErrorCode    int    `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
	Data         struct {
		Statement string   `json:"statement"`
		Inputs    []string `json:"inputs"`
	} `json:"data"`
}

func (am *ApiManageService) newSelectParser(sql string, params []*parser.ParsedParam) (*parser.SelectParser, error) {
	// sqlparser不支持${p}形式，用反引号来避免，`${p}`
	for _, p := range params {
		placeholder := fmt.Sprintf("`$${%s}`", p.ParamName)
		replacePatternStr := fmt.Sprintf(tempBackQuotePatternStr, "`", p.ParamName, "`")
		replacePattern := regexp.MustCompile(replacePatternStr)
		sql = replacePattern.ReplaceAllString(sql, placeholder)
	}
	p, err := parser.NewSelectParser(sql)
	if err != nil {
		return nil, err
	}
	return p, nil
}

func (am *ApiManageService) apiSqlModeValidate(ctx context.Context, project string, req proto.ApiUpdateReq) (err error) {
	// 获取当前查询引擎
	var resource *dapCommonProto.ResourceInfo
	resource, err = am.common.GetStorageResources(ctx, project)
	if err != nil {
		return
	}
	var originSql string
	var resourceType = global.ResourceType(resource.ResourceType)
	// 根据方言设置和引擎推算出使用的具体哪种方言
	var dialectType = req.CreateContent.GetDialectType(resourceType)
	if originSql, err = req.CreateContent.GetSql(resourceType); err != nil {
		return
	}
	var dialectContext = &dialect.Context{
		DialectType: dialectType,
		OriginSql:   originSql,
	}
	var dialectCompiler = dialect.NewSqlDialectCompiler(dialectContext)
	if err = dialectCompiler.Validate(ctx); err != nil {
		return
	}
	return
}

// ApiUpdate api编辑接口
func (am *ApiManageService) ApiUpdate(ctx context.Context, project string, req proto.ApiUpdateReq) (apiID string, err error) {
	err = am.apiUpdateValidate(ctx, project, &req)
	if err != nil {
		return
	}
	apiID, err = am.repo.UpdateApi(ctx, project, req)
	return
}

// checkCanRelease 检测是否可以发布
func (am *ApiManageService) checkApiCanRelease(devApi, releaseApi *base.ApiDetail) error {
	// 产品要求
	// 1.请求参数，名称，个数和类型必须完全一致
	// 2.返回参数，名称、类型必须完全一致，个数不能少于原接口个数。
	if len(devApi.RequestParams) != len(releaseApi.RequestParams) {
		return bizErrors.NewReleaseReqParamsNumErr()
	}
	if len(devApi.ResponseParams) < len(releaseApi.ResponseParams) {
		return bizErrors.NewReleaseRespParamsNumErr()
	}
	// 构造开发api字段map
	devReqMap := map[string]*base.ApiParamBase{}
	for _, reqParam := range devApi.RequestParams {
		devReqMap[reqParam.Name] = reqParam
	}
	for _, releaseParam := range releaseApi.RequestParams {
		devParam, ok := devReqMap[releaseParam.Name]
		if !ok {
			return bizErrors.NewReleaseReqLackParamsErr(releaseParam.Name)
		}
		// 类型检测
		if devParam.DataType != releaseParam.DataType {
			return bizErrors.NewReleaseReqParamTypeErr(releaseParam.Name, releaseParam.DataType, devParam.DataType)
		}
	}
	// 构造开发api字段map
	devRespMap := map[string]*base.ApiParamBase{}
	for _, respParam := range devApi.ResponseParams {
		devRespMap[unwrapQuote(respParam.Name)] = respParam
	}
	for _, releaseParam := range releaseApi.ResponseParams {
		devParam, ok := devRespMap[unwrapQuote(releaseParam.Name)]
		if !ok {
			return bizErrors.NewReleaseRespLackParamsErr(releaseParam.Name)
		}
		// 类型检测
		if devParam.DataType != releaseParam.DataType {
			return bizErrors.NewReleaseRespParamTypeErr(releaseParam.Name, releaseParam.DataType, devParam.DataType)
		}
	}
	return nil
}

func unwrapQuote(identifier string) string {
	if len(identifier) >= 2 {
		for _, quoteChar := range []string{"'", "`", "\""} {
			if strings.HasPrefix(identifier, quoteChar) && strings.HasSuffix(identifier, quoteChar) {
				return identifier[1 : len(identifier)-1]
			}
		}
	}
	return identifier
}

// CheckNameValidate 验证名称是否唯一
func (am *ApiManageService) nameValidate(ctx context.Context, project, name, groupID string) (err error) {
	flag, err := am.repo.CheckNameUniqueness(ctx, project, name, groupID)
	if err != nil {
		return
	}
	if !flag {
		return bizErrors.NewAppCodeNameRepeatErr()
	}
	return
}

// CheckPathValidate 验证路径是否唯一
func (am *ApiManageService) pathValidate(ctx context.Context, project, apiCode, apiPath string) (err error) {
	flag, err := am.repo.CheckPathUniqueness(ctx, project, apiCode, apiPath)
	if err != nil {
		return
	}
	if !flag {
		return bizErrors.NewAppCodePathRepeatErr()
	}
	return
}

// ApiOffline api下线接口
func (am *ApiManageService) ApiOffline(ctx context.Context, project, account string, req proto.ApiOfflineReq) (err error) {
	event := fast.StartEvent(pkgFast.OfflineApi, project, account)
	defer func() {
		event.EndEvent(ctx, err)
	}()

	apiDetail, err := am.repo.GetApiDetailByCode(ctx, project, req.ApiCode, base.StageRelease)
	if err == sql.ErrNoRows {
		return bizErrors.NewNotReleaseErr(req.ApiCode)
	}
	if err != nil {
		return
	}

	// 获取api主信息
	apiMain, err := am.repo.GetApiMainDetail(ctx, project, apiDetail.Code)
	if err != nil {
		return
	}

	event.RecordBizParams(am.recorder.GenerateApiReleaseFastBizParam(ctx, project, apiMain, apiDetail))
	err = am.repo.UpdateApiStageAndStatus(ctx, project, apiDetail)
	if err != nil {
		return
	}
	am.apiCache.DelMetaCache(project, apiDetail.RequestPath)
	return
}

// ApiRollback api回滚接口
func (am *ApiManageService) ApiRollback(ctx context.Context, project string, req proto.ApiRollbackReq) (apiID string, err error) {

	// 获取需要回滚的信息
	apiNeedRollback, err := am.repo.GetApiDetail(ctx, project, req.ApiID)
	if err != nil {
		return
	}
	if apiNeedRollback.Stage != base.StageHistory {
		err = bizErrors.NewRollbackErr()
		return
	}

	// 获取当前开发态
	devApi, err := am.repo.GetApiDetailByCode(ctx, project, apiNeedRollback.Code, base.StageDraft)
	if err != nil && err == sql.ErrNoRows {
		stageStr := GetDisplayStage(base.StageDraft)
		err = bizErrors.NewApICodeNotExistErr(stageStr, apiNeedRollback.Code)
		return
	}
	if err != nil {
		return
	}

	// 构建保存api🔐需要参数
	updateReq := proto.ApiUpdateReq{
		ApiID:   devApi.ID,
		ApiCode: devApi.Code,
		ApiBase: proto.ApiBase{
			RequestMethod:  apiNeedRollback.RequestMethod,
			RequestPath:    apiNeedRollback.RequestPath,
			ResponseFormat: apiNeedRollback.ResponseFormat,
		},
		Description:            apiNeedRollback.Description,
		ResponseSuccessExample: apiNeedRollback.ResponseSuccessExample,
		ResponseFailedExample:  apiNeedRollback.ResponseFailedExample,
		ResourceID:             apiNeedRollback.ResourceID,
		CreateContent:          apiNeedRollback.CreateContent,
		CreateMode:             apiNeedRollback.CreateMode,
		EnablePage:             apiNeedRollback.EnablePage,

		// 参数
		RequestParams:  apiNeedRollback.RequestParams,
		ResponseParams: apiNeedRollback.ResponseParams,
	}

	// 保存
	apiID, err = am.repo.UpdateApi(ctx, project, updateReq)
	return
}

// GetDisplayStage
func GetDisplayStage(stage base.ApiStage) (stageStr string) {
	switch stage {
	case base.StageDraft:
		stageStr = "草稿"
	case base.StageRelease:
		stageStr = "已发布"
	case base.StageHistory:
		stageStr = "历史版本"
	}
	return
}

// ApiRelease api发布接口
func (am *ApiManageService) ApiRelease(ctx context.Context, project, account string, req proto.ApiReleaseReq) (apiID string, err error) {
	// 1.获取当前开发态信息
	// 2.获取当前开发态信息
	// 3.如果已发布，校验是否符合发布条件
	// 4.构建发布api信息
	// 5.保存新的发布api信息
	// 6.配置接口限流

	// 上报天眼事件
	event := fast.StartEvent(pkgFast.ReleaseApi, project, account)
	defer func() {
		event.EndEvent(ctx, err)
	}()

	devApi, err := am.repo.GetApiDetailByCode(ctx, project, req.ApiCode, base.StageDraft)
	if err != nil && err == sql.ErrNoRows {
		stageStr := GetDisplayStage(base.StageDraft)
		err = bizErrors.NewApICodeNotExistErr(stageStr, req.ApiCode)
		return
	}
	if err != nil {
		return
	}

	// 获取api主信息
	apiMain, err := am.repo.GetApiMainDetail(ctx, project, devApi.Code)
	if err != nil {
		return
	}
	event.RecordBizParams(am.recorder.GenerateApiReleaseFastBizParam(ctx, project, apiMain, devApi))

	isRelease := true
	releaseStatus := "release"
	releaseApi, err := am.repo.GetApiDetailByCode(ctx, project, req.ApiCode, base.StageRelease)
	if err == sql.ErrNoRows {
		releaseApi = nil
		isRelease = false
		_, err = am.repo.GetApiDetailByCode(ctx, project, req.ApiCode, base.StageHistory)
		if err == sql.ErrNoRows {
			releaseStatus = "first_release"
		} else if err == nil {
			releaseStatus = "release"
		}
		err = nil
	} else if err != nil {
		return
	} else {
		defer am.apiCache.DelMetaCache(project, releaseApi.RequestPath)
	}
	if isRelease {
		err = am.checkApiCanRelease(devApi, releaseApi)
		if err != nil {
			return
		}
	}

	//获取api所引用表列表
	tables, err := am.getDataApiRefTables(ctx, project, devApi)
	if err != nil {
		devApi.RefTableSetResult = err.Error()
		am.logger.Errorf("获取api引用表列表失败: %s", err.Error())
		err = nil
	}

	// 构造proto.ApiUpdateReq, 调用updateApi接口
	newRelease := proto.ApiUpdateReq{
		ApiBase: proto.ApiBase{
			RequestMethod:  devApi.RequestMethod,
			RequestPath:    devApi.RequestPath,
			ResponseFormat: devApi.ResponseFormat,
		},
		ApiCode:                req.ApiCode,
		Description:            devApi.Description,
		ResponseSuccessExample: devApi.ResponseSuccessExample,
		ResponseFailedExample:  devApi.ResponseFailedExample,
		RequestParams:          devApi.RequestParams,
		ResponseParams:         devApi.ResponseParams,
		ResourceID:             devApi.ResourceID,
		CreateMode:             devApi.CreateMode,
		CreateContent:          devApi.CreateContent,
		EnablePage:             devApi.EnablePage,
		ReleaseStatus:          releaseStatus,
		RefTableSet:            strings.Join(tables, ","),
	}
	apiID, err = am.repo.ReleaseApi(ctx, project, newRelease)
	if err != nil {
		return
	}
	return
}

func (am *ApiManageService) ApiReleaseByProjects(ctx context.Context, projects []string) error {
	if len(projects) == 0 {
		projectList, err := am.repo.GetAllProject(ctx, 1)
		if err != nil {
			am.logger.Error("ApiReleaseByProjects get project list failed")
			return err
		}
		for _, v := range projectList {
			projects = append(projects, v.Code)
		}
	}

	am.logger.Info("数据服务api发布升级:ApiReleaseByProjects 开始")
	for _, projectCode := range projects {
		am.logger.Infof("ApiReleaseByProjects start projectCode: %s", projectCode)
		apiCodes, err := am.repo.GetAllApiCodeByProject(ctx, projectCode, base.Published)
		if err != nil {
			am.logger.Errorf("ApiReleaseByProjects  get release api failed, error: %s", err.Error())
			continue
		}
		for _, apiCode := range apiCodes {
			req := proto.ApiReleaseReq{ApiCode: apiCode}
			_, err = am.ApiRelease(ctx, projectCode, consts.ADMIN, req)
			if err != nil {
				am.logger.Errorf("ApiReleaseByProjects release failed apiCode: %s error: %s", apiCode, err.Error())
			}
		}
		am.logger.Infof("ApiReleaseByProjects end projectCode: %s", projectCode)
	}
	am.logger.Info("数据服务api发布升级:ApiReleaseByProjects 完成")

	return nil
}

func (am *ApiManageService) getDataApiRefTables(ctx context.Context, project string, apiDetail *base.ApiDetail) ([]string, error) {
	tables := make([]string, 0)
	switch apiDetail.CreateMode {
	case base.ModeView:
		for _, v := range apiDetail.CreateContent.Tables {
			tables = append(tables, v.TableName)
		}
	case base.ModeSql:
		sqlRefTables, err := am.dataApi.GetRefTables(ctx, project, apiDetail)
		if err != nil {
			return tables, err
		}
		if len(sqlRefTables) > 0 {
			tables = append(tables, sqlRefTables...)
		}
	case base.ModeIndicator:
		indicatorModelInfo, err := am.indicatorModelRepo.GetIndicatorModelByCode(ctx, project, apiDetail.CreateContent.ModelCode, pkgDi.Prod)
		if err != nil {
			return tables, err
		}
		tables = append(tables, indicatorModelInfo.Table)

		viewContentObj := indicator_model.AdsViewContent{}
		_ = json.Unmarshal([]byte(indicatorModelInfo.ViewContent), &viewContentObj)
		tables = append(tables, viewContentObj.Dependence.TableName)

		if viewContentObj.Dependence.Category == modelBase.MultiDimCate {
			multiDimModelInfo, err := am.multiDimModelRepo.GetModelByCode(ctx, project, viewContentObj.Dependence.Code, pkgDi.Prod)
			if err != nil {
				return tables, err
			}
			for _, v := range multiDimModelInfo.ViewContent.Node {
				tables = append(tables, v.TableName)
			}
		}
	}

	return tables, nil
}

// ApiDetail api详情
func (am *ApiManageService) ApiDetail(ctx context.Context, project string, req proto.ApiDetailReq) (resp *proto.ApiDetailResponse, err error) {
	if req.ApiID == "" && req.ApiCode == "" {
		err = bizErrors.NewAppIDOrCodeRequiredErr()
		return
	}
	// 支持两种方式app_id和code加stage
	if req.ApiID == "" {
		req.ApiID, err = am.repo.GetApiIDByCode(ctx, project, req.ApiCode, req.Stage)
		if err != nil {
			return
		}
	}
	apiDetail, err := am.repo.GetApiDetail(ctx, project, req.ApiID)
	if err != nil {
		return
	}
	apiMain, err := am.repo.GetApiMainDetail(ctx, project, apiDetail.Code)
	if err != nil {
		return
	}
	// 调用地址
	apiMain.ApiUrl = am.genApiUrl(project, apiDetail.RequestPath)
	resp = &proto.ApiDetailResponse{
		apiDetail,
		apiMain,
	}
	return
}

// GenAuthList
func (am *ApiManageService) GenAuthList(ctx context.Context, project, apiCode string, apiCodes []string) (authList []*proto.ApiReleaseDetailAuthAppsItem, err error) {
	var appIDs []string
	authApps, err := am.repo.ApplicationApiRelationListWithRank(ctx, project, apiCode, apiCodes)
	if err != nil {
		return
	}
	for _, item := range authApps {
		appIDs = append(appIDs, item.AppID)
	}
	appDetailMap := make(map[string]*appProto.AppListResp)
	var appList []*appProto.AppListResp
	if len(appIDs) != 0 {
		appList, err = am.appRepo.ApplicationListByIDs(ctx, project, appIDs, true)
		if err != nil {
			return
		}
	}
	for _, app := range appList {
		if int(app.Visible) == 0 {
			continue
		}
		_, ok := appDetailMap[app.ID]
		if !ok {
			appDetailMap[app.ID] = app
		}
	}

	// 鉴权应用详情列表
	for _, item := range authApps {
		appDetail, ok := appDetailMap[item.AppID]
		if !ok {
			continue
		}
		authApp := &proto.ApiReleaseDetailAuthAppsItem{
			Rank:      item.Rank,
			AppID:     item.AppID,
			Name:      appDetail.Name,
			AuthTerm:  item.AuthTerm,
			StartTime: item.StartTime,
			EndTime:   item.EndTime,
		}
		authList = append(authList, authApp)
	}
	return
}

// ApiReleaseDetail api详情
func (am *ApiManageService) ApiReleaseDetail(ctx context.Context, project string, req proto.ApiDetailReq) (resp *proto.ApiReleaseDetailResp, err error) {
	if req.ApiID == "" && req.ApiCode == "" {
		err = bizErrors.NewAppIDOrCodeRequiredErr()
		return
	}
	// 支持两种方式app_id和code加stage
	if req.ApiID == "" {
		req.ApiID, err = am.repo.GetApiIDByCode(ctx, project, req.ApiCode, req.Stage)
		if err != nil {
			return
		}
	}
	apiDetail, err := am.repo.GetApiDetail(ctx, project, req.ApiID)
	if err != nil {
		return
	}
	// 主信息
	apiMain, err := am.repo.GetApiMainProdDetail(ctx, project, apiDetail.Code)
	if err != nil {
		return
	}
	// 调用地址
	apiUrl := am.genApiUrl(project, apiDetail.RequestPath)
	apiMain.ApiUrl = apiUrl

	// 鉴权关联应用列表
	authList, err := am.GenAuthList(ctx, project, apiMain.ApiCode, []string{})
	if err != nil {
		return
	}

	// 错误码
	apiErrs := apierr.GetAPIErrs()

	resp = &proto.ApiReleaseDetailResp{
		apiDetail,
		apiMain,
		&proto.ApiReleaseDetailAuthApps{AuthApps: authList},
		&proto.ApiReleaseDetailApiErrs{ApiErrs: apiErrs},
	}
	return
}

// ApiVersionList api版本详情
func (am *ApiManageService) ApiVersionList(ctx context.Context, project string, req proto.ApiVersionReq) (resp proto.ApiVersionResp, err error) {
	resp, err = am.repo.GetApiVersionList(ctx, project, req)
	return
}

func (am *ApiManageService) ApiTestRun(ctx context.Context, c *gin.Context, project string, req proto.ApiTestRunReq) (*response.ApiQueryResult, error) {
	err := am.validateApiParam(req.ApiMeta.RequestParams, req.ApiMeta.ResponseParams)
	if err != nil {
		return nil, err
	}

	inputParams := data_api.DiscardEmptyInputParams(req.InputParams)
	resp, sqllist, _, _ := am.dataApi.QueryData(ctx, c, project, req.ApiMeta, inputParams)
	resp.Sql = sqllist

	return resp, nil
}

// ApiTestResult 保存测试结果
func (am *ApiManageService) ApiTestResult(ctx context.Context, project string, req proto.ApiTestResultReq) (err error) {
	err = am.repo.UpdateApiTestResult(ctx, project, req)
	return
}

// parseToLocalTime
func parseToLocalTime(t string) (newT db.NullTime) {
	loc, _ := time.LoadLocation("Local")
	localT, _ := time.ParseInLocation(db.TimeFormat, t, loc)
	return db.NullTime{mysql.NullTime{Time: localT, Valid: true}}
}

// ApiAuthorize 授权
func (am *ApiManageService) ApiAuthorize(ctx context.Context, project string, req proto.ApiAuthorizeReq) (err error) {
	if len(req.ApiCodes) == 0 {
		err = utils.NewUserError("授权接口code不能为空")
		return
	}

	apiDetails, err := am.repo.BatchGetApiDetailByCodes(ctx, project, req.ApiCodes, base.StageRelease)
	if err != nil {
		return
	}

	if len(req.Apps) == 0 {
		err = utils.NewUserError("授权应用ID不能为空")
		return
	}
	var appIDs []string
	for _, app := range req.Apps {
		appIDs = append(appIDs, app.AppID)
	}
	appList, err := am.appRepo.ApplicationListByIDs(ctx, project, appIDs, true)
	if err != nil {
		return
	}
	if len(req.Apps) != len(appList) {
		err = utils.NewUserError("存在无效的应用")
		return
	}

	for _, app := range appList {
		if appBase.IsSystemApp(app.Type) {
			err = utils.NewUserError("不能授权系统应用")
			return
		}
	}

	succApis := make([]string, 0)

	var ierr error
	for _, apiDetail := range apiDetails {
		am.apiCache.DelMetaCache(project, apiDetail.RequestPath)
		ierr = am.apiAuth(ctx, project, apiDetail.Code, req)
		if ierr != nil {
			break
		}
		succApis = append(succApis, apiDetail.RequestPath)
	}

	if ierr != nil && len(succApis) != len(req.ApiCodes) && len(succApis) >= 0 {
		err = utils.NewUserError("部分接口授权失败, 原因: %s", ierr.Error())
		return
	}

	return
}

// apiAuth 单个接口授权
func (am *ApiManageService) apiAuth(ctx context.Context, project string, apiCode string, req proto.ApiAuthorizeReq) (err error) {
	// 获取TX
	tx, err := am.repo.GetTx(ctx, project)
	if err != nil {
		return
	}
	defer func() {
		if e := recover(); e != nil {
			debug.PrintStack()
			var ok bool
			if err, ok = e.(error); ok {
				am.logger.Errorf("授权api失败: %s", err.Error())
			}
			_ = tx.Rollback()
		} else if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		} else {
			_ = tx.Commit()
		}
	}()
	// replace模式删除原有的关联关系
	if req.AuthMethod == base.AuthMethodReplace {
		err = am.repo.BatchDeleteApplicationApiRelation(ctx, tx, apiCode, []string{})
		if err != nil {
			return
		}
	}
	for _, app := range req.Apps {
		switch app.AuthTerm {
		case base.Forever:
			if app.StartTime != "" || app.EndTime != "" {
				err = utils.NewUserError("永久授权模式无法设置授权使用期限")
				return
			}
		case base.Custom:
			if app.StartTime == "" || app.EndTime == "" {
				err = utils.NewUserError("非法的授权使用期限")
				return
			}
		}
		var startTime db.NullTime
		var endTime db.NullTime
		if app.StartTime != "" {
			startTime = parseToLocalTime(app.StartTime)
		}
		if app.EndTime != "" {
			endTime = parseToLocalTime(app.EndTime)
		}
		if startTime.Valid && endTime.Valid && startTime.Time.After(endTime.Time) {
			err = utils.NewUserError("授权使用期限的起始时间不能晚于截止时间")
			return
		}
		newRelation := base.ApplicationApi{
			ApiCode:   apiCode,
			AppID:     app.AppID,
			AuthTerm:  app.AuthTerm,
			StartTime: startTime,
			EndTime:   endTime,
		}
		err = am.repo.ReplaceApplicationApiRelation(ctx, tx, newRelation)
		if err != nil {
			return
		}
	}
	return
}

// ApiUnbind 解绑
func (am *ApiManageService) ApiUnbind(ctx context.Context, project string, req proto.ApiUnbindReq, checkAppsFlag bool, tx *sql.Tx) (err error) {
	if len(req.Apps) == 0 {
		err = utils.NewUserError("授权应用ID不能为空")
		return
	}
	appList, err := am.appRepo.ApplicationListByIDs(ctx, project, req.Apps, true)
	if err != nil {
		return
	}
	if checkAppsFlag && len(req.Apps) != len(appList) {
		err = utils.NewUserError("存在无效的应用")
		return
	}

	// 获取TX
	if tx == nil {
		tx, err = am.repo.GetTx(ctx, project)
		if err != nil {
			return
		}
		defer func() {
			if err != nil {
				_ = tx.Rollback()
			} else {
				_ = tx.Commit()
			}
		}()
	}
	err = am.repo.BatchDeleteApplicationApiRelation(ctx, tx, req.ApiCode, req.Apps)
	if err != nil {
		return
	}

	// 如果api已下线，则无需后续步骤
	apiMainDetail, err := am.repo.GetApiMainDetail(ctx, project, req.ApiCode)
	if err != nil {
		return
	}
	if apiMainDetail.Status == base.Dev {
		return
	}

	// 删除已发布api的缓存
	detail, err := am.repo.GetApiDetailByCode(ctx, project, req.ApiCode, base.StageRelease)
	if err == nil {
		am.apiCache.DelMetaCache(project, detail.RequestPath)
	}

	return
}

// ApiUpsetLimit 配置接口限流
func (am *ApiManageService) ApiUpsetLimit(ctx context.Context, project string, req proto.ApiUpSetLimitReq) (err error) {
	apiDetail, err := am.repo.GetApiDetailByCode(ctx, project, req.ApiCode, base.StageRelease)
	stageStr := GetDisplayStage(base.StageRelease)
	if err == sql.ErrNoRows {
		err = bizErrors.NewApICodeNotExistErr(stageStr, req.ApiCode)
		return
	}
	if err != nil {
		return
	}
	if apiDetail == nil {
		err = bizErrors.NewApICodeNotExistErr(stageStr, req.ApiCode)
		return
	}
	defer am.apiCache.DelMetaCache(project, apiDetail.RequestPath)

	// 获取TX
	tx, err := am.repo.GetTx(ctx, project)
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()
	err = am.repo.ApiUpsetLimit(ctx, tx, req)
	if err != nil {
		return
	}
	err = tx.Commit()
	return
}

// GenAppJwtToken 生成应用的调用的jwt token
func (am *ApiManageService) GenAppJwtToken(ctx context.Context, project string, req proto.GenAppJwtTokenReq) (resp proto.GenAppJwtTokenResp, err error) {
	return am.GenUnifiedAuthenticationToken(ctx, project)
}

const frontTokenExp = 60 * 15

func (am *ApiManageService) GenUnifiedAuthenticationToken(ctx context.Context, tenant string) (resp proto.GenAppJwtTokenResp, err error) {
	resp = proto.GenAppJwtTokenResp{}
	jws, err := sm3Jwt.NewJwkSets(global.AppConfig.GetSkylineAppSecret(tenant), "天际", global.AppConfig.JWT.Expires)
	if err != nil {
		return
	}

	token, err := jws.CreateToken(frontTokenExp)
	if err != nil {
		return
	}
	resp.Jwt = token
	return
}

func (am *ApiManageService) checkJwtTokenValid(ctx context.Context, project string, req proto.GenAppJwtTokenReq, appDetail *appBase.Application) (err error) {
	apiDetail, err1 := am.repo.GetApiDetail(ctx, project, req.ApiID)
	if err1 != nil {
		err = err1
		return
	}
	if apiDetail.Stage != base.StageRelease {
		_, err1 := am.repo.GetApiDetailByCode(ctx, project, apiDetail.Code, base.StageRelease)
		if err1 != nil {
			if err1 == sql.ErrNoRows {
				err = utils.NewUserError("接口API:%s未发布", req.ApiID)
				return
			}
			err = err1
			return
		}
	}

	apiApps, err1 := am.repo.ApplicationApiRelationList(ctx, project, apiDetail.Code)
	if err1 != nil {
		err = err1
		return
	}
	var authApp *base.ApplicationApi
	for _, apiApp := range apiApps {
		if apiApp.AppID == appDetail.ID {
			authApp = apiApp
			break
		}
	}
	if authApp == nil {
		err = utils.NewUserError("应用[%s]未被授权，无法生成有效token", appDetail.Name)
		return
	}
	nowTime := time.Now()
	if authApp.AuthTerm == base.Custom && (authApp.StartTime.Time.After(nowTime) || authApp.EndTime.Time.Before(nowTime)) {
		err = utils.NewUserError("应用[%s]授权未生效或已过期，无法生成有效token", appDetail.Name)
		return
	}
	return
}

func (am *ApiManageService) apiIndicatorModeValidate(req *proto.ApiUpdateReq) (err error) {
	if len(req.CreateContent.ModelCode) == 0 {
		err = fmt.Errorf("模型不能为空")
	}

	if len(req.CreateContent.SubjectId) == 0 {
		err = fmt.Errorf("主题域不能为空")
	}

	return
}

func (am *ApiManageService) GetApiListWithOpenApi(ctx context.Context, req proto.ApiListWithOpenApiReq) (resp []*proto.ApiListWithOpenApiResp, err error) {
	resp = make([]*proto.ApiListWithOpenApiResp, 0)
	projectCode := req.ProjectCode
	if req.TenantCode != "" {
		tenantProjectRelInfo := &dapCommonProto.TenantProjectRelInfo{}
		tenantProjectRelInfo, err = am.common.GetTenantProjectRel(ctx, req.TenantCode)
		if err != nil {
			return
		}
		if tenantProjectRelInfo.CustomProjectCode != "" {
			projectCode = tenantProjectRelInfo.CustomProjectCode
		} else if tenantProjectRelInfo.CommonProjectCode != "" {
			projectCode = tenantProjectRelInfo.CommonProjectCode
		}
	}
	apiGroupList, err := am.apiGroupRepo.GetGroupListWithSubjectIds(ctx, projectCode, lo.Ternary(req.SubjectId == "", []string{}, []string{req.SubjectId}))
	if err != nil {
		return
	}
	apiGroupIds := make([]string, 0)
	for _, apiGroup := range apiGroupList {
		apiGroupIds = append(apiGroupIds, apiGroup.Id)
	}
	apiMainList, err := am.apiGroupRepo.GetGroupApiListWithOpenApi(ctx, projectCode, apiGroupIds)
	if err != nil {
		return
	}
	apiIds := make([]string, 0)
	apiMainListMap := make(map[string][]*proto.GroupApiListWithOpenApiResp)
	for _, api := range apiMainList {
		apiIds = append(apiIds, api.Id)
		apiMainListMap[api.GroupId] = append(apiMainListMap[api.GroupId], api)
	}
	apiInfoList, err := am.repo.BatchGetApiDetail(ctx, projectCode, apiIds)
	if err != nil {
		return
	}
	apiInfoMap := make(map[string]*base.ApiDetail)
	for _, api := range apiInfoList {
		apiInfoMap[api.ID] = api
	}
	for _, v := range apiGroupList {
		info := &proto.ApiListWithOpenApiResp{
			SubjectId: v.SubjectId,
			Name:      v.Name,
			Apis:      make([]*proto.ApiListWithOpenApiInfo, 0),
		}
		for _, api := range apiMainListMap[v.Id] {
			apiDetail := apiInfoMap[api.Id]
			if apiDetail == nil {
				continue
			}
			info.Apis = append(info.Apis, &proto.ApiListWithOpenApiInfo{
				Name:           api.Name,
				Method:         api.RequestMethod,
				ApiUrl:         am.genApiUrl(projectCode, apiDetail.RequestPath),
				RequestParams:  apiDetail.RequestParams,
				ResponseParams: apiDetail.ResponseParams,
			})
		}
		resp = append(resp, info)
	}
	return
}
