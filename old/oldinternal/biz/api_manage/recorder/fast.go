package recorder

import (
	"context"
	"time"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/repo"
	fastBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/base"
)

const (
	CHINESE_WORD_YES = "是"
	CHINESE_WORD_NO  = "否"
	DYNAMICCOLUMNS   = "dynamicColumns"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewApiManagerFastRecorder))
}

type ApiManagerFastRecorder struct {
	groupRepo repo.ApiGroupRepo
}

func NewApiManagerFastRecorder(groupRepo repo.ApiGroupRepo) *ApiManagerFastRecorder {
	return &ApiManagerFastRecorder{
		groupRepo: groupRepo,
	}
}

// 发布日志参数
func (s *ApiManagerFastRecorder) GenerateApiReleaseFastBizParam(ctx context.Context, project string, apiMain *base.ApiMain, apiDetail *base.ApiDetail) fastBase.BizParamsGenerator {
	return func() map[string]interface{} {
		res := map[string]interface{}{}
		res["api_name"] = apiMain.Name
		res["api_code"] = apiMain.Code
		group, err := s.groupRepo.GetGroupDetail(ctx, project, apiMain.GroupID)
		if err == nil {
			res["group_name"] = group.Name
		}
		res["create_model"] = apiDetail.CreateMode
		res["request_path"] = apiDetail.RequestPath
		res["request_method"] = apiDetail.RequestMethod
		res["response_format"] = apiDetail.ResponseFormat
		res["ref_table_set_result"] = apiDetail.RefTableSetResult

		bDynamic := CHINESE_WORD_NO
		reqCount := len(apiDetail.RequestParams)
		request_params := ""
		for index, requestParam := range apiDetail.RequestParams {
			if DYNAMICCOLUMNS == requestParam.Name {
				bDynamic = CHINESE_WORD_YES
			}

			if index < reqCount-1 {
				request_params = request_params + requestParam.Name + ","
			} else {
				request_params = request_params + requestParam.Name
			}
		}
		res["dynamic"] = bDynamic
		res["request_params"] = request_params

		response_params := ""
		reqCount = len(apiDetail.ResponseParams)
		for index, responseParam := range apiDetail.ResponseParams {
			if index < reqCount-1 {
				response_params = response_params + responseParam.Name + ","
			} else {
				response_params = response_params + responseParam.Name
			}
		}

		res["response_params"] = response_params

		quote_table := ""
		reqCount = len(apiDetail.CreateContent.Tables)
		for index, table := range apiDetail.CreateContent.Tables {
			if index < reqCount-1 {
				quote_table = quote_table + table.TableName + ","
			} else {
				quote_table = quote_table + table.TableName
			}
		}

		res["quote_table"] = quote_table
		if len(apiDetail.CreateContent.Sorts) > 0 {
			res["sort_column"] = apiDetail.CreateContent.Sorts[0].Column
			if "ASC" == apiDetail.CreateContent.Sorts[0].Method {
				res["sort"] = "正序"
			} else {
				res["sort"] = "倒序"
			}
		}

		if apiDetail.EnablePage == 0 {
			res["enable_page"] = CHINESE_WORD_NO
		} else {
			res["enable_page"] = CHINESE_WORD_YES
		}

		res["time"] = time.Now().Format("2006-01-02 15:04:05.000000")
		return res
	}
}
