package repo

import (
	"context"
	"database/sql"
	"strings"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/context_helper"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/saas_db"

	"github.com/jmoiron/sqlx"
	pkg "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	baseStore "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/base/store"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/proto"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewApiGroupStore, inject.As(new(ApiGroupRepo))))
}

type ApiGroupRepo interface {
	// 根据分组获取api列表
	GetGroupApiList(ctx context.Context, project string, groupIDs []string) (resp []*proto.GroupApiListResp, err error)
	// 根据分组获取api列表-对外
	GetGroupApiListWithOpenApi(ctx context.Context, project string, groupIDs []string) (resp []*proto.GroupApiListWithOpenApiResp, err error)
	// 获取分组下的api列表
	GetGroupApiListWithApis(ctx context.Context, project, subjectId string) ([]*base.ApiGroup, error)
	// 获取分组详情
	GetGroupDetail(ctx context.Context, project, groupID string) (group *base.ApiGroup, err error)
	GormGetGroupDetail(ctx context.Context, project, groupID string) (group *base.ApiGroup, err error)
	// 批量获取分组详情
	BatchGetGroupDetail(ctx context.Context, project string, groupIDs []string) (groups []*base.ApiGroup, err error)
	// 检查分组是否已存在
	CheckGroupExist(ctx context.Context, project, groupID, name, subjectId string) (group *base.ApiGroup, err error)
	//获取分组列表
	GetGroupList(ctx context.Context, project string) (groupList []*base.ApiGroup, err error)
	// 根据主题id获取分组列表
	GetGroupListWithSubjectIds(ctx context.Context, project string, subjectIds []string) (groupList []*base.ApiGroup, err error)
	// 新增分组
	AddGroup(ctx context.Context, project string, req proto.GroupAddReq) (groupID string, err error)
	// 更新分组
	UpdateGroup(ctx context.Context, project string, req proto.GroupUpdateReq) (err error)
	// 删除分组
	DeleteGroup(ctx context.Context, project, groupID string) (err error)
}

type ApiGroupStore struct {
	*baseStore.Repo
}

func NewApiGroupStore(saas db.SaaS) *ApiGroupStore {
	return &ApiGroupStore{
		Repo: baseStore.NewRepo(saas),
	}
}

// GetGroupApiList 根据分组获取api列表
func (s *ApiGroupStore) GetGroupApiList(ctx context.Context, project string, groupIDs []string) (resp []*proto.GroupApiListResp, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	sqlText := "select am.code,am.name,am.group_id,am.status,am.is_updated,am.rate_limit,am.timeout_limit, api.create_mode from dap_m_api_main am left join (select code, create_mode from dap_m_api where stage = 0) api on api.code = am.code "

	var whereStr string
	args := map[string]interface{}{}
	if len(groupIDs) > 0 {
		args["groupIDs"] = groupIDs
		whereStr = "where group_id in (:groupIDs) "
	}
	sqlText += whereStr

	orderStr := "order by created_on desc "
	sqlText += orderStr

	resp = []*proto.GroupApiListResp{}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.SelectContext(ctx, &resp, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}
	return
}

func (s *ApiGroupStore) GetGroupApiListWithOpenApi(ctx context.Context, project string, groupIDs []string) (resp []*proto.GroupApiListWithOpenApiResp, err error) {
	resp = make([]*proto.GroupApiListWithOpenApiResp, 0)
	if len(groupIDs) == 0 {
		return
	}
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}

	sqlText := "select api.id,api.request_method,api.request_path,am.name,am.group_id from dap_m_api api left join dap_m_api_main am on api.code = am.code  where am.status='已发布' and stage = 1 and group_id in (:groupIDs)"
	args := map[string]interface{}{
		"groupIDs": groupIDs,
	}

	resp = []*proto.GroupApiListWithOpenApiResp{}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.SelectContext(ctx, &resp, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}
	return
}

// GetGroupApiListWithApis 获取分组下的api列表
func (s *ApiGroupStore) GetGroupApiListWithApis(ctx context.Context, project, subjectId string) ([]*base.ApiGroup, error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	sqlText := "select id,name,description,created_by,created_on,modified_by,modified_on" +
		" from dap_m_api_group where subject_id = :subjectId order by created_on desc "
	args := map[string]interface{}{
		"subjectId": subjectId,
	}
	var groupList []*base.ApiGroup
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return nil, err
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return nil, err
	}
	if err = sess.SelectContext(ctx, &groupList, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return nil, err
	}
	return groupList, nil
}

// AddGroup 新增分组
func (s *ApiGroupStore) AddGroup(ctx context.Context, project string, req proto.GroupAddReq) (groupID string, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	account := context_helper.MustGetSessionAccount(ctx)
	sqlText := "insert into dap_m_api_group (id,name,description,subject_id,created_by,modified_by) values " +
		"(:id,:name,:description,:subject_id,:createdBy,:modifiedBy) "

	newGroupID := pkg.NewMysqlID()
	args := map[string]interface{}{
		"id":          newGroupID,
		"name":        req.Name,
		"description": req.Description,
		"subject_id":  req.SubjectId,
		"createdBy":   account,
		"modifiedBy":  account,
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	_, err = sess.ExecContext(ctx, query, queryArgs...)
	if err != nil {
		return
	}
	groupID = newGroupID
	return

}

// UpdateGroup 更新分组
func (s *ApiGroupStore) UpdateGroup(ctx context.Context, project string, req proto.GroupUpdateReq) (err error) {
	fieldsValues := make(map[string]interface{})
	fieldsValues["name"] = req.Name
	fieldsValues["description"] = req.Description
	err = s.Update(ctx, project, req.Id, "dap_m_api_group", fieldsValues)
	return
}

// DeleteGroup 删除分组
func (s *ApiGroupStore) DeleteGroup(ctx context.Context, project, groupID string) (err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	sqlText := "delete from dap_m_api_group where id = :groupID "
	args := map[string]interface{}{
		"groupID": groupID,
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	_, err = sess.ExecContext(ctx, query, queryArgs...)
	return
}

// GetGroupDetail 获取分组详情
func (s *ApiGroupStore) GetGroupDetail(ctx context.Context, project, groupID string) (group *base.ApiGroup, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	var obj base.ApiGroup
	sqlQuery := "select id,name,description,created_by,created_on,modified_by,modified_on " +
		"from dap_m_api_group where id = ? limit 1 "
	if err = sess.GetContext(ctx, &obj, sqlQuery, groupID); err != nil && err != sql.ErrNoRows {
		return
	}
	return &obj, nil
}

func (s *ApiGroupStore) GormGetGroupDetail(ctx context.Context, project string, id string) (group *base.ApiGroup, err error) {
	sess, err := saas_db.GetProjectDB(ctx, project)
	if err != nil {
		return
	}
	if err = sess.Where("id=?", id).Take(&group).Error; err != nil {
		err = errors.WrapGorm(err)
		return
	}

	return
}

// BatchGetGroupDetail 批量获取分组详情
func (s *ApiGroupStore) BatchGetGroupDetail(ctx context.Context, project string, groupIDs []string) (group []*base.ApiGroup, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	var objs []*base.ApiGroup

	sqlText := "select id,name,description,created_by,created_on,modified_by,modified_on " +
		"from dap_m_api_group where id in (:ids)"
	args := map[string]interface{}{
		"ids": groupIDs,
	}

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}

	if err = sess.SelectContext(ctx, &objs, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}
	return objs, nil
}

// GetGroupByName 根据名称获取分组信息
func (s *ApiGroupStore) CheckGroupExist(ctx context.Context, project, groupID, name, subjectId string) (group *base.ApiGroup, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	var obj base.ApiGroup
	sqlQuery := "select id,name,description from dap_m_api_group "
	var whereSQLs []string
	args := make(map[string]interface{})
	if groupID != "" {
		args["groupID"] = groupID
		whereSQLs = append(whereSQLs, " id != :groupID ")
	}
	if name != "" {
		args["name"] = name
		whereSQLs = append(whereSQLs, " name = :name ")
	}

	if subjectId != "" {
		args["subjectId"] = subjectId
		whereSQLs = append(whereSQLs, " subject_id = :subjectId ")
	}

	if len(whereSQLs) != 0 {
		whereStr := "where " + strings.Join(whereSQLs, " and ")
		sqlQuery += whereStr
	}
	limitStr := "limit 1 "
	sqlQuery += limitStr
	query, queryArgs, err := sqlx.Named(sqlQuery, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.GetContext(ctx, &obj, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}
	return &obj, nil
}

// GetGroupList 获取分组列表
func (s *ApiGroupStore) GetGroupList(ctx context.Context, project string) (groupList []*base.ApiGroup, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	sqlText := "select id,name,description,subject_id,created_on,created_by,modified_on,modified_by " +
		"from dap_m_api_group order by created_on desc "

	groupList = []*base.ApiGroup{}
	args := map[string]interface{}{}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.SelectContext(ctx, &groupList, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}
	return groupList, nil
}

func (s *ApiGroupStore) GetGroupListWithSubjectIds(ctx context.Context, project string, subjectIds []string) (groupList []*base.ApiGroup, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	sqlText := "select id,name,description,subject_id,created_on,created_by,modified_on,modified_by " +
		"from dap_m_api_group  "

	groupList = []*base.ApiGroup{}
	var whereSQLs []string
	args := make(map[string]interface{})

	if len(subjectIds) != 0 {
		args["subjectIds"] = subjectIds
		whereSQLs = append(whereSQLs, " subject_id in (:subjectIds) ")
	}

	if len(whereSQLs) != 0 {
		whereStr := "where " + strings.Join(whereSQLs, " and ")
		sqlText += whereStr
	}
	sqlText += " order by created_on desc "

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.SelectContext(ctx, &groupList, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}
	return groupList, nil
}
