package repo

import (
	"context"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/saas_db"

	"github.com/defval/inject/v2"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/proto"
)

func init() {
	global.InjectContext = append(global.InjectContext, inject.Provide(NewIndicatorStore, inject.As(new(IndicatorRepo))))
}

type IndicatorRepo interface {
	GetIndicator(ctx context.Context, project string, code string) (resp proto.IndicatorBrief, err error)
}

type IndicatorStore struct {
}

func NewIndicatorStore() *IndicatorStore {
	return &IndicatorStore{}
}

func (s *IndicatorStore) GetIndicator(ctx context.Context, project string, code string) (brief proto.IndicatorBrief, err error) {
	db, err := saas_db.GetProjectDB(ctx, project)
	if err != nil {
		return
	}

	err = db.Raw("select code, table_name from dap_m_indicator where code = ? and environment = ?", code, global.Prod).Scan(&brief).Error
	if err != nil {
		err = errors.WrapGorm(err)
		return
	}
	return
}
