package repo

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/db_helper"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/context_helper"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/saas_db"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/json_utils"

	"github.com/jmoiron/sqlx"

	"github.com/defval/inject/v2"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	baseStore "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/base/store"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/proto"
	pkg "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"

	apiErrors "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/errors"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

func init() {
	global.InjectContext = append(global.InjectContext, inject.Provide(NewApiManageStore, inject.As(new(ApiManageRepo))))
}

type ApiManageRepo interface {
	// 获取Tx，支持外层做事务
	GetTx(ctx context.Context, project string) (tx *sql.Tx, err error)
	// 新增api信息
	AddApi(ctx context.Context, project string, req proto.ApiAddReq) (resp proto.ApiAddResp, err error)
	// 获取api列表
	GetApiList(ctx context.Context, project string, req proto.ApiListReq) (apiList []*proto.ApiListDbItem, total int, err error)
	// 编辑api信息
	UpdateApi(ctx context.Context, project string, req proto.ApiUpdateReq) (apiID string, err error)
	// 删除api
	DeleteApi(ctx context.Context, project string, tx *sql.Tx, req proto.ApiDeleteReq) (err error)
	// 发布api
	ReleaseApi(ctx context.Context, project string, req proto.ApiUpdateReq) (apiID string, err error)
	// 获取api详情信息
	GetApiDetail(ctx context.Context, project, apiID string) (detail *base.ApiDetail, err error)
	// 获取api详情信息
	GetApiMainDetail(ctx context.Context, project, apiCode string) (detail *base.ApiMain, err error)
	GormGetApiMainDetail(ctx context.Context, project string, apiCode string) (detail *base.ApiMain, err error)
	// 批量获取api详情
	BatchGetApiMainDetail(ctx context.Context, project string, req proto.BatchGetApiMainReq) (detail []*base.ApiMain, err error)
	// 获取api详情信息(通过code)
	GetApiDetailByCode(ctx context.Context, project, code string, stage base.ApiStage) (detail *base.ApiDetail, err error)
	// 批量获取api详情信息
	BatchGetApiDetailByCodes(ctx context.Context, project string, codes []string, stage base.ApiStage) (details []*base.ApiDetail, err error)
	// GetApiDetailByPath 通过request_path获取发布的api的详情
	GormGetApiDetailByPath(ctx context.Context, project, requestPath string) (detail *base.ApiDetail, err error)
	// CheckPathUniqueness 检验path是否唯一
	CheckPathUniqueness(ctx context.Context, project, apiCode, requestPath string) (flag bool, err error)
	// CheckNameUniqueness 检验name是否唯一
	CheckNameUniqueness(ctx context.Context, project, name, groupID string) (flag bool, err error)
	// 通过code和stage获取api_id
	GetApiIDByCode(ctx context.Context, project, code string, stage base.ApiStage) (apiID string, err error)
	getNextVersion(ctx context.Context, project, code string) (version int32, err error)
	// 获取api发布后详情
	GetApiMainProdDetail(ctx context.Context, project, apiCode string) (result *proto.ApiReleaseDetailMain, err error)

	// 修改api阶段和状态
	UpdateApiStageAndStatus(ctx context.Context, project string, detail *base.ApiDetail) error

	// 获取api版本详情
	GetApiVersionList(ctx context.Context, project string, req proto.ApiVersionReq) (result proto.ApiVersionResp, err error)
	// 保存api测试结果
	UpdateApiTestResult(ctx context.Context, project string, req proto.ApiTestResultReq) (err error)
	// 获取授权关联Api列表
	GetApplicationAuthApiList(ctx context.Context, req proto.ApplicationAuthApiReq) ([]*proto.ApplicationAuthApiItemResp, int, error)
	// 获取项目信息
	GetProject(ctx context.Context, project string) (*entities.Project, error)
	// 获取所有项目信息
	GetAllProject(ctx context.Context, status int) ([]*entities.Project, error)
	// 替换授权记录
	ReplaceApplicationApiRelation(ctx context.Context, tx *sql.Tx, req base.ApplicationApi) (err error)
	// 批量删除授权记录
	BatchDeleteApplicationApiRelation(ctx context.Context, tx *sql.Tx, apiCode string, appIDs []string) (err error)
	// 获取关联列表
	ApplicationApiRelationList(ctx context.Context, project, apiCode string) (relationList []*base.ApplicationApi, err error)
	GormApplicationApiRelationList(ctx context.Context, project string, apiCode string) (relationList []*base.ApplicationApi, err error)
	// 获取关联列表
	ApplicationApiRelationListWithRank(ctx context.Context, project, apiCode string, apiCodes []string) (relationList []*proto.ApplicationApiItemResp, err error)
	// 配置接口限流
	ApiUpsetLimit(ctx context.Context, tx *sql.Tx, req proto.ApiUpSetLimitReq) (err error)

	//获取授权应用信息
	GetAuthAppList(ctx context.Context, project, code string) (apps []base.MiniAuthApp, err error)

	GetAllApiCodeByProject(ctx context.Context, project string, stage base.ApiStatus) (apiCodes []string, err error)
	BatchGetApiDetail(ctx context.Context, project string, apiIDs []string) (details []*base.ApiDetail, err error)
	GetApiCountBySubjectId(ctx context.Context, project string) (subjectMap map[string]int, err error)
}

var _ ApiManageRepo = (*ApiManageStore)(nil)

type ApiManageStore struct {
	*baseStore.Repo
}

func NewApiManageStore(saas db.SaaS) *ApiManageStore {
	return &ApiManageStore{
		Repo: baseStore.NewRepo(saas),
	}
}

type ApiMainEntity struct {
	Code         string         `gorm:"code" json:"code" db:"code"`
	Name         string         `gorm:"name" json:"name" db:"name"`
	GroupID      string         `gorm:"group_id" json:"group_id" db:"group_id"`
	Status       base.ApiStatus `gorm:"status" json:"status" db:"status"`
	IsUpdated    int32          `gorm:"is_updated" json:"is_updated" db:"is_updated"`
	RateLimit    int64          `gorm:"rate_limit" json:"rate_limit" db:"rate_limit"`
	TimeoutLimit int64          `gorm:"timeout_limit" json:"timeout_limit" db:"timeout_limit"`

	CreatedBy  db.NullString `gorm:"created_by" json:"created_by" db:"created_by"`
	ModifiedBy db.NullString `gorm:"modified_by" json:"modified_by" db:"modified_by"`
}

func (s *ApiMainEntity) TableName() string {
	return "dap_m_api_main"
}

func (s *ApiMainEntity) ToBiz() *base.ApiMain {
	return &base.ApiMain{
		Code:         s.Code,
		Name:         s.Name,
		GroupID:      s.GroupID,
		Status:       s.Status,
		IsUpdated:    s.IsUpdated,
		RateLimit:    s.RateLimit,
		TimeoutLimit: s.TimeoutLimit,
	}
}

type ApiEntity struct {
	ID             string        `gorm:"id" json:"id" db:"id"`
	Code           string        `gorm:"code" json:"code" db:"code"`
	Description    string        `gorm:"description" json:"description" db:"description"`
	Stage          base.ApiStage `gorm:"stage" json:"stage" db:"stage"`
	Version        int32         `gorm:"version" json:"version" db:"version"`
	ReleaseComment db.NullString `gorm:"release_comment" json:"release_comment" db:"release_comment"`

	RequestMethod          base.ApiRequestMethod  `gorm:"request_method" json:"request_method" db:"request_method"`
	RequestPath            db.NullString          `gorm:"request_path" json:"request_path" db:"request_path"`
	ResponseFormat         base.ApiResponseFormat `gorm:"response_format" json:"response_format" db:"response_format"`
	ResponseSuccessExample db.NullString          `gorm:"response_success_example" json:"response_success_example" db:"response_success_example"`
	ResponseFailedExample  db.NullString          `gorm:"response_failed_example" json:"response_failed_example" db:"response_failed_example"`

	ResourceID    string             `gorm:"resource_id" json:"resource_id" db:"resource_id"`
	CreateMode    base.ApiCreateMode `gorm:"create_mode" json:"create_mode" db:"create_mode"`
	CreateContent db.NullString      `gorm:"create_content" json:"create_content" db:"create_content"`
	EnablePage    base.ApiEnablePage `gorm:"enable_page" json:"enable_page" db:"enable_page"`

	CreatedBy  db.NullString `gorm:"created_by" json:"created_by" db:"created_by"`
	CreatedOn  db.NullTime   `gorm:"created_on" json:"created_on" db:"created_on"`
	ModifiedBy db.NullString `gorm:"modified_by" json:"modified_by" db:"modified_by"`
	ModifiedOn db.NullTime   `gorm:"modified_on" json:"modified_on" db:"modified_on"`
}

func (s *ApiEntity) TableName() string {
	return "dap_m_api"
}

func (s *ApiEntity) ToBiz() (*base.ApiDetail, error) {
	result := &base.ApiDetail{
		ID:                     s.ID,
		Code:                   s.Code,
		Stage:                  s.Stage,
		Description:            s.Description,
		Version:                s.Version,
		ReleaseComment:         s.ReleaseComment.String,
		RequestMethod:          s.RequestMethod,
		RequestPath:            s.RequestPath.String,
		ResponseFormat:         s.ResponseFormat,
		ResponseSuccessExample: s.ResponseSuccessExample.String,
		ResponseFailedExample:  s.ResponseFailedExample.String,
		ResourceID:             s.ResourceID,
		CreateMode:             s.CreateMode,
		EnablePage:             s.EnablePage,
		CreatedBy:              s.CreatedBy.String,
		ModifiedBy:             s.ModifiedBy.String,
	}
	if s.CreateContent.Valid {
		err := json_utils.Unmarshal([]byte(s.CreateContent.String), &result.CreateContent)
		if err != nil {
			return result, err
		}
	}
	if s.CreatedOn.Valid {
		result.CreatedOn = s.CreatedOn.Time.Format(db.TimeFormat)
	}
	if s.ModifiedOn.Valid {
		result.ModifiedOn = s.ModifiedOn.Time.Format(db.TimeFormat)
	}
	return result, nil
}

type ApiParamEntity struct {
	ID              string                `gorm:"id" json:"id" db:"id"`
	ApiID           string                `gorm:"api_id" json:"api_id" db:"api_id"`
	Name            string                `gorm:"name" json:"name" db:"name"`
	Description     db.NullString         `gorm:"description" json:"description" db:"description"`
	DataType        string                `gorm:"data_type" json:"data_type" db:"data_type"`
	Operator        db.NullString         `gorm:"operator" json:"operator" db:"operator"`
	Category        base.ApiFieldCategory `gorm:"category" json:"category" db:"category"`
	IsRequired      int32                 `gorm:"is_required" json:"is_required" db:"is_required"`
	Source          string                `gorm:"source" json:"source" db:"source"`
	DefaultValue    db.NullString         `gorm:"default_value" json:"default_value" db:"default_value"`
	ExampleValue    db.NullString         `gorm:"example_value" json:"example_value" db:"example_value"`
	ParamType       base.ApiParamType     `gorm:"param_type" json:"param_type" db:"param_type"`
	IndicatorCode   string                `gorm:"indicator_code" json:"indicator_code" db:"indicator_code"`
	VariableContent db.NullString         `gorm:"variable_content" json:"variable_content" db:"variable_content"`

	CreatedBy  db.NullString `gorm:"created_by" json:"created_by" db:"created_by"`
	CreatedOn  db.NullTime   `gorm:"created_on" json:"created_on" db:"created_on"`
	ModifiedBy db.NullString `gorm:"modified_by" json:"modified_by" db:"modified_by"`
	ModifiedOn db.NullTime   `gorm:"modified_on" json:"modified_on" db:"modified_on"`
}

func (s *ApiParamEntity) TableName() string {
	return "dap_m_api_param"
}

func (s *ApiParamEntity) ToBiz() *base.ApiParamBase {
	return &base.ApiParamBase{
		ID:              s.ID,
		ApiID:           s.ApiID,
		Name:            s.Name,
		Description:     s.Description.String,
		Operator:        s.Operator.String,
		DataType:        s.DataType,
		IsRequired:      s.IsRequired,
		DefaultValue:    s.DefaultValue.String,
		ExampleValue:    s.ExampleValue.String,
		ParamType:       s.ParamType,
		IndicatorCode:   s.IndicatorCode,
		Source:          s.Source,
		VariableContent: s.VariableContent.String,
	}
}

// GetTx
func (s *ApiManageStore) GetTx(ctx context.Context, project string) (tx *sql.Tx, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	tx, err = sess.Begin()
	return
}

func (s *ApiManageStore) AddApi(ctx context.Context, project string, req proto.ApiAddReq) (resp proto.ApiAddResp, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	tx, err := sess.Begin()
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	account := context_helper.MustGetSessionAccount(ctx)
	apiID := pkg.NewMysqlID()
	apiCode := pkg.NewMysqlID()

	// 插入api_main表
	insertApiMainSQL := "insert into dap_m_api_main(code,name,group_id,status,is_updated,created_by,modified_by) " +
		"values(?,?,?,?,?,?,?)"

	stmt, err := tx.Prepare(insertApiMainSQL)
	if err != nil {
		return
	}
	if _, err = stmt.ExecContext(ctx, apiCode, req.Name, req.GroupID, base.Dev, base.ChangeNo, account, account); err != nil {
		return
	}

	// 插入api表
	insertApiSQL := "insert into dap_m_api(id,code,stage,description,version,request_method,request_path,response_format," +
		"response_success_example,response_failed_example,create_mode,create_content,created_by," +
		"modified_by,resource_id) " +
		"values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

	defaultCreateContent, _ := json.Marshal(global.DefaultDialectSettingContent())

	stmt, err = tx.Prepare(insertApiSQL)
	if err != nil {
		return
	}
	if _, err = stmt.ExecContext(ctx, apiID, apiCode, base.StageDraft, req.Description, 0, req.RequestMethod,
		req.RequestPath, req.ResponseFormat, "", "", req.CreateMode, string(defaultCreateContent), account, account, req.ResourceID); err != nil {
		return
	}
	err = tx.Commit()
	resp.ApiID = apiID
	resp.ApiCode = apiCode
	return
}

func (s *ApiManageStore) UpdateApi(ctx context.Context, project string, req proto.ApiUpdateReq) (apiID string, err error) {
	//将结构体转化为string
	createContent, err := json.Marshal(req.CreateContent)
	if err != nil {
		return
	}

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	tx, err := sess.Begin()
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	account := context_helper.MustGetSessionAccount(ctx)
	// 更新api表信息
	updateApiSql := "update dap_m_api set request_method = ?,request_path = ?,response_format = ?,resource_id = ?," +
		"create_content = ?, enable_page =?, description=?, modified_by = ?  where id = ?"

	stmt, err := tx.Prepare(updateApiSql)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, req.RequestMethod, req.RequestPath, req.ResponseFormat, req.ResourceID,
		string(createContent), req.EnablePage, req.Description, account, req.ApiID)
	if err != nil {
		return
	}
	// 更新api_main表中is_updated字段
	//更新发布状态
	//有发布态 状态修改为  发布后更新 update

	//查询发布态api信息
	var existRelease int
	err = sess.GetContext(ctx, &existRelease, "select count(*) from dap_m_api where code = ? and stage = 1", req.ApiCode)
	if err != nil {
		return
	}
	if existRelease > 0 {
		updateApiMainSql := "update dap_m_api_main set is_updated = ?, modified_by = ?,release_status = ?,group_id = ?  where code = ?"
		stmt, err = tx.Prepare(updateApiMainSql)
		if err != nil {
			return
		}
		_, err = stmt.ExecContext(ctx, base.Change, account, "update", req.GroupID, req.ApiCode)
		if err != nil {
			return
		}
	} else {
		updateApiMainSql := "update dap_m_api_main set is_updated = ?, modified_by = ?,group_id = ?  where code = ?"

		stmt, err = tx.Prepare(updateApiMainSql)
		if err != nil {
			return
		}
		_, err = stmt.ExecContext(ctx, base.Change, account, req.GroupID, req.ApiCode)
		if err != nil {
			return
		}
	}

	// 清理api_param表
	deleteApiParamSql := "delete from dap_m_api_param where api_id=?"
	stmt, err = tx.Prepare(deleteApiParamSql)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, req.ApiID)
	if err != nil {
		return
	}

	//  更新api_param表
	insertApiParamSql := "insert into dap_m_api_param(id,api_id,name,description,data_type,operator,category,is_required," +
		"source,default_value,example_value,param_type,indicator_code,variable_content,created_by,modified_by) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	for _, reqParam := range req.RequestParams {
		stmt, err = tx.Prepare(insertApiParamSql)
		if err != nil {
			return
		}
		_, err = stmt.ExecContext(ctx, pkg.NewMysqlID(), req.ApiID, reqParam.Name, reqParam.Description,
			reqParam.DataType, reqParam.Operator, base.ApiFieldReq, reqParam.IsRequired, reqParam.Source,
			reqParam.DefaultValue, reqParam.ExampleValue, reqParam.ParamType, reqParam.IndicatorCode, reqParam.VariableContent, account, account)
		if err != nil {
			return
		}
	}
	for _, reqParam := range req.ResponseParams {
		stmt, err = tx.Prepare(insertApiParamSql)
		if err != nil {
			return
		}
		_, err = stmt.ExecContext(ctx, pkg.NewMysqlID(), req.ApiID, reqParam.Name, reqParam.Description,
			reqParam.DataType, "", base.ApiFieldResp, 0, base.SourceFieldUser,
			reqParam.DefaultValue, reqParam.ExampleValue, reqParam.ParamType, reqParam.IndicatorCode, "{}", account, account)
		if err != nil {
			return
		}
	}

	err = tx.Commit()
	return
}

// DeleteApi
func (s *ApiManageStore) DeleteApi(ctx context.Context, project string, tx *sql.Tx, req proto.ApiDeleteReq) (err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	var apiDetails []*ApiEntity
	sqlText := "select `id`, `code`, `stage`, `description`, `version`,`release_comment`,`request_method`, `request_path`, " +
		"`response_format`, `response_success_example`, `response_failed_example`, `resource_id`, `create_mode`, " +
		"`create_content`, `enable_page`, `created_on`, `created_by`, `modified_on`, `modified_by` from dap_m_api where `code` = ? "
	if err = sess.SelectContext(ctx, &apiDetails, sqlText, req.ApiCode); err != nil && err != sql.ErrNoRows {
		return
	}
	var apiIDs []string
	for _, item := range apiDetails {
		apiIDs = append(apiIDs, item.ID)
	}

	// 删除api_main
	deleteSQL := "delete from dap_m_api_main where code = ? "
	stmt, err := tx.Prepare(deleteSQL)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, req.ApiCode)
	if err != nil {
		return
	}

	// 删除 api
	deleteSQL = "delete from dap_m_api where code = ? "
	stmt, err = tx.Prepare(deleteSQL)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, req.ApiCode)
	if err != nil {
		return
	}

	// 删除api_param
	if len(apiIDs) == 0 {
		return
	}
	args := map[string]interface{}{
		"apiIDs": apiIDs,
	}
	deleteSQL = "delete from dap_m_api_param where api_id in (:apiIDs) "
	query, queryArgs, err := sqlx.Named(deleteSQL, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	stmt, err = tx.Prepare(query)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, queryArgs...)
	if err != nil {
		return
	}
	return
}

func getQueryWhereStr(req proto.ApiListReq) (where string, args map[string]interface{}) {
	args = map[string]interface{}{
		"skip": req.GetSkip(),
		"size": req.PageSize,
	}
	var whereSQLs []string
	if req.GroupID != "" {
		args["groupID"] = req.GroupID
		whereSQLs = append(whereSQLs, "am.group_id = :groupID")
	}
	if req.StartTime != "" {
		args["startTime"] = req.StartTime
		whereSQLs = append(whereSQLs, "a1.cur_publish_on >= :startTime")
	}
	if req.EndTime != "" {
		args["endTime"] = req.EndTime
		whereSQLs = append(whereSQLs, "a1.cur_publish_on <= :endTime")
	}
	if req.Status != "" {
		args["status"] = req.Status
		whereSQLs = append(whereSQLs, "am.status = :status")
	}
	if req.Keyword != "" {
		kw := req.GetEscapeKeyword()
		args["keyword"] = kw
		whereSQLs = append(whereSQLs, "(am.name like :keyword or a1.description like :keyword or a1.request_path like :keyword or a2.request_path like :keyword)")
	}
	if req.ExcludeAppBind != "" {
		args["exclude_app_id"] = req.ExcludeAppBind
		whereSQLs = append(whereSQLs, "(am.code not in (select api_code from dap_m_application_api where app_id = :exclude_app_id))")
	}
	if req.CodeGTE != "" {
		args["code"] = req.CodeGTE
		whereSQLs = append(whereSQLs, "(am.code >= :code)")
	}

	if req.Name != "" {
		args["name"] = "%" + strings.Replace(req.Name, "%", "\\%", -1) + "%"
		whereSQLs = append(whereSQLs, "(am.name like :name)")
	}

	if len(req.BatchApiList) > 0 {
		args["codes"] = req.BatchApiList
		whereSQLs = append(whereSQLs, "(am.code in (:codes))")
	}

	if req.SubjectId != "" {
		args["subject_id"] = req.SubjectId
		whereSQLs = append(whereSQLs, "(ag.subject_id = :subject_id)")
	}

	if req.SubjectId == "" && len(req.BatchSubjectList) > 0 {
		args["subjects"] = req.BatchSubjectList
		whereSQLs = append(whereSQLs, "(ag.subject_id in (:subjects))")
	}

	if len(whereSQLs) == 0 {
		return
	}
	where = "where " + strings.Join(whereSQLs, " and ")
	return
}

func (s *ApiManageStore) GetApiCountBySubjectId(ctx context.Context, project string) (subjectMap map[string]int, err error) {
	subjectMap = make(map[string]int)
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	sqlText := `select t2.subject_id, count(*) as api_count
from dap_m_api_main t1
inner join dap_m_api_group t2 on t1.group_id = t2.id
group by t2.subject_id`

	type subjectApiCount struct {
		SubjectId string `db:"subject_id"`
		ApiCount int `db:"api_count"`
	}
	var rv []*subjectApiCount
	if err = sess.SelectContext(ctx, &rv, sqlText); err != nil {
		return
	}
	if len(rv) > 0 {
		subjectMap = lo.SliceToMap(rv, func(item *subjectApiCount) (string, int) {
			return item.SubjectId, item.ApiCount
		})
	}
	return
}

// GetApiList 获取api列表
func (s *ApiManageStore) GetApiList(ctx context.Context, project string, req proto.ApiListReq) (apiList []*proto.ApiListDbItem, total int, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	apiList = []*proto.ApiListDbItem{}
	sqlText := "select am.code,am.name," +
		"ifnull(a1.description, ifnull(a2.description, \"\")) as description, " +
		"ifnull(a1.request_path, ifnull(a2.request_path, \"\")) as request_path, " +
		"am.group_id,am.rate_limit,am.timeout_limit, am.ref_table_set, " +
		"ag.name as group_name, ag.subject_id as subject_id, am.status,am.is_updated,am.created_by,am.created_on,am.modified_by,am.modified_on,a1.cur_publish_on," +
		"ifnull(a1.create_mode, ifnull(a2.create_mode, \"\")) as create_mode " +
		"from dap_m_api_main am " +
		"left join dap_m_api_group ag on am.group_id = ag.id " +
		"left join (select code, request_path, created_on as cur_publish_on,description,create_mode from dap_m_api where stage = 1) a1 on am.code = a1.code " +
		"left join (select code, max(description) as description, max(request_path) as request_path,max(create_mode) as create_mode from (select code,description,request_path,create_mode from dap_m_api where stage = 2 or stage = 0 " +
		"order by version desc) as tmp group by tmp.code ) a2 " +
		"on am.code = a2.code "

	whereStr, args := getQueryWhereStr(req)

	var orderBy string
	// 默认created_on
	if req.Sorts != "" {
		orderBy = fmt.Sprintf(" order by am.%s ", req.Sorts)
	} else {
		orderBy = " order by am.created_on DESC"
	}
	limitSQL := " limit :skip, :size "
	sqlText += whereStr + orderBy + limitSQL

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.SelectContext(ctx, &apiList, query, queryArgs...); err != nil {
		return
	}

	// 查询条目数
	sqlTextCount := "select count(am.code) " +
		"from dap_m_api_main am " +
		"left join dap_m_api_group ag on am.group_id = ag.id " +
		"left join (select code, request_path , created_on as cur_publish_on,description,create_mode from dap_m_api where stage = 1) a1 on am.code = a1.code " +
		"left join (select code,request_path from (select code,description,request_path from dap_m_api where stage = 2 or stage = 0 " +
		"order by version desc) as tmp group by tmp.code,tmp.request_path ) a2 " +
		"on am.code = a2.code "
	countWhereStr, args := getQueryWhereStr(req)
	sqlTextCount += countWhereStr
	query, queryArgs, err = sqlx.Named(sqlTextCount, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.GetContext(ctx, &total, query, queryArgs...); err != nil {
		return
	}
	return
}

func (s *ApiManageStore) getNextVersion(ctx context.Context, project, code string) (version int32, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	err = sess.GetContext(ctx,
		&version, "select max(version)+1 from dap_m_api where code=? and stage in (?, ?)", code, base.StageHistory, base.StageRelease)
	if err != nil {
		if strings.Contains(err.Error(), "converting NULL to int") {
			return 1, nil
		}
	}
	return
}

func (s *ApiManageStore) UpdateApiStageAndStatus(ctx context.Context, project string, detail *base.ApiDetail) (err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	tx, err := sess.Begin()
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	account := context_helper.MustGetSessionAccount(ctx)
	apiSql := "update dap_m_api set stage = ?, modified_by = ? where id = ?"

	stmt, err := tx.Prepare(apiSql)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, base.StageHistory, account, detail.ID)
	if err != nil {
		return
	}

	// 需要api_main表
	apiMainSql := "update dap_m_api_main set status = ?, modified_by = ? ,release_status = 'offline' where code = ?"
	stmt, err = tx.Prepare(apiMainSql)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, base.Dev, account, detail.Code)
	if err != nil {
		return
	}
	err = tx.Commit()
	return
}

func (s *ApiManageStore) ReleaseApi(ctx context.Context, project string, req proto.ApiUpdateReq) (apiID string, err error) {
	//将结构体转化为string
	createContent, err := json.Marshal(req.CreateContent)
	if err != nil {
		return
	}

	apiID = pkg.NewMysqlID()

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	tx, err := sess.Begin()
	if err != nil {
		return
	}
	defer func() {
		if err != nil {
			errRollback := tx.Rollback()
			if errRollback != nil {
				log.Println(errRollback)
			}
		}
	}()

	account := context_helper.MustGetSessionAccount(ctx)

	// 获取版本信息
	version, err := s.getNextVersion(ctx, project, req.ApiCode)
	if err != nil {
		return
	}

	updateApiSql := "update dap_m_api set stage = ?, modified_by = ? where code = ? and stage = ?"

	// 修改老的正式版成历史版本
	stmt, err := tx.Prepare(updateApiSql)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, base.StageHistory, account, req.ApiCode, base.StageRelease)
	if err != nil {
		return
	}

	// 插入api表
	insertApiSQL := "insert into dap_m_api(id,code,stage,description,version,request_method,request_path,response_format," +
		"response_success_example,response_failed_example,resource_id,create_mode,create_content,enable_page,created_by," +
		"modified_by) " +
		"values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

	// 生成正式版本
	stmt, err = tx.Prepare(insertApiSQL)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, apiID, req.ApiCode, base.StageRelease, req.Description, version, req.RequestMethod,
		req.RequestPath, req.ResponseFormat, req.ResponseSuccessExample, req.ResponseFailedExample,
		req.ResourceID, req.CreateMode, string(createContent), req.EnablePage, account, account)
	if err != nil {
		return
	}

	// 更新api_main表中is_updated字段
	updateApiMainSql := "update dap_m_api_main set is_updated = ?, status = ?, modified_by = ?,release_status = ?, ref_table_set =  ?  where code = ?"

	stmt, err = tx.Prepare(updateApiMainSql)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, base.ChangeNo, base.Published, account, req.ReleaseStatus, req.RefTableSet, req.ApiCode)
	if err != nil {
		return
	}

	// 清理api_param表
	deleteApiParamSql := "delete from dap_m_api_param where api_id=?"
	stmt, err = tx.Prepare(deleteApiParamSql)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, req.ApiID)
	if err != nil {
		return
	}

	//  更新api_param表
	insertApiParamSql := "insert into dap_m_api_param(id,api_id,name,description,data_type,operator,category,is_required," +
		"source,default_value,example_value,param_type,indicator_code,variable_content,created_by,modified_by) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	for _, reqParam := range req.RequestParams {
		stmt, err = tx.Prepare(insertApiParamSql)
		if err != nil {
			return
		}
		_, err = stmt.ExecContext(ctx, pkg.NewMysqlID(), apiID, reqParam.Name, reqParam.Description,
			reqParam.DataType, reqParam.Operator, base.ApiFieldReq, reqParam.IsRequired, reqParam.Source,
			reqParam.DefaultValue, reqParam.ExampleValue, reqParam.ParamType, reqParam.IndicatorCode, reqParam.VariableContent, account, account)
		if err != nil {
			return
		}
	}
	for _, reqParam := range req.ResponseParams {
		stmt, err = tx.Prepare(insertApiParamSql)
		if err != nil {
			return
		}
		_, err = stmt.ExecContext(ctx, pkg.NewMysqlID(), apiID, reqParam.Name, reqParam.Description,
			reqParam.DataType, "", base.ApiFieldResp, 0, reqParam.Source,
			reqParam.DefaultValue, reqParam.ExampleValue, reqParam.ParamType, reqParam.IndicatorCode, "{}", account, account)
		if err != nil {
			return
		}
	}

	err = tx.Commit()
	return
}

// GetApiDetailByCode 获取api详情(通过code)
func (s *ApiManageStore) GetApiDetailByCode(ctx context.Context, project, code string, stage base.ApiStage) (detail *base.ApiDetail, err error) {
	apiID, err := s.GetApiIDByCode(ctx, project, code, stage)
	if err != nil {
		return
	}
	return s.GetApiDetail(ctx, project, apiID)
}

// BatchGetApiDetailByCodes 批量获取api详情
func (s *ApiManageStore) BatchGetApiDetailByCodes(ctx context.Context, project string, codes []string, stage base.ApiStage) (details []*base.ApiDetail, err error) {
	apiIDs, err := s.GetApiIDByCodes(ctx, project, codes, stage)
	if err != nil {
		return
	}
	return s.BatchGetApiDetail(ctx, project, apiIDs)
}

// GormGetApiDetailByPath 通过request_path获取发布的api的详情
func (s *ApiManageStore) GormGetApiDetailByPath(ctx context.Context, project, requestPath string) (detail *base.ApiDetail, err error) {
	apiID, err := s.GormGetApiIDByPath(ctx, project, requestPath)
	if err != nil {
		return
	}
	return s.GormGetApiDetail(ctx, project, apiID)
}

func (s *ApiManageStore) GetApiMainDetail(ctx context.Context, project, apiCode string) (result *base.ApiMain, err error) {
	var obj ApiMainEntity

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	err = sess.GetContext(ctx,
		&obj, "select `code`, `name`, `group_id`, `status`, `is_updated`, `created_by`, "+
			"`modified_by`,`rate_limit`,`timeout_limit` from dap_m_api_main where `code`=? limit 1", apiCode)
	if err != nil {
		return
	}
	return obj.ToBiz(), nil
}

func (s *ApiManageStore) GormGetApiMainDetail(ctx context.Context, project string, apiCode string) (detail *base.ApiMain, err error) {
	var obj ApiMainEntity
	sess, err := saas_db.GetProjectDB(ctx, project)
	if err != nil {
		return
	}
	if err = sess.Where("`code`=?", apiCode).Take(&obj).Error; err != nil {
		err = errors.WrapGorm(err, "api_main[code=%s]", apiCode)
		return
	}
	return obj.ToBiz(), nil
}

func (s *ApiManageStore) BatchGetApiMainDetail(ctx context.Context, project string, req proto.BatchGetApiMainReq) (result []*base.ApiMain, err error) {
	var objs []ApiMainEntity

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	sqlText := "select `code`, `name`, `group_id`, `status`, `is_updated`, `created_by`, " +
		"`modified_by`,`rate_limit`,`timeout_limit` from dap_m_api_main "
	args := map[string]interface{}{
		"codes": req.ApiCodes,
	}

	var whereSQLs []string
	if len(req.ApiCodes) > 0 {
		args["codes"] = req.ApiCodes
		whereSQLs = append(whereSQLs, "`code` in (:codes)")
	}
	if req.Status != "" {
		args["status"] = req.Status
		whereSQLs = append(whereSQLs, "`status`=:status")
	}

	if len(whereSQLs) > 0 {
		sqlText += "where " + strings.Join(whereSQLs, " and ")
	}

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}

	err = sess.SelectContext(ctx, &objs, query, queryArgs...)
	if err != nil {
		return
	}

	result = make([]*base.ApiMain, 0)
	for _, obj := range objs {
		result = append(result, obj.ToBiz())
	}
	return
}

// GetApiMainProdDetail
func (s *ApiManageStore) GetApiMainProdDetail(ctx context.Context, project, apiCode string) (*proto.ApiReleaseDetailMain, error) {
	var obj proto.ApiReleaseDetailMain
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	err = sess.GetContext(ctx,
		&obj, "select am.code,am.name,am.status,am.is_updated,am.group_id,am.rate_limit,am.timeout_limit,"+
			"ag.name as group_name "+
			"from dap_m_api_main am left join dap_m_api_group ag on am.group_id=ag.id where am.code = ? limit 1 ", apiCode)
	if err != nil {
		return nil, err
	}
	return &obj, nil
}

// BatchGetApiDetail 批量获取api详情
func (s *ApiManageStore) BatchGetApiDetail(ctx context.Context, project string, apiIDs []string) (details []*base.ApiDetail, err error) {
	var objs []ApiEntity

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	if len(apiIDs) == 0 {
		details = make([]*base.ApiDetail, 0)
		return
	}

	sqlText := "select `id`, `code`, `stage`, `description`, `version`,`release_comment`,`request_method`, `request_path`, " +
		"`response_format`, `response_success_example`, `response_failed_example`, `resource_id`, `create_mode`, " +
		"`create_content`, `enable_page`, `created_on`, `created_by`, `modified_on`, `modified_by` from dap_m_api where `id` in (:ids)"
	args := map[string]interface{}{
		"ids": apiIDs,
	}

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	err = sess.SelectContext(ctx, &objs, query, queryArgs...)
	if err == sql.ErrNoRows {
		err = apiErrors.NewAppIDNotExistErr(apiIDs[0])
		return
	}
	if err != nil {
		return
	}

	for _, apiID := range apiIDs {
		exist := false
		for _, obj := range objs {
			if strings.ToLower(obj.ID) == strings.ToLower(apiID) {
				exist = true
				break
			}
		}
		if !exist {
			err = apiErrors.NewAppIDNotExistErr(apiID)
			return
		}
	}

	detailsMap := make(map[string]*base.ApiDetail)
	for _, obj := range objs {
		detailsMap[obj.ID], err = obj.ToBiz()
		if err != nil {
			return
		}
	}

	// 查询param信息
	var apiParams []*ApiParamEntity

	sqlText = "select `id`, `api_id`, `name`, `data_type`,`operator`,`category`, `is_required`, " +
		"`source`, `default_value`, `example_value`, `description`, `param_type`, `indicator_code`,`variable_content`, " +
		"`created_on`, `created_by`, `modified_on`, `modified_by` from dap_m_api_param where `api_id` in (:ids)"
	args = map[string]interface{}{
		"ids": apiIDs,
	}

	query, queryArgs, err = sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	err = sess.SelectContext(ctx, &apiParams, query, queryArgs...)
	if err != nil {
		return
	}

	for _, apiParam := range apiParams {
		detail, ok := detailsMap[apiParam.ApiID]
		if !ok {
			continue
		}
		if apiParam.Category == base.ApiFieldReq {
			detail.RequestParams = append(detail.RequestParams, apiParam.ToBiz())
		} else {
			detail.ResponseParams = append(detail.ResponseParams, apiParam.ToBiz())
		}
	}

	details = make([]*base.ApiDetail, 0)
	for _, detail := range detailsMap {
		details = append(details, detail)
	}

	return
}

// GetApiDetail 获取api详情
func (s *ApiManageStore) GetApiDetail(ctx context.Context, project, apiID string) (detail *base.ApiDetail, err error) {
	var obj ApiEntity

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	err = sess.GetContext(ctx,
		&obj, "select `id`, `code`, `stage`, `description`, `version`,`release_comment`,`request_method`, `request_path`, "+
			"`response_format`, `response_success_example`, `response_failed_example`, `resource_id`, `create_mode`, "+
			"`create_content`, `enable_page`, `created_on`, `created_by`, `modified_on`, `modified_by` from dap_m_api where `id`=? limit 1", apiID)
	if err == sql.ErrNoRows {
		err = apiErrors.NewAppIDNotExistErr(apiID)
		return
	}
	if err != nil {
		return
	}
	detail, err = obj.ToBiz()
	if err != nil {
		return
	}

	// 查询param信息
	var apiParams []*ApiParamEntity

	getApiParamsSql := "select `id`, `api_id`, `name`, `data_type`,`operator`,`category`, `is_required`, " +
		"`source`, `default_value`, `example_value`, `description`, `param_type`, `indicator_code`, `variable_content`," +
		"`created_on`, `created_by`, `modified_on`, `modified_by` from dap_m_api_param where `api_id`=?"

	if err := sess.SelectContext(ctx, &apiParams, getApiParamsSql, apiID); err != nil {
		return nil, err
	}

	for _, apiParam := range apiParams {
		if apiParam.Category == base.ApiFieldReq {
			detail.RequestParams = append(detail.RequestParams, apiParam.ToBiz())
		} else {
			detail.ResponseParams = append(detail.ResponseParams, apiParam.ToBiz())
		}
	}

	return
}

// GormGetApiDetail 获取api详情
func (s *ApiManageStore) GormGetApiDetail(ctx context.Context, project string, id string) (detail *base.ApiDetail, err error) {
	sess, err := saas_db.GetProjectDB(ctx, project)
	if err != nil {
		return
	}

	// 查询api信息
	var obj ApiEntity
	if err = sess.Where("id=?", id).Take(&obj).Error; err != nil {
		err = errors.WrapGorm(err, "api[id=%s]", id)
		return
	}
	detail, err = obj.ToBiz()
	if err != nil {
		return
	}

	// 查询param信息
	var apiParams []*ApiParamEntity
	if err = sess.Where("api_id", id).Find(&apiParams).Error; err != nil {
		err = errors.WrapGorm(err)
		return
	}
	for _, apiParam := range apiParams {
		if apiParam.Category == base.ApiFieldReq {
			detail.RequestParams = append(detail.RequestParams, apiParam.ToBiz())
		} else {
			detail.ResponseParams = append(detail.ResponseParams, apiParam.ToBiz())
		}
	}

	return
}

func (s *ApiManageStore) GetApiVersionList(ctx context.Context, project string, req proto.ApiVersionReq) (result proto.ApiVersionResp, err error) {
	req.Stage = base.StageDraft
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	var ApiVersion []*ApiEntity

	getVersionSql := "select `id`, `code`, `stage`, `version`,`release_comment`,`request_method`, `request_path`, " +
		"`response_format`, `response_success_example`, `response_failed_example`, `resource_id`, `create_mode`, " +
		"`create_content`, `created_on`, `created_by`, `modified_on`, `modified_by` from dap_m_api where `code`=? " +
		"and stage != ? order by version DESC limit ?,?"

	if err1 := sess.SelectContext(ctx, &ApiVersion, getVersionSql, req.ApiCode, req.Stage, req.GetSkip(), req.PageSize); err1 != nil {
		err = err1
		return
	}
	for _, api := range ApiVersion {
		temp, err1 := api.ToBiz()
		if err1 != nil {
			err = err1
			return
		}
		result.Items = append(result.Items, temp)
	}
	// 求总数
	sqlTextCount := "select count(id) from dap_m_api where `code` = :api_code and `stage` != :stage"

	count, err := sess.NamedQueryContext(ctx, sqlTextCount, req)
	if err != nil {
		return
	}
	defer func() {
		errCountClose := count.Close()
		if errCountClose != nil {
			log.Print(errCountClose)
		}
	}()
	for count.Next() {
		err = count.Scan(&result.Total)
		if err != nil {
			return
		}
	}
	return
}

// UpdateApiTestResult
func (s *ApiManageStore) UpdateApiTestResult(ctx context.Context, project string, req proto.ApiTestResultReq) (err error) {
	fieldsValues := make(map[string]interface{})
	if req.ResponseSuccessExample != "" {
		var strSucc bytes.Buffer
		_ = json.Indent(&strSucc, []byte(req.ResponseSuccessExample), "", "	")
		fieldsValues["response_success_example"] = strSucc.String()
	}
	if req.ResponseFailedExample != "" {
		var strFail bytes.Buffer
		_ = json.Indent(&strFail, []byte(req.ResponseFailedExample), "", "	")
		fieldsValues["response_failed_example"] = strFail.String()
	}
	err = s.Update(ctx, project, req.ApiID, "dap_m_api", fieldsValues)
	return
}

// GetApiIDByCode 通过code和stage获取api_id
func (s *ApiManageStore) GetApiIDByCode(ctx context.Context, project, code string, stage base.ApiStage) (apiID string, err error) {
	var obj ApiEntity

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	err = sess.GetContext(ctx,
		&obj, "select `id` from dap_m_api where `code`=? and `stage`=? limit 1", code, stage)
	if err != nil {
		return
	}
	return obj.ID, nil
}

func (s *ApiManageStore) GetApiIDByCodes(ctx context.Context, project string, codes []string, stage base.ApiStage) (apiIDs []string, err error) {
	if len(codes) == 0 {
		return make([]string, 0), nil
	}
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	sqlText := "select `id` from dap_m_api where `code` in (:codes) and `stage` = :stage"
	args := map[string]interface{}{
		"codes": codes,
		"stage": stage,
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.SelectContext(ctx, &apiIDs, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}
	if err != nil {
		return
	}

	return apiIDs, nil
}

// CheckPathUniqueness 检测路径是否唯一(返回true表示唯一)
func (s *ApiManageStore) CheckPathUniqueness(ctx context.Context, project, apiCode, requestPath string) (flag bool, err error) {
	var obj ApiEntity

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	err = sess.GetContext(ctx,
		&obj, "select `code` from dap_m_api where `code`!=? and `request_path`=? limit 1", apiCode, requestPath)
	if err == sql.ErrNoRows {
		return true, nil
	}
	return
}

// CheckNameUniqueness 检测名称和分组是否唯一
func (s *ApiManageStore) CheckNameUniqueness(ctx context.Context, project, name, groupID string) (flag bool, err error) {
	var obj ApiEntity

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	err = sess.GetContext(ctx,
		&obj, "select `code` from dap_m_api_main where `name`=? and `group_id`=? limit 1", name, groupID)
	if err == sql.ErrNoRows {
		return true, nil
	}
	return
}

// GetApiIDByCode 通过request_path获取发布的apiID
func (s *ApiManageStore) GetApiIDByPath(ctx context.Context, project, requestPath string) (apiID string, err error) {
	var obj ApiEntity

	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}
	err = sess.GetContext(ctx,
		&obj, "select `id` from dap_m_api where `request_path`=? and `stage`=1 limit 1", requestPath)
	if err != nil {
		return
	}
	return obj.ID, nil
}

// GormGetApiIDByCode 通过request_path获取发布的apiID
func (s *ApiManageStore) GormGetApiIDByPath(ctx context.Context, project, requestPath string) (apiID string, err error) {
	sess, err := saas_db.GetProjectDB(ctx, project)
	if err != nil {
		return
	}
	err = sess.Raw("select `id` from dap_m_api where `request_path`=? and `stage`=1 limit 1", requestPath).Take(&apiID).Error
	if err != nil {
		err = errors.WrapGorm(err, "api[request_path=%s]", requestPath)
		return
	}
	return
}

// GetApplicationAuthApiList 获取授权关联Api列表
func (s *ApiManageStore) GetApplicationAuthApiList(ctx context.Context, req proto.ApplicationAuthApiReq) (apiList []*proto.ApplicationAuthApiItemResp, total int, err error) {
	sess, err := s.GetProjDB(ctx, req.ProjectCode)
	if err != nil {
		return nil, 0, err
	}
	apiList = []*proto.ApplicationAuthApiItemResp{}
	sqlText := "select aa.app_id,aa.api_code,aa.auth_term,aa.start_time,aa.end_time," +
		"ifnull(a1.request_method, ifnull(a2.request_method, \"\")) as request_method," +
		"ifnull(a1.request_path, ifnull(a2.request_path, \"\")) as request_path, " +
		"am.name,IF(a1.description != '', a1.description, a2.description) as description,am.status," +
		"am.created_by,am.created_on,a1.cur_publish_on " +
		"from dap_m_application_api as aa " +
		"left join dap_m_api_main as am on aa.api_code=am.code " +
		"left join (select code, request_method, request_path, created_on as cur_publish_on,description from dap_m_api where stage = 1) a1 on am.code = a1.code " +
		"left join (select code, max(description) as description, max(request_method) as request_method, max(request_path) as request_path from (select code,description,request_method,request_path from dap_m_api where stage = 2 or stage = 0 " +
		"order by version desc) as tmp group by tmp.code ) a2 " +
		"on am.code = a2.code "

	args := map[string]interface{}{
		"skip": req.GetSkip(),
		"size": req.PageSize,
	}
	var whereSQLs []string
	if req.AppID != "" {
		args["appID"] = req.AppID
		whereSQLs = append(whereSQLs, "aa.app_id = :appID")
	}
	if req.Keyword != "" {
		kw := req.GetEscapeKeyword()
		args["keyword"] = kw
		whereSQLs = append(whereSQLs, "(am.name like :keyword or a1.description like :keyword)")
	}
	if len(whereSQLs) != 0 {
		where := "where " + strings.Join(whereSQLs, " and ")
		sqlText += where
	}

	var orderBy string
	// 默认created_on
	if req.Sorts != "" {
		orderBy = fmt.Sprintf(" order by am.%s ", req.Sorts)
	} else {
		orderBy = " order by aa.created_on DESC"
	}

	limitSQL := " limit :skip, :size "
	sqlText += orderBy + limitSQL

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.SelectContext(ctx, &apiList, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		fmt.Println("GetApplicationAuthApiList error,sql: ", sqlText)
		return
	}

	// 查询条目数
	sqlTextCount := "select count(app_id) from dap_m_application_api where app_id = :appID "
	count, err := sess.NamedQueryContext(ctx, sqlTextCount, args)
	if err != nil {
		return
	}
	defer func() {
		errCountClose := count.Close()
		if errCountClose != nil {
			log.Print(errCountClose)
		}
	}()
	for count.Next() {
		err = count.Scan(&total)
		if err != nil {
			return
		}
	}
	return
}

// ReplaceApplicationApiRelation 添加授权记录
func (s *ApiManageStore) ReplaceApplicationApiRelation(ctx context.Context, tx *sql.Tx, req base.ApplicationApi) (err error) {
	account := context_helper.MustGetSessionAccount(ctx)

	var stmt *sql.Stmt
	if strings.ToLower(global.AppConfig.DB.DBType) == "dm" {
		deleteSQL := db_helper.NativeTag("delete from dap_m_application_api where app_id=? and api_code=?")
		stmt, err = tx.Prepare(deleteSQL)
		if err != nil {
			return
		}
		if _, err = stmt.ExecContext(ctx, req.AppID, req.ApiCode); err != nil {
			return
		}
		insertSQL := db_helper.NativeTag("insert into /*+IGNORE_ROW_ON_DUPKEY_INDEX(\"dap_m_application_api\")*/ dap_m_application_api (app_id,api_code,auth_term,start_time,end_time,created_by,modified_by) values(?,?,?,?,?,?,?)")
		stmt, err = tx.Prepare(insertSQL)
		if err != nil {
			return
		}
		if _, err = stmt.ExecContext(ctx, req.AppID, req.ApiCode, req.AuthTerm, req.StartTime, req.EndTime, account, account); err != nil {
			return
		}
	} else {
		replaceSQL := db_helper.NativeTag("replace dap_m_application_api (app_id,api_code,auth_term,start_time,end_time,created_by,modified_by) values(?,?,?,?,?,?,?)")
		stmt, err = tx.Prepare(replaceSQL)
		if err != nil {
			return
		}
		if _, err = stmt.ExecContext(ctx, req.AppID, req.ApiCode, req.AuthTerm, req.StartTime, req.EndTime, account, account); err != nil {
			return
		}
	}

	return
}

// BatchDeleteApplicationApiRelation 批量删除授权记录
func (s *ApiManageStore) BatchDeleteApplicationApiRelation(ctx context.Context, tx *sql.Tx, apiCode string, appIDs []string) (err error) {
	sqlText := "delete from dap_m_application_api "
	var whereSQLs []string
	args := make(map[string]interface{})
	if apiCode != "" {
		args["apiCode"] = apiCode
		whereSQLs = append(whereSQLs, " api_code = :apiCode ")
	}
	if len(appIDs) != 0 {
		args["appIDs"] = appIDs
		whereSQLs = append(whereSQLs, " app_id in (:appIDs) ")
	}
	if len(whereSQLs) != 0 {
		whereStr := "where " + strings.Join(whereSQLs, " and ")
		sqlText += whereStr
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	stmt, err := tx.Prepare(query)
	if err != nil {
		return
	}
	_, err = stmt.ExecContext(ctx, queryArgs...)
	if err != nil {
		return
	}
	return
}

// ApplicationApiRelationList
func (s *ApiManageStore) ApplicationApiRelationList(ctx context.Context, project, apiCode string) (relationList []*base.ApplicationApi, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	relationList = []*base.ApplicationApi{}
	sqlText := "select app_id,ifnull(t2.api_key,'') as api_key,api_code,auth_term,start_time," +
		"end_time,t1.created_by,t1.created_on,t1.modified_by,t1.modified_on " +
		"from dap_m_application_api t1 left join dap_m_application t2 on t1.app_id=t2.id "

	var whereSQLs []string
	args := make(map[string]interface{})
	if apiCode != "" {
		args["apiCode"] = apiCode
		whereSQLs = append(whereSQLs, " api_code = :apiCode ")
	}
	if len(whereSQLs) != 0 {
		whereStr := "where " + strings.Join(whereSQLs, " and ")
		sqlText += whereStr
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.SelectContext(ctx, &relationList, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}
	return
}

func (s *ApiManageStore) GormApplicationApiRelationList(ctx context.Context, project string, apiCode string) (relationList []*base.ApplicationApi, err error) {
	sess, err := saas_db.GetProjectDB(ctx, project)
	if err != nil {
		return nil, err
	}
	relationList = []*base.ApplicationApi{}
	sqlText := `select app_id, ifnull(t2.api_key,'') as api_key,api_code,auth_term,start_time,
end_time,t1.created_by,t1.created_on,t1.modified_by,t1.modified_on 
from dap_m_application_api t1 left join dap_m_application t2 on t1.app_id=t2.id
where api_code=?`
	if err = sess.Raw(sqlText, apiCode).Find(&relationList).Error; err != nil {
		err = errors.WrapGorm(err)
		return
	}
	return
}

// ApplicationApiRelationList
func (s *ApiManageStore) ApplicationApiRelationListWithRank(ctx context.Context, project, apiCode string, apiCodes []string) (relationList []*proto.ApplicationApiItemResp, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return nil, err
	}
	relationList = []*proto.ApplicationApiItemResp{}
	sqlText := "select 0 as `rank`, aa.app_id,aa.api_code,aa.auth_term,aa.start_time,aa.end_time," +
		"aa.created_by,aa.created_on,aa.modified_by,aa.modified_on " +
		"from dap_m_application_api aa "

	var whereSQLs []string
	args := make(map[string]interface{})
	if apiCode != "" {
		args["apiCode"] = apiCode
		whereSQLs = append(whereSQLs, " aa.api_code = :apiCode ")
	}
	if len(apiCodes) != 0 {
		args["apiCodes"] = apiCodes
		whereSQLs = append(whereSQLs, " aa.api_code in (:apiCodes) ")
	}
	if len(whereSQLs) != 0 {
		whereStr := "where " + strings.Join(whereSQLs, " and ")
		sqlText += whereStr
	}
	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}
	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}
	if err = sess.SelectContext(ctx, &relationList, query, queryArgs...); err != nil && err != sql.ErrNoRows {
		return
	}
	relationList = lo.Map(relationList, func(item *proto.ApplicationApiItemResp, index int) *proto.ApplicationApiItemResp {
		item.Rank = index
		return item
	})
	return
}

// ApiUpsetLimit 配置接口限流
func (s *ApiManageStore) ApiUpsetLimit(ctx context.Context, tx *sql.Tx, req proto.ApiUpSetLimitReq) (err error) {
	fieldsValues := make(map[string]interface{})
	fieldsValues["rate_limit"] = req.RateLimit
	fieldsValues["timeout_limit"] = req.TimeoutLimit
	updateApiMainSQL := "update dap_m_api_main set rate_limit = ?, timeout_limit = ? where code = ? "
	stmt, err := tx.Prepare(updateApiMainSQL)
	if err != nil {
		return
	}
	if _, err = stmt.ExecContext(ctx, req.RateLimit, req.TimeoutLimit, req.ApiCode); err != nil {
		return
	}
	return
}

func (s *ApiManageStore) GetAuthAppList(ctx context.Context, project, code string) (apps []base.MiniAuthApp, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	sqlText := "select id as app_id,`name` from dap_m_application where id in (select app_id from dap_m_application_api where api_code = :code )"
	args := map[string]interface{}{
		"code": code,
	}

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}

	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}

	err = sess.SelectContext(ctx, &apps, query, queryArgs...)
	return
}

func (s *ApiManageStore) GetAllApiCodeByProject(ctx context.Context, project string, status base.ApiStatus) (apiCodes []string, err error) {
	sess, err := s.GetProjDB(ctx, project)
	if err != nil {
		return
	}

	sqlText := "select code from dap_m_api_main where status = :status"
	args := map[string]interface{}{
		"status": status,
	}

	query, queryArgs, err := sqlx.Named(sqlText, args)
	if err != nil {
		return
	}

	query, queryArgs, err = sqlx.In(query, queryArgs...)
	if err != nil {
		return
	}

	err = sess.SelectContext(ctx, &apiCodes, query, queryArgs...)
	return
}
