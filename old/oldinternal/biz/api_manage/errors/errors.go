package errors

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/apierr"
)

func NewAppIDNotExistErr(apiID string) error {
	return apierr.NewCommonUserErrf("当前api不存在(id:%s)", apiID)
}

func NewApICodeNotExistErr(stageStr, apiCode string) error {
	return apierr.NewCommonUserErrf("状态为(stage:%s)的api不存在(code:%s)", stageStr, apiCode)
}

func NewAppIDOrCodeRequiredErr() error {
	return apierr.NewCommonUserErrf("api_id和api_code、stage必须填写一个。")
}

func NewAppCodePathRepeatErr() error {
	return apierr.NewCommonUserErrf("路径重复，请修改。")
}

func NewAppCodeNameRepeatErr() error {
	return apierr.NewCommonUserErrf("当前组下名称重复，请修改。")
}

func NewUpdateNotDraftErr() error {
	return apierr.NewCommonUserErrf("只允许修改编辑态API。")
}

func NewNotReleaseErr(code string) error {
	return apierr.NewCommonUserErrf("api尚未发布(code:%s)", code)
}

func NewReleasedErr(code string) error {
	return apierr.NewCommonUserErrf("api已发布，无法删除(code:%s)", code)
}

func NewValidateParamDataErr(data interface{}, dataType, value string) error {
	return apierr.NewCommonUserErrf("参数校验错误:[%s]非法取值,需要[%s],但传入[%s]", data, dataType, value)
}

func NewUpdateReqNumErr() error {
	return apierr.NewCommonUserErrf("当无请求参数时，必须开启返回结果分页")
}

func NewUpdateRespNumErr() error {
	return apierr.NewCommonUserErrf("必须填写一个以上的返回参数。")
}

func NewRollbackErr() error {
	return apierr.NewCommonUserErrf("仅支持回滚历史版本。")
}

func NewReleaseReqParamsNumErr() error {
	return apierr.NewCommonUserErrf("请求参数，个数必须完全一致。（不兼容修改需要将接口先下线再发布）")
}

func NewReleaseReqLackParamsErr(name string) error {
	return apierr.NewCommonUserErrf("请求参数，缺少%s参数", name)
}

func NewReleaseReqParamTypeErr(name, oldType, newType string) error {
	return apierr.NewCommonUserErrf("请求参数，%s参数类型不一致。线上类型为：%s，目前类型为：%s。（不兼容修改需要将接口先下线再发布）", name, oldType, newType)
}

func NewReleaseRespParamsNumErr() error {
	return apierr.NewCommonUserErrf("返回参数，个数不能少于原接口个数。（不兼容修改需要将接口先下线再发布）")
}

func NewReleaseRespLackParamsErr(name string) error {
	return apierr.NewCommonUserErrf("返回参数，缺少%s参数。（不兼容修改需要将接口先下线再发布）", name)
}

func NewReleaseRespParamTypeErr(name, oldType, newType string) error {
	return apierr.NewCommonUserErrf("返回参数，%s参数类型不一致。线上类型为：%s，目前类型为：%s。（不兼容修改需要将接口先下线再发布）", name, oldType, newType)
}

func NewUpdateReqVariableErr(name string) error {
	return apierr.NewCommonUserErrf("变量参数:%s内容为空", name)
}
