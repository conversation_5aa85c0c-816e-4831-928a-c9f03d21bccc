package cache

import (
	"context"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/json_utils"
	"time"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewApiCache))
}

type ApiCache struct {
	redisCli *cache.Redis
}

func NewApiCache(redisCli *cache.Redis) *ApiCache {
	return &ApiCache{redisCli: redisCli}
}

func getMetaCacheKey(project, requestPath string) string {
	return fmt.Sprintf(base.ApiMetaRedisCacheKey, project, requestPath)
}

func (s *ApiCache) SetApiDataCache(ctx context.Context, project, requestPath string, data interface{}) {
	key := getMetaCacheKey(project, requestPath)
	b, err := json_utils.Marshal(data)
	if err != nil {
		return
	}
	s.redisCli.Client.Set(ctx, key, string(b), time.Hour*24*7)
}

func (s *ApiCache) GetApiDataByPathCache(ctx context.Context, project, requestPath string) (b []byte, err error) {
	b, err = s.redisCli.Client.Get(ctx, getMetaCacheKey(project, requestPath)).Bytes()
	if err != nil {
		err = errors.WrapRedis(err)
		return
	}
	return
}

func (s *ApiCache) DelMetaCache(project, requestPath string) {
	s.redisCli.Client.Del(context.Background(), getMetaCacheKey(project, requestPath))
}
