package services

import (
	"context"
	dap_stream "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-stream"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/iface"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/model_agg/proto"
	indicatorViewService "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/indicator_view/services"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model"
	multiDimModelService "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/services"
	projectService "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/project/services"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewModelAggService, inject.As(new(ModelAggService))))
}

type ModelAggService interface {
	// query
	ListAllModelBriefs(ctx context.Context, project string, req proto.ListModelBriefReq) (rsp proto.ListAllModelBriefsRsp, err error)
	QueryModelBriefTree(ctx context.Context, project string, req proto.QueryModelBriefTreeReq) (rsp proto.QueryModelBriefTreeRsp, err error)
	ListAllModels(ctx context.Context, project string, req proto.ListAllModelsReq) (rsp proto.ListAllModelsRsp, err error)
	GetModelRelationMap(ctx context.Context, project string, req proto.GetModelRelationMapReq) (rsp *proto.GetModelRelationMapRsp, err error)
}

var _ ModelAggService = (*ModelAggInternalService)(nil)

type ModelAggInternalService struct {
	indicatorViewService    indicatorViewService.IndicatorService
	multiDimModelService    multiDimModelService.MultiDimService
	modelService            *model.ModelService
	projectService          *projectService.ProjectService
	newMultiDimModelService iface.MultiDimService
	newPhysicalModelService iface.PhysicalModelService
	newSubjectService       iface.SubjectService
	streamService           dap_stream.DapStreamService
}

func NewModelAggService(
	indicatorViewService *indicatorViewService.IndicatorViewService,
	multiDimModelService multiDimModelService.MultiDimService,
	modelService *model.ModelService,
	projectService *projectService.ProjectService,
	newMultiDimModelService iface.MultiDimService,
	newPhysicalModelService iface.PhysicalModelService,
	newSubjectService iface.SubjectService,
) *ModelAggInternalService {
	return &ModelAggInternalService{
		indicatorViewService:    indicatorViewService,
		multiDimModelService:    multiDimModelService,
		modelService:            modelService,
		projectService:          projectService,
		newMultiDimModelService: newMultiDimModelService,
		newPhysicalModelService: newPhysicalModelService,
		newSubjectService:       newSubjectService,
		streamService:           dap_stream.NewDapStreamServiceS(),
	}
}
