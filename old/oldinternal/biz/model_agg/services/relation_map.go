package services

import (
	"context"
	"github.com/samber/lo"
	baseProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/model_agg/converter"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/model_agg/proto"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	multiDimModelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

func (s *ModelAggInternalService) GetModelRelationMap(ctx context.Context, project string, req proto.GetModelRelationMapReq) (rsp *proto.GetModelRelationMapRsp, err error) {
	switch req.Category {
	case modelBase.DimModelCate, modelBase.DwdModelCate, modelBase.DwsModelCate:
		return s.getPhysicalModelRelationMap(ctx, project, req.Code)
	case modelBase.MultiDimCate:
		return s.getMultiDimRelationMap(ctx, project, req.Code)
	case modelBase.AdsViewModelCate:
		return s.getAdsViewRelationMap(ctx, project, req.Code)
	}
	return
}

func (s *ModelAggInternalService) getPhysicalModelRelationMap(ctx context.Context, project string, code string) (*proto.GetModelRelationMapRsp, error) {
	rsp := &proto.GetModelRelationMapRsp{
		BaseCode:  code,
		Relations: []*modelProto.ModelRefRelation{},
		Nodes:     []*proto.ModelNode{},
	}

	// 1.1 获取物理表下游多维
	relations, err := s.multiDimModelService.GetMultiDimRelationsByUpstreamPhysicalModelCode(ctx, project, code)
	if err != nil {
		return nil, err
	}

	rsp.Relations = append(rsp.Relations, relations...)
	codes := []string{}
	for _, r := range relations {
		codes = append(codes, r.ToCode)
	}
	codes = lo.Uniq(codes)

	// 1.2 获取多维briefs
	var multiDimBriefs []*multiDimModelProto.MultiDimModelBrief
	if len(codes) > 0 {
		multiDimBriefs, err = s.multiDimModelService.GetMultiDimModelBriefList(ctx, project, multiDimModelProto.GetMultiDimModelListRequest{
			ModelCodes:       codes,
			Environment:      entities.ProjectEnvDev,
			QueryBaseRequest: cBase.QueryBaseNoPageRequest(),
		})
		if err != nil {
			return nil, err
		}
		for _, brief := range multiDimBriefs {
			rsp.Nodes = append(rsp.Nodes, (*proto.ModelNode)(converter.ConvertMultiDimModelToModelBrief(brief)))
		}
	}

	// 2.1 获取多维下游应用表关系
	// 2.2 获取物理表下游应用表关系
	// 2.3 保存应用表关系
	// 2.4 批量获取物理模型+应用表briefs
	// 2.4 批量获取物理模型+应用表briefs
	var adsTableBriefs []*modelProto.PhysicalModelBrief
	adsTableBriefs, err = s.modelService.ListPhysicalModelBriefs(ctx, project, modelProto.ListModelDefinitionReq{
		PhysicalModelCodes: []string{code},
		Environment:        cBase.ProjectEnvDev,
		QueryBaseRequest:   cBase.QueryBaseNoPageRequest(),
	})
	if err != nil {
		return nil, err
	}
	for _, brief := range adsTableBriefs {
		rsp.Nodes = append(rsp.Nodes, (*proto.ModelNode)(converter.ConvertPhysicalModelToModelBrief(brief)))
	}

	// 3.1 获取多维下游应用视图关系
	adsViewRelations := []*modelProto.ModelRefRelation{}
	if len(codes) > 0 {
		if relations, err = s.indicatorViewService.GetAdsViewRelationsByUpstreamMultiDimModelCodes(ctx, project, codes); err != nil {
			return nil, err
		} else {
			adsViewRelations = append(adsViewRelations, relations...)
		}
	}

	// 3.2 获取物理表下游应用视图关系
	if relations, err = s.indicatorViewService.GetAdsViewRelationsByUpstreamPhysicalModelCodes(ctx, project, []string{code}); err != nil {
		return nil, err
	} else {
		adsViewRelations = append(adsViewRelations, relations...)
	}
	// 3.3 保存应用视图关系
	adsViewCodes := []string{}
	rsp.Relations = append(rsp.Relations, adsViewRelations...)
	for _, r := range adsViewRelations {
		adsViewCodes = append(adsViewCodes, r.ToCode)
	}
	// 3.4 批量获取应用视图briefs
	adsViewCodes = lo.Uniq(adsViewCodes)
	if len(adsViewCodes) > 0 {
		var adsViewBriefs []*modelProto.IndicatorViewBrief
		adsViewBriefs, err = s.indicatorViewService.ListIndicatorBriefs(ctx, project, modelProto.ListIndicatorViewReq{
			Codes:            adsViewCodes,
			Environment:      cBase.ProjectEnvDev,
			QueryBaseRequest: cBase.QueryBaseNoPageRequest(),
		})
		if err != nil {
			return nil, err
		}
		for _, brief := range adsViewBriefs {
			rsp.Nodes = append(rsp.Nodes, (*proto.ModelNode)(converter.ConvertIndicatorViewModelToModelBrief(brief)))
		}
	}

	return rsp, nil
}

func (s *ModelAggInternalService) getMultiDimRelationMap(ctx context.Context, project string, code string) (*proto.GetModelRelationMapRsp, error) {
	rsp := &proto.GetModelRelationMapRsp{
		BaseCode:  code,
		Relations: []*modelProto.ModelRefRelation{},
		Nodes:     []*proto.ModelNode{},
	}

	// 1. 获取多维模型
	multiDimModel, err := s.multiDimModelService.GetMultiDimModelBriefWithContent(ctx, project, code, entities.ProjectEnvDev)
	if err != nil {
		return nil, err
	}
	rsp.Nodes = append(rsp.Nodes, (*proto.ModelNode)(converter.ConvertMultiDimModelToModelBrief(multiDimModel.MultiDimModelBrief)))

	// 2. 获取上游节点信息
	nodes, relations, err := s.getMultiDimModelUpstream(ctx, project, multiDimModel)
	if err != nil {
		return nil, err
	}
	rsp.Nodes = append(rsp.Nodes, nodes...)
	rsp.Relations = append(rsp.Relations, relations...)

	// 3. 获取下游节点信息
	// 3.1 获取多维下游应用表
	// 3.2 获取多维下游应用视图
	relations, err = s.indicatorViewService.GetAdsViewRelationsByUpstreamMultiDimModelCodes(ctx, project, []string{code})
	if err != nil {
		return nil, err
	}
	rsp.Relations = append(rsp.Relations, relations...)

	toCodes := []string{}
	for _, r := range relations {
		toCodes = append(toCodes, r.ToCode)
	}
	toCodes = lo.Uniq(toCodes)
	if len(toCodes) > 0 {
		var adsIndicatorModels []*modelProto.IndicatorViewBrief
		adsIndicatorModels, err = s.indicatorViewService.ListIndicatorBriefs(ctx, project, modelProto.ListIndicatorViewReq{
			Codes:            toCodes,
			Environment:      cBase.ProjectEnvDev,
			QueryBaseRequest: cBase.QueryBaseNoPageRequest(),
		})
		if err != nil {
			return nil, err
		}
		for _, adsModel := range adsIndicatorModels {
			rsp.Nodes = append(rsp.Nodes, (*proto.ModelNode)(converter.ConvertIndicatorViewModelToModelBrief(adsModel)))
		}
	}

	return rsp, nil
}

func (s *ModelAggInternalService) getAdsViewRelationMap(ctx context.Context, project string, code string) (*proto.GetModelRelationMapRsp, error) {
	rsp := &proto.GetModelRelationMapRsp{
		BaseCode:  code,
		Relations: []*modelProto.ModelRefRelation{},
		Nodes:     []*proto.ModelNode{},
	}

	// 获取应用视图
	adsView, err := s.indicatorViewService.GetIndicatorBriefWithContent(ctx, project, code, cBase.ProjectEnvDev)
	if err != nil {
		return nil, err
	}
	rsp.Nodes = append(rsp.Nodes, (*proto.ModelNode)(converter.ConvertIndicatorViewModelToModelBrief(adsView.IndicatorViewBrief)))

	// 获取应用视图模型依赖
	if adsView.ViewContent == nil {
		return rsp, nil
	}
	dep, err := adsView.ViewContent.GetDependence()
	if err != nil {
		return rsp, nil
	}
	nodes, relations, err := s.getAdsModelUpstream(ctx, project, code, dep)
	if err != nil {
		return nil, err
	}
	rsp.Nodes = append(rsp.Nodes, nodes...)
	rsp.Relations = append(rsp.Relations, relations...)
	return rsp, nil
}

func (s *ModelAggInternalService) getAdsModelUpstream(ctx context.Context, project string, code string, dep *rpc_call.Dependence) ([]*proto.ModelNode, []*modelProto.ModelRefRelation, error) {
	relations := []*modelProto.ModelRefRelation{}
	nodes := []*proto.ModelNode{}
	relations = append(relations, &modelProto.ModelRefRelation{
		FromCode: dep.CodeId,
		ToCode:   code,
	})
	if dep.Category == baseProto.MultiDimCate {
		// 获取上游多维模型
		multiDimModel, err := s.multiDimModelService.GetMultiDimModelBriefWithContent(ctx, project, dep.CodeId, entities.ProjectEnvDev)
		if err != nil {
			return nil, nil, err
		}
		nodes = append(nodes, (*proto.ModelNode)(converter.ConvertMultiDimModelToModelBrief(multiDimModel.MultiDimModelBrief)))
		upstreamNodes, upstreamRelations, err := s.getMultiDimModelUpstream(ctx, project, multiDimModel)
		nodes = append(nodes, upstreamNodes...)
		relations = append(relations, upstreamRelations...)
	} else {
		// 获取上游物理模型
		// todo:  替换处理(黄文武)
		briefs, err := s.modelService.ListPhysicalModelBriefs(ctx, project, modelProto.ListModelDefinitionReq{
			PhysicalModelCodes: []string{dep.CodeId},
			Environment:        cBase.ProjectEnvDev,
			QueryBaseRequest:   cBase.QueryBaseNoPageRequest(),
		})
		if err != nil {
			return nil, nil, err
		}
		for _, brief := range briefs {
			nodes = append(nodes, (*proto.ModelNode)(converter.ConvertPhysicalModelToModelBrief(brief)))
		}
	}
	return nodes, relations, nil
}

func (s *ModelAggInternalService) getMultiDimModelUpstream(ctx context.Context, project string, multiDimModel *multiDimModelProto.MultiDimModelBriefWithContent) (nodes []*proto.ModelNode, relations []*modelProto.ModelRefRelation, err error) {
	codes := []string{}
	for _, item := range multiDimModel.ViewContent.Node {
		codes = append(codes, item.CodeId)
		relations = append(relations, &modelProto.ModelRefRelation{
			FromCode: item.CodeId,
			ToCode:   multiDimModel.Code,
		})
	}
	if len(codes) > 0 {
		var briefs []*modelProto.PhysicalModelBrief
		// todo:  替换处理(黄文武)
		briefs, err = s.modelService.ListPhysicalModelBriefs(ctx, project, modelProto.ListModelDefinitionReq{
			PhysicalModelCodes: codes,
			Environment:        cBase.ProjectEnvDev,
			QueryBaseRequest:   cBase.QueryBaseNoPageRequest(),
		})
		if err != nil {
			return
		}
		for _, brief := range briefs {
			nodes = append(nodes, (*proto.ModelNode)(converter.ConvertPhysicalModelToModelBrief(brief)))
		}
	}
	return nodes, relations, nil
}
