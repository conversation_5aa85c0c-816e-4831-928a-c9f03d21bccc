package services

import (
	"context"
	"github.com/jinzhu/copier"
	newBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/subject"
	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/model_agg/converter"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/model_agg/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	multiDimModelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/proto"
	projectProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/project/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/errgroup"
	"strings"
)

func (s *ModelAggInternalService) ListAllModelBriefs(ctx context.Context, project string, req proto.ListModelBriefReq) (rsp proto.ListAllModelBriefsRsp, err error) {
	// t0 := time.Now()
	if rsp, err = s.listAllModelBriefs(ctx, project, req); err != nil {
		return
	}
	// log.Info("debug [ListAllModelBriefs] Time consumption 1：%v", time.Now().Sub(t0))
	// 获取主题信息
	var subjects []*projectProto.Subject
	subjects, err = s.projectService.GetProjectSubjects(ctx, project)
	if err != nil {
		return
	}
	// log.Info("debug [GetProjectSubjects] Time consumption 2：%v", time.Now().Sub(t0))

	subjectMap := map[string]*projectProto.Subject{}
	businessUnitIds := make([]string, 0)
	for _, subject := range subjects {
		subjectMap[subject.ID] = subject
		businessUnitIds = append(businessUnitIds, subject.BusinessUnitId)
	}

	//获取业务板块信息
	businessUnitNameMap := make(map[string]string, 0)
	businessUnits, err := s.projectService.ListBusinessUnitsByIds(ctx, project, businessUnitIds)
	if err != nil {
		return
	}
	for _, v := range businessUnits {
		businessUnitNameMap[v.Id] = v.Name
	}
	// log.Info("debug [ListBusinessUnitsByIds] Time consumption 5：%v", time.Now().Sub(t0))

	// 补充主题名称
	for _, brief := range rsp {
		if subject, ok := subjectMap[brief.SubjectID]; ok {
			brief.SubjectName = subject.Name
			brief.BusinessUnitId = subject.BusinessUnitId
			brief.BusinessUnitName = businessUnitNameMap[subject.BusinessUnitId]
		}
	}
	// log.Info("debug [ListAllModelBriefs] Time consumption 6：%v", time.Now().Sub(t0))
	return
}

func (s *ModelAggInternalService) QueryModelBriefTree(ctx context.Context, project string, req proto.QueryModelBriefTreeReq) (rsp proto.QueryModelBriefTreeRsp, err error) {
	var listReq = proto.ListModelBriefReq{}
	if err = copier.Copy(&listReq, &req); err != nil {
		return
	}
	var listModelsRsp proto.ListAllModelBriefsRsp
	if listModelsRsp, err = s.listAllModelBriefs(ctx, project, listReq); err != nil {
		return
	}
	// 遍历列表，构建主题->模型的映射
	var subjectToModelMap = make(map[string][]*proto.ModelBrief)
	for _, item := range listModelsRsp {
		if _, ok := subjectToModelMap[item.SubjectID]; !ok {
			subjectToModelMap[item.SubjectID] = make([]*proto.ModelBrief, 0)
		}
		subjectToModelMap[item.SubjectID] = append(subjectToModelMap[item.SubjectID], item)
	}

	rsp = make([]*proto.ModelBriefTreeItem, 0)
	// 获取主题域树形结构
	var subjectList []*subject.BusinessUnitSubject
	if subjectList, err = s.newSubjectService.GetBusinessUnitSubjectList(ctx, project); err != nil {
		return
	}
	for _, businessUnit := range subjectList {
		var item = &proto.ModelBriefTreeItem{
			BusinessUnitId:   businessUnit.BusinessUnitId,
			BusinessUnitName: businessUnit.BusinessUnitName,
			SubjectList:      make([]*proto.SubjectWithModels, 0),
		}
		for _, subjectInfo := range businessUnit.SubjectList {
			var subjectWithModels = &proto.SubjectWithModels{
				SubjectId:   subjectInfo.SubjectId,
				SubjectName: subjectInfo.SubjectName,
			}
			subjectWithModels.ModelList = make([]*proto.ModelBrief, 0)
			if models, ok := subjectToModelMap[subjectInfo.SubjectId]; ok {
				subjectWithModels.ModelList = models
				subjectWithModels.ModelTotal = len(models)
			}
			item.SubjectList = append(item.SubjectList, subjectWithModels)
			item.ModelTotal += subjectWithModels.ModelTotal
		}
		rsp = append(rsp, item)
	}
	return
}

func (s *ModelAggInternalService) ListAllModels(ctx context.Context, project string, req proto.ListAllModelsReq) (rsp proto.ListAllModelsRsp, err error) {
	// 默认排序
	query := cBase.QueryBaseNoPageRequest()
	query.Sorts = "name asc"

	// 响应
	g := errgroup.WithContext(ctx)

	var dimRsp, dwdRsp, dwsRsp, dwsViewRsp []*modelBase.Model
	var multiDimRsp []multiDimModelProto.MultiDimModelResp

	// 维度
	g.Go(func(ctx context.Context) (innerError error) {
		listDimModelReq := modelProto.ListModels{
			SubjectID:        req.SubjectID,
			Category:         modelBase.DimModelCate,
			QueryBaseRequest: query,
			Environment:      string(entities.ProjectEnvDev),
		}
		dimRsp, innerError = s.modelService.List(ctx, project, listDimModelReq)
		if innerError != nil {
			return
		}
		return
	})

	// 事实
	g.Go(func(ctx context.Context) (innerError error) {
		listDwdModelReq := modelProto.ListModels{
			SubjectID:        req.SubjectID,
			Category:         modelBase.DwdModelCate,
			QueryBaseRequest: query,
			Environment:      string(entities.ProjectEnvDev),
		}
		dwdRsp, innerError = s.modelService.List(ctx, project, listDwdModelReq)
		if innerError != nil {
			return
		}
		return
	})

	// 汇总
	g.Go(func(ctx context.Context) (innerError error) {
		listDwsModelReq := modelProto.ListModels{
			SubjectID:        req.SubjectID,
			Category:         modelBase.DwsModelCate,
			QueryBaseRequest: query,
			Environment:      string(entities.ProjectEnvDev),
		}
		dwsRsp, innerError = s.modelService.List(ctx, project, listDwsModelReq)
		if innerError != nil {
			return
		}
		return
	})

	// 汇总视图
	g.Go(func(ctx context.Context) (innerError error) {
		listDwsModelReq := modelProto.ListModels{
			SubjectID:        req.SubjectID,
			QueryBaseRequest: query,
			Environment:      string(entities.ProjectEnvDev),
		}
		dwsViewRsp, innerError = s.modelService.DwsViewList(ctx, project, listDwsModelReq)
		if innerError != nil {
			return
		}
		return
	})

	//多维模型
	g.Go(func(ctx context.Context) (innerError error) {
		listMultiDimModelReq := multiDimModelProto.GetMultiDimModelListRequest{
			SubjectID:           req.SubjectID,
			Sorts:               query.Sorts,
			IncludeUnsavedModel: 1,
			MergeStandard:       false,
			QueryBaseRequest:    query,
		}
		multiDimRsp, innerError = s.multiDimModelService.GetMultiDimModelList(ctx, project, listMultiDimModelReq)
		return
	})

	if err = g.Wait(); err != nil {
		return
	}

	dwsRsp = append(dwsRsp, dwsViewRsp...)

	dimWrappers := []*proto.ModelWrapper{}
	dwdWrappers := []*proto.ModelWrapper{}
	dwsWrappers := []*proto.ModelWrapper{}
	multiDimWrappers := []*proto.MultiDimModelWrapper{}

	// convert each model struct to wrapper struct
	for _, dim := range dimRsp {
		dimWrappers = append(dimWrappers, &proto.ModelWrapper{
			Model:           dim,
			SameWithRelease: converter.ConvertModifyStatusToSameWithReleaseDesc(dim.ReleaseStatus, dim.ModifyStatus),
			Status:          converter.ConvertReleaseStatusToBriefStatus(dim.ReleaseStatus, dim.ModifyStatus, dim.CreatedOn, dim.ModifiedOn),
			BackendCategory: modelBase.DimModelCate,
		})
	}
	//
	for _, dwd := range dwdRsp {
		dwdWrappers = append(dwdWrappers, &proto.ModelWrapper{
			Model:           dwd,
			SameWithRelease: converter.ConvertModifyStatusToSameWithReleaseDesc(dwd.ReleaseStatus, dwd.ModifyStatus),
			Status:          converter.ConvertReleaseStatusToBriefStatus(dwd.ReleaseStatus, dwd.ModifyStatus, dwd.CreatedOn, dwd.ModifiedOn),
			BackendCategory: modelBase.DwdModelCate,
		})
	}
	for _, dws := range dwsRsp {
		dws.Category = base.DwsModelCate
		dwsWrappers = append(dwsWrappers, &proto.ModelWrapper{
			Model:           dws,
			SameWithRelease: converter.ConvertModifyStatusToSameWithReleaseDesc(dws.ReleaseStatus, dws.ModifyStatus),
			Status:          converter.ConvertReleaseStatusToBriefStatus(dws.ReleaseStatus, dws.ModifyStatus, dws.CreatedOn, dws.ModifiedOn),
			BackendCategory: modelBase.DwsModelCate,
		})
	}

	for idx := range multiDimRsp {
		multiDim := &multiDimRsp[idx]
		multiDimWrappers = append(multiDimWrappers, &proto.MultiDimModelWrapper{
			MultiDimModelResp: multiDim,
			SameWithRelease:   converter.ConvertModifyStatusToSameWithReleaseDesc(multiDim.ReleaseStatus, multiDim.ModifyStatus),
			Status:            converter.ConvertReleaseStatusToBriefStatus(multiDim.ReleaseStatus, multiDim.ModifyStatus, multiDim.CreatedOn, multiDim.ModifiedOn),
			BackendCategory:   modelBase.MultiDimCate,
		})
	}

	rsp.Dim = dimWrappers
	rsp.Dwd = dwdWrappers
	rsp.Dws = dwsWrappers
	rsp.MultiDim = multiDimWrappers
	return
}

func (s *ModelAggInternalService) listAllModelBriefs(ctx context.Context, project string, req proto.ListModelBriefReq) (rsp proto.ListAllModelBriefsRsp, err error) {
	// 默认排序
	env := cBase.ProjectEnvDev
	if len(req.Env) > 0 {
		env = cBase.ProjectEnvType(req.Env)
	}

	devMode := "config,sql,detail"
	if len(req.DevMode) > 0 {
		devMode = req.DevMode
	}

	query := cBase.QueryBaseNoPageRequest()
	query.Sorts = "created_on DESC"
	query.Keyword = req.Keyword
	if len(req.Category) == 0 {
		req.Category = "dim,dwd,dws"
	}

	category := strings.Split(req.Category, ",")
	categoryMap := make(map[string]string)
	for _, item := range category {
		categoryMap[item] = item
	}

	// 响应
	g := errgroup.WithContext(ctx)

	var physicalModelRsp []*modelProto.PhysicalModelBrief
	var multiDimRsp []*multiDimModelProto.MultiDimModelBrief
	var IndicatorViewRsp []*modelProto.IndicatorViewBrief

	// 兼容处理devMode和物理模型类型,只有单独请求类型为:dws时才判断devMode
	toFindPhysical := false
	if _, ok := categoryMap[string(newBase.DimModelCate)]; ok {
		toFindPhysical = true
	}

	if _, ok := categoryMap[string(newBase.DwdModelCate)]; ok {
		toFindPhysical = true
	}

	if _, ok := categoryMap[string(newBase.DwsModelCate)]; ok && strings.Contains(devMode, string(newBase.DevModeTypeSql)) {
		toFindPhysical = true
	}

	if toFindPhysical {
		g.Go(func(ctx context.Context) (innerError error) {
			physicalModelRsp, innerError = s.modelService.ListPhysicalModelBriefs(ctx, project, modelProto.ListModelDefinitionReq{
				SubjectID:          req.SubjectId,
				Environment:        env,
				QueryBaseRequest:   query,
				Category:           req.Category,
				PhysicalModelCodes: req.Codes,
				EtlDevMode:         req.EtlDevMode,
			})
			return
		})
	}

	// 应用表视图
	if _, ok := categoryMap[string(newBase.DwsModelCate)]; ok && (strings.Contains(devMode, string(newBase.DevModeTypeConfig)) || strings.Contains(devMode, string(newBase.DevModeTypeDetail))) {
		g.Go(func(ctx context.Context) (innerError error) {
			IndicatorViewRsp, innerError = s.indicatorViewService.ListIndicatorBriefs(ctx, project, modelProto.ListIndicatorViewReq{
				SubjectID:        req.SubjectId,
				Environment:      env,
				QueryBaseRequest: query,
				Codes:            req.Codes,
			})
			return
		})
	}

	// 多维模型
	if _, ok := categoryMap[string(newBase.MultiDimCate)]; ok {
		g.Go(func(ctx context.Context) (innerError error) {
			multiDimRsp, innerError = s.multiDimModelService.GetMultiDimModelBriefList(ctx, project, multiDimModelProto.GetMultiDimModelListRequest{
				SubjectID:           req.SubjectId,
				Environment:         entities.ProjectEnvType(env),
				IncludeUnsavedModel: 1,
				QueryBaseRequest:    query,
				ModelCodes:          req.Codes,
			})
			return
		})
	}

	if err = g.Wait(); err != nil {
		return
	}

	rsp = make([]*proto.ModelBrief, 0)
	for _, item := range physicalModelRsp {
		rsp = append(rsp, converter.ConvertPhysicalModelToModelBrief(item))
	}

	for _, item := range multiDimRsp {
		rsp = append(rsp, converter.ConvertMultiDimModelToModelBrief(item))
	}

	for _, item := range IndicatorViewRsp {
		rsp = append(rsp, converter.ConvertIndicatorViewModelToModelBrief(item))
	}
	return
}
