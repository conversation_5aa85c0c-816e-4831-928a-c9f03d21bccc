package proto

import (
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	multiDimProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/proto"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

type ModelWrapper struct {
	*modelBase.Model
	SameWithRelease SameWithReleaseDesc        `json:"same_with_release"`
	Status          modelBase.ModelBriefStatus `json:"status"`
	BackendCategory modelBase.ModelCategory    `json:"backend_category"`
}

type ModelDefinitionWrapper struct {
	*modelProto.ListModelDefinition
	SameWithRelease SameWithReleaseDesc        `json:"same_with_release"`
	Status          modelBase.ModelBriefStatus `json:"status"`
	BackendCategory modelBase.ModelCategory    `json:"backend_category"`
}

type MultiDimModelWrapper struct {
	*multiDimProto.MultiDimModelResp
	SameWithRelease SameWithReleaseDesc        `json:"same_with_release"`
	Status          modelBase.ModelBriefStatus `json:"status"`
	BackendCategory modelBase.ModelCategory    `json:"backend_category"`
}

type SameWithReleaseDesc string

const (
	IsSameWithRelease  SameWithReleaseDesc = "是"
	NotSameWithRelease SameWithReleaseDesc = "否"
)

type ModelBrief struct {
	ID               string                     `json:"id"`
	Code             string                     `json:"code"`
	SubjectID        string                     `json:"subject_id"`
	SubjectName      string                     `json:"subject_name"`
	Category         modelBase.ModelCategory    `json:"category"`         // dim, dwd, dws, ads, multi_dim
	BackendCategory  modelBase.ModelCategory    `json:"backend_category"` // dim, dwd, dws, ads, ads_view, multi_dim
	Name             string                     `json:"name"`
	NameCn           string                     `json:"name_cn"`
	Status           modelBase.ModelBriefStatus `json:"status"`
	SameWithRelease  SameWithReleaseDesc        `json:"same_with_release"`
	CreatedOn        db.NullTime                `json:"created_on"`
	ModifiedOn       db.NullTime                `json:"modified_on"`
	CreatedBy        string                     `json:"created_by"`  // 创建人
	ModifiedBy       string                     `json:"modified_by"` // 修改人
	DevMode          string                     `json:"dev_mode"`
	Description      string                     `json:"description"`
	BusinessUnitId   string                     `json:"business_unit_id"`
	BusinessUnitName string                     `json:"business_unit_name"`
	EtlDevMode       string                     `json:"etl_dev_mode"`
}

type ModelIdentify struct {
	// 兼容旧接口，有些会使用id，有些会使用code
	Id       string                  `json:"id" form:"id"`
	Code     string                  `json:"code" form:"code"`
	Category modelBase.ModelCategory `json:"category" form:"category"`
}
