package proto

import modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"

type ListAllModelBriefsRsp []*ModelBrief

type ListAllModelsReq struct {
	SubjectID     string   `form:"subject_id" json:"subject_id"`
	Category      []string `form:"category" json:"category"`
	MergeStandard bool     `form:"merge_standard" json:"merge_standard"`
}

type ListAllModelsRsp struct {
	Dim      []*ModelWrapper           `json:"dim"`
	Dwd      []*ModelWrapper           `json:"dwd"`
	Dws      []*ModelWrapper           `json:"dws"`
	Ads      []*ModelDefinitionWrapper `json:"ads"`
	MultiDim []*MultiDimModelWrapper   `json:"multi_dim"`
}

type GetModelRelationMapReq ModelIdentify

type ModelNode ModelBrief

type GetModelRelationMapRsp struct {
	BaseCode  string                         `json:"base_code"`
	Nodes     []*ModelNode                   `json:"nodes"`
	Relations []*modelProto.ModelRefRelation `json:"relations"`
}

type ListModelBriefReq struct {
	Category   string   `form:"category" json:"category"`     //类型
	SubjectId  string   `form:"subject_id" json:"subject_id"` //主题
	Keyword    string   `form:"keyword" json:"keyword"`       //关键词
	DevMode    string   `form:"dev_mode" json:"dev_mode"`     //开发模式
	Env        string   `form:"env" json:"env"`               //环境
	Codes      []string `form:"codes" json:"codes"`           //模型代码
	EtlDevMode string   `form:"etl_dev_mode" json:"etl_dev_mode"`
}

type QueryModelBriefTreeReq ListModelBriefReq

type SubjectWithModels struct {
	SubjectId   string        `json:"subject_id"`
	SubjectName string        `json:"subject_name"`
	ModelTotal  int           `json:"model_total"`
	ModelList   []*ModelBrief `json:"model_list"`
}
type ModelBriefTreeItem struct {
	BusinessUnitId   string               `json:"business_unit_id"`
	BusinessUnitName string               `json:"business_unit_name"`
	ModelTotal       int                  `json:"model_total"`
	SubjectList      []*SubjectWithModels `json:"subjects"`
}

type QueryModelBriefTreeRsp []*ModelBriefTreeItem
