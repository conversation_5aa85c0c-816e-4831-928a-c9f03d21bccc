package converter

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/model_agg/proto"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	modelProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/proto"
	multiDimProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/multi_dim_model/proto"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"
)

func ConvertPhysicalModelToModelBrief(source *modelProto.PhysicalModelBrief) *proto.ModelBrief {
	return &proto.ModelBrief{
		ID:              source.ID,
		Code:            source.Code,
		SubjectID:       source.SubjectID,
		SubjectName:     "",
		Category:        source.Category,
		BackendCategory: source.Category,
		Name:            source.TableName,
		NameCn:          source.Name,
		Status:          ConvertReleaseStatusToBriefStatus(source.ReleaseStatus, source.ModifyStatus, source.CreatedOn, source.ModifiedOn),
		CreatedOn:       source.CreatedOn,
		ModifiedOn:      source.ModifiedOn,
		SameWithRelease: ConvertModifyStatusToSameWithReleaseDesc(source.ReleaseStatus, source.ModifyStatus),
		CreatedBy:       source.CreatedBy.String,
		ModifiedBy:      source.ModifiedBy.String,
		DevMode:         source.DevMode,
		Description:     source.Description,
		EtlDevMode:      source.EtlDevMode,
	}
}

func ConvertIndicatorViewModelToModelBrief(source *modelProto.IndicatorViewBrief) *proto.ModelBrief {
	return &proto.ModelBrief{
		ID:              source.ID,
		Code:            source.Code,
		SubjectID:       source.SubjectID,
		SubjectName:     "",
		Category:        modelBase.DwsModelCate,
		BackendCategory: modelBase.DwsModelCate,
		Name:            source.ViewName,
		NameCn:          source.Name,
		Status:          ConvertReleaseStatusToBriefStatus(source.ReleaseStatus, source.ModifyStatus, source.CreatedOn, source.ModifiedOn),
		CreatedOn:       source.CreatedOn,
		ModifiedOn:      source.ModifiedOn,
		SameWithRelease: ConvertModifyStatusToSameWithReleaseDesc(source.ReleaseStatus, source.ModifyStatus),
		CreatedBy:       source.CreatedBy.String,
		ModifiedBy:      source.ModifiedBy.String,
		DevMode:         source.DevMode,
		Description:     source.Description,
	}
}

func ConvertMultiDimModelToModelBrief(source *multiDimProto.MultiDimModelBrief) *proto.ModelBrief {
	return &proto.ModelBrief{
		ID:              source.ID,
		Code:            source.Code,
		SubjectID:       source.SubjectID,
		SubjectName:     "",
		Category:        modelBase.MultiDimCate,
		BackendCategory: modelBase.MultiDimCate,
		Name:            source.Name,
		NameCn:          source.NameCn,
		Status:          ConvertReleaseStatusToBriefStatus(source.ReleaseStatus, source.ModifyStatus, source.CreatedOn, source.ModifiedOn),
		CreatedOn:       source.CreatedOn,
		ModifiedOn:      source.ModifiedOn,
		SameWithRelease: ConvertModifyStatusToSameWithReleaseDesc(source.ReleaseStatus, source.ModifyStatus),
		CreatedBy:       source.CreatedBy.String,
		ModifiedBy:      source.ModifiedBy.String,
		Description:     source.Description,
	}
}

func ConvertReleaseStatusToBriefStatus(releaseStatus modelBase.ReleaseStatus, modifyStatus modelBase.ModifyStatus,
	createdOn db.NullTime, modifiedOn db.NullTime) modelBase.ModelBriefStatus {
	var status modelBase.ModelBriefStatus
	if releaseStatus == modelBase.Unrelease {
		status = modelBase.Unreleased
	} else if releaseStatus == modelBase.Release || releaseStatus == modelBase.FirstRelease {
		if modifyStatus == modelBase.ModifiedModifyStatus && createdOn != modifiedOn {
			status = modelBase.Editing
		} else {
			status = modelBase.Released
		}
	} else {
		status = modelBase.OfflineAlready
	}
	return status
}

func ConvertModifyStatusToSameWithReleaseDesc(releaseStatus modelBase.ReleaseStatus, modifyStatus modelBase.ModifyStatus) proto.SameWithReleaseDesc {
	if releaseStatus != modelBase.FirstRelease && releaseStatus != modelBase.Release {
		return proto.NotSameWithRelease
	}
	if modifyStatus == modelBase.ModifiedModifyStatus {
		return proto.NotSameWithRelease
	}
	return proto.IsSameWithRelease
}
