package proto

import (
	"context"
	"github.com/gin-gonic/gin"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/http/utils"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/apierr"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors/biz"
	"gitlab.mypaas.com.cn/dmp/gopkg/str"
	"gitlab.mypaas.com.cn/fast/tracker-go/trace"
)

type PaginateData struct {
	TotalNum int64                    `json:"totalNum"`
	PageSize int64                    `json:"pageSize"`
	PageNum  int64                    `json:"pageNum"`
	Rows     []map[string]interface{} `json:"rows"`
}

type ApiQueryResult struct {
	RequestID string         `json:"requestId"`
	TraceID   string         `json:"traceId"`
	ErrCode   errors.ErrCode `json:"errCode"`
	ErrMsg    string         `json:"errMsg"`
	Data      interface{}    `json:"data"`
	Sql       []string       `json:"sql"`
}

func GenSuccessResponse(ctx context.Context, data interface{}) *ApiQueryResult {
	return &ApiQueryResult{
		ErrCode:   0,
		ErrMsg:    "成功",
		Data:      data,
		TraceID:   trace.GetTraceId(ctx),
		RequestID: str.UUID(),
	}
}

func GenFailedResponse(ctx context.Context, c *gin.Context, err error) (rsp *ApiQueryResult) {
	rsp = &ApiQueryResult{
		RequestID: str.UUID(),
		TraceID:   trace.GetTraceId(ctx),
	}

	_ = c.Error(err)
	if errors.Is(err, context.DeadlineExceeded) || errors.Is(err, context.Canceled) {
		e := errors.New(biz.API_SQL_EXECUTE_TIMEOUT_ERR)
		rsp.ErrCode = e.Code
		rsp.ErrMsg = e.Error()
		return
	}

	switch e := err.(type) {
	case *errors.Error:
		rsp.ErrCode = e.Code
		rsp.ErrMsg = e.Error()
	case *utils.UserError:
		userErr := apierr.NewCommonUserErrWrap(err)
		rsp.ErrCode = userErr.Code
		rsp.ErrMsg = userErr.Error()
	default:
		internalServerErr := errors.Wrap(biz.API_INTERNAL_ERR, err)
		rsp.ErrCode = internalServerErr.Code
		rsp.ErrMsg = internalServerErr.Error()
	}
	return rsp
}
