package proto

const (
	TokenExpiresIn int64 = 7200
)

type GetTokenReq struct {
	AppID     string `form:"app_id" json:"app_id" `
	AppKey    string `form:"app_key" json:"app_key"`
	AppSecret string `form:"app_secret" json:"app_secret"`
}

type ReloadAppReq struct {
	Key string `form:"key" json:"key"`
}

type ReloadAppRsp struct {
	Secrets map[string]string `json:"secrets"`
}

type GetTokenResponse struct {
	Token     string `form:"token" json:"token"`
	ExpiresIn int64  `form:"expires_in" json:"expires_in"`
}

