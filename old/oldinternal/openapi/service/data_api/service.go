package data_api

import (
	"context"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/sqlparse/proxy"
	"regexp"
	"strconv"
	"time"

	"github.com/defval/inject/v2"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors/biz"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/context_helper"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/errgroup"
	sm3Jwt "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/jwt/sm3"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/interface_utils"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/json_utils"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/trace_utils"

	dap_common "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common"

	"github.com/gin-gonic/gin"

	queryEngine "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_query/model/engine"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/apierr"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	eventfast "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/fast"
	pkgFast "gitlab.mypaas.com.cn/dmp/gopkg/bigdata/fast"
)

func init() {
	global.InjectContext = append(global.InjectContext, inject.Provide(NewDataApiService))
}

// CtxProjectKey 上下文项目code Key
const CtxProjectKey = "project"

// CtxOpenApiPathKey 上下文apiPAth Key
const CtxOpenApiPathKey = "api_path"

const OpenApiProductPath = "/product"

type DataApiService struct {
	meta      *ApiMeta
	recorder  *CallApiFastRecorder
	dapCommon dap_common.DapCommonService
}

func NewDataApiService(meta *ApiMeta, recorder *CallApiFastRecorder) *DataApiService {
	dataApiService := &DataApiService{
		meta:      meta,
		recorder:  recorder,
		dapCommon: dap_common.NewDapCommonServiceRepoImpl(),
	}

	return dataApiService
}

func (s *DataApiService) GetToken(ctx context.Context, req *proto.GetTokenReq) (rsp *proto.GetTokenResponse, err error) {
	return s.GenUnifiedAuthenticationToken(ctx, "")
}

func (s *DataApiService) GenUnifiedAuthenticationToken(ctx context.Context, tenantCode string) (rsp *proto.GetTokenResponse, err error) {
	rsp = new(proto.GetTokenResponse)
	jwts, err := sm3Jwt.NewJwkSets(global.AppConfig.GetSkylineAppSecret(tenantCode), "天际", global.AppConfig.JWT.Expires)
	if err != nil {
		return
	}

	exp := global.AppConfig.JWT.Expires
	if exp < 1 {
		exp = int(proto.TokenExpiresIn)
	}

	rsp.Token, err = jwts.CreateToken(exp)
	if err != nil {
		return
	}
	rsp.ExpiresIn = int64(exp)

	return
}

func (s *DataApiService) QueryData(ctx context.Context, c *gin.Context, project string, apiMeta *base.ApiDetail, inputParams []*base.InputRequestParam) (rsp *proto.ApiQueryResult, sqls []string, args []interface{}, cost time.Duration) {
	ctx, t := trace_utils.StartSpanWithContext(ctx, "自定义API查询")
	defer func() {
		t.Tag("params", json_utils.MarshalOrError(inputParams))
		t.Tag("sqls", fmt.Sprintf("%v", sqls))
		t.End()
	}()

	defer func() {
		r := errgroup.Recover(ctx)
		if r != nil {
			err := errors.Wrap(biz.API_INTERNAL_ERR, r)
			rsp = proto.GenFailedResponse(ctx, c, err)
			return
		}
	}()

	// 查询数据
	data, sqls, args, cost, err := s.query(ctx, project, apiMeta, inputParams)
	if err != nil {
		trace_utils.Errorw(ctx, err)
		rsp = proto.GenFailedResponse(ctx, c, err)
		return
	}
	rsp = proto.GenSuccessResponse(ctx, data)

	// 记录上报一条查询结果
	switch v := data.(type) {
	case []map[string]interface{}:
		t.Tag("count", strconv.Itoa(len(v)))
		if len(v) > 0 {
			t.Tag("first_row", json_utils.MarshalOrError(v[0]))
		}
	case *proto.PaginateData:
		t.Tag("count", strconv.Itoa(len(v.Rows)))
		t.Tag("page_num", strconv.Itoa(int(v.PageNum)))
		t.Tag("page_size", strconv.Itoa(int(v.PageSize)))
		t.Tag("total", strconv.Itoa(int(v.TotalNum)))
		if len(v.Rows) > 0 {
			t.Tag("first_row", json_utils.MarshalOrError(v.Rows[0]))
		}
	}

	return
}

func (s *DataApiService) query(ctx context.Context, project string, apiMeta *base.ApiDetail, inputParams []*base.InputRequestParam) (rsp interface{}, sqls []string, args []interface{}, cost time.Duration, err error) {
	// 校验请求参数
	params, err := validateAndConvertInputParams(apiMeta, inputParams)
	if err != nil {
		return
	}
	paramsMap := lo.SliceToMap(params, func(item *base.InputRequestParam) (string, *base.InputRequestParam) {
		return item.Name, item
	})

	// 获取查询资源
	tenantCode := getTenantCodeFromInputParams(inputParams)
	resource, err := s.dapCommon.GetStorageResourceV3(ctx, project, tenantCode)
	if err != nil {
		return
	}

	// 生成sql
	apiCompiler, err := compiler.NewApiCompiler(project, entities.ProjectResourceType(resource.ResourceType), apiMeta, params)
	if err != nil {
		return
	}
	compiledApi, err := apiCompiler.Compile(ctx)
	if err != nil {
		return
	}

	// 查询数据
	var (
		data  []map[string]interface{}
		total int64
	)
	startTime := time.Now()
	eg := errgroup.WithCancel(ctx)
	engineParam := queryEngine.EngineOptions{
		MaxExecutionTime: context_helper.GetSQLMaxExecutionTime(ctx),
	}
	// 查询明细数据
	args = compiledApi.Query.Args
	sqls = append(sqls, compiledApi.Query.SQL)
	eg.Go(func(ctx context.Context) (err error) {
		engine, err := queryEngine.NewResourceEngineFromResource(resource)
		if err != nil {
			return errors.Wrap(biz.API_RESOURCE_CONFIG_ERR, err)
		}
		data, _, err = engine.Query(ctx, compiledApi.Query.SQL, engineParam, compiledApi.Query.Args...)
		if err != nil {
			return errors.Wrapf(biz.API_SQL_EXECUTE_ERR, err, compiledApi.OrigQuery.SQL)
		}
		return
	})
	// 分页下查询总数
	if compiledApi.IsPaginate {
		sqls = append(sqls, compiledApi.TotalQuery.SQL)
		eg.Go(func(ctx context.Context) (err error) {
			engine, err := queryEngine.NewResourceEngineFromResource(resource)
			if err != nil {
				return errors.Wrap(biz.API_RESOURCE_CONFIG_ERR, err)
			}
			totalData, _, err := engine.Query(ctx, compiledApi.TotalQuery.SQL, engineParam, compiledApi.TotalQuery.Args...)
			if err != nil {
				return errors.Wrapf(biz.API_SQL_EXECUTE_ERR, err, compiledApi.TotalQuery.SQL)
			}
			total, _ = interface_utils.ValueToInt64(totalData[0][base.TotalNumName])
			return
		})
	}
	err = eg.Wait()
	cost = time.Now().Sub(startTime)
	if err != nil {
		return
	}
	// 返回查询结果
	if compiledApi.IsPaginate {
		rsp = &proto.PaginateData{
			Rows:     data,
			TotalNum: total,
			PageSize: paramsMap["pageSize"].Value.(int64),
			PageNum:  paramsMap["pageNum"].Value.(int64),
		}
	} else {
		rsp = data
	}
	return
}

func (s *DataApiService) adapteRequestContext(ctx context.Context, reqCtx *RequestContext) error {
	param, ok := lo.Find(reqCtx.inputParams, func(item *base.InputRequestParam) bool {
		return item.Name == base.BigdataTenantCodePublicParam.Name
	})
	if !ok || param.Value.(string) == "" {
		return nil
	}
	tenantProjectRel, err := s.dapCommon.GetTenantProjectRel(ctx, param.Value.(string))
	if err != nil {
		return apierr.NewCommonUserErrWrap(err)
	}
	if tenantProjectRel.CustomProjectCode != "" {
		reqCtx.project = tenantProjectRel.CustomProjectCode
	} else if tenantProjectRel.CommonProjectCode != "" {
		reqCtx.project = tenantProjectRel.CommonProjectCode
	}
	return nil
}

// Query 数据开放服务openapi查询数据
func (s *DataApiService) ReleaseQuery(ctx context.Context, c *gin.Context) (rsp *proto.ApiQueryResult, err error) {
	startCallTime := time.Now()
	// 上报天眼事件
	event := eventfast.StartEvent(pkgFast.CallApi, c.Param(CtxProjectKey), "")
	defer func() {
		event.EndEvent(ctx, err)
	}()

	// 解析参数
	reqCtx, err := GetInputParamFromGinCtx(c)
	if err != nil {
		return
	}
	// 根据租户code获取正确的项目空间
	if err = s.adapteRequestContext(ctx, reqCtx); err != nil {
		return
	}

	// 获取api元数据
	apiMeta, err := s.getApiMeta(ctx, reqCtx)
	if err != nil {
		return
	}

	// 设置超时时间
	ctx, cancel := context.WithTimeout(ctx, time.Duration(apiMeta.MainInfo.TimeoutLimit)*time.Millisecond)
	defer cancel()

	// 取数
	rsp, sqls, _, cost := s.QueryData(ctx, c, reqCtx.project, apiMeta.Detail, reqCtx.inputParams)
	defer func() {
		event.RecordBizParams(s.recorder.GenerateApiCallFastBizParam(ctx, c, startCallTime, reqCtx, apiMeta, sqls, cost))
	}()
	if IfDebugRequest(reqCtx) {
		rsp.Sql = sqls
	}

	return
}

func (s *DataApiService) getApiMeta(ctx context.Context, reqCtx *RequestContext) (apiMeta *ApiMetaData, err error) {
	ctx, t := trace_utils.StartSpanWithContext(ctx, "获取api元数据")
	defer t.End()
	t.Tag("project", reqCtx.project)
	t.Tag("path", reqCtx.apiPath)
	apiMeta, err = s.meta.GetApiDataByPath(ctx, reqCtx.project, reqCtx.apiPath)
	if err != nil {
		return
	}
	return
}

func (s *DataApiService) GetRefTables(ctx context.Context, project string, apiMeta *base.ApiDetail) (tables []string, err error) {
	inputParams := make([]*base.InputRequestParam, 0)
	defaultValue := "1"
	for _, v := range apiMeta.RequestParams {
		if v.Name == base.DynamicColumnsPublicParam.Name {
			continue
		}
		inputParams = append(inputParams, &base.InputRequestParam{
			Name:  v.Name,
			Value: defaultValue, //随便设置一个值,确保编译能过而已
		})

	}

	// 校验请求参数
	params, err := validateAndConvertInputParams(apiMeta, inputParams)
	if err != nil {
		return
	}

	// 获取查询资源
	resource, err := s.dapCommon.GetStorageResources(ctx, project)
	if err != nil {
		return
	}

	// 生成sql
	apiCompiler, err := compiler.NewApiCompiler(project, entities.ProjectResourceType(resource.ResourceType), apiMeta, params)
	if err != nil {
		return
	}
	compiledApi, err := apiCompiler.Compile(ctx)
	if err != nil {
		return
	}
	sql := compiledApi.Query.SQL
	re := regexp.MustCompile(`\?`)
	sql = re.ReplaceAllString(sql, defaultValue)

	//解析并获取sql中引用的表
	dialect := entities.ProjectResourceType(resource.ResourceType).ToSqlparserDialect()
	parser := proxy.NewTableReplaceParserProxy(sql, dialect)
	err = parser.Parse(ctx, "get_ref_tables")
	if err != nil {
		return
	}
	tables = parser.ParseTables()
	return
}
