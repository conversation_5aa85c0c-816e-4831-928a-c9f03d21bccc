package data_api

import (
	"bytes"
	"encoding/json"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors/biz"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/string_helper"
	"io/ioutil"
	"net/url"

	"github.com/gin-gonic/gin"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/apierr"
)

type RequestFrom string

const (
	// 请求来自Pulsar Kong
	FromPulsarKong RequestFrom = "Pulsar"
	// 请求来自Mysre星图
	FromMysre RequestFrom = "Mysre"
)

const contextRequestFromKey = "requestFromKey"

func SetRequestFrom(c *gin.Context, from RequestFrom) {
	c.Set(contextRequestFromKey, from)
}

func getRequestFrom(c *gin.Context) RequestFrom {
	v, ok := c.Get(contextRequestFromKey)
	if !ok {
		return FromPulsarKong
	}
	return v.(RequestFrom)
}

type RequestContext struct {
	project     string
	apiPath     string
	appKey      string
	inputParams []*base.InputRequestParam
	from        RequestFrom
	ip          string
}

func GetInputParamFromGinCtx(c *gin.Context) (*RequestContext, error) {
	project := c.Param(CtxProjectKey)
	apiPath := c.Param(CtxOpenApiPathKey)
	appKey := c.Request.Header.Get("X-Consumer-Username")

	res := &RequestContext{
		project:     project,
		apiPath:     apiPath,
		appKey:      appKey,
		inputParams: []*base.InputRequestParam{},
		from:        getRequestFrom(c),
		ip:          c.ClientIP(),
	}
	defer func() {
		res.inputParams = DiscardEmptyInputParams(res.inputParams)
	}()

	if c.Request.Method == "GET" {
		res.inputParams = toInputParams(c.Request.URL.Query())
		return res, nil
	}

	if c.ContentType() == "application/json" {
		requestParams := map[string]interface{}{}
		body, err := getBody(c)
		if err != nil {
			return nil, apierr.NewCommonUserErrWrap(err)
		}
		if len(body) == 0 {
			return res, nil
		}
		err = json.Unmarshal(body, &requestParams)
		if err != nil {
			return nil, errors.New(biz.API_PARAM_INVALID_FORMAT_ERR, c.ContentType())
		}
		for key, value := range requestParams {
			res.inputParams = append(res.inputParams, &base.InputRequestParam{
				Name:  key,
				Value: value,
			})
		}
		return res, nil
	}

	if c.ContentType() == "application/x-www-form-urlencoded" {
		body, err := getBody(c)
		if err != nil {
			return nil, apierr.NewCommonUserErrWrap(err)
		}
		postParam, err := url.ParseQuery(string(body))
		if err != nil {
			return nil, apierr.NewCommonUserErrWrap(err)
		}
		res.inputParams = toInputParams(postParam)
		return res, nil
	}

	if c.ContentType() == "multipart/form-data" {
		if _, err := c.MultipartForm(); err != nil {
			return nil, errors.New(biz.API_PARAM_INVALID_FORMAT_ERR, c.ContentType())
		}
	}
	if err := c.Request.ParseForm(); err != nil {
		return nil, errors.New(biz.API_PARAM_INVALID_FORMAT_ERR, c.ContentType())
	}
	res.inputParams = toInputParams(c.Request.PostForm)
	return res, nil
}

func toInputParams(values url.Values) []*base.InputRequestParam {
	inputParams := []*base.InputRequestParam{}
	for paramName, paramValue := range values {
		if len(paramValue) == 1 && paramValue[0] == "" {
			continue
		}
		inputParam := &base.InputRequestParam{
			Name:  paramName,
			Value: paramValue,
		}
		if len(paramValue) == 1 {
			inputParam.Value = paramValue[0]
		}
		inputParams = append(inputParams, inputParam)
	}
	return inputParams
}

func getBody(c *gin.Context) ([]byte, error) {
	body, err := ioutil.ReadAll(c.Request.Body)
	// 解决body不可重复读取的问题
	_ = c.Request.Body.Close()
	c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}
	return body, nil
}

func IfDebugRequest(reqParam *RequestContext) bool {
	if reqParam != nil {
		for _, param := range reqParam.inputParams {
			if param.Name == "debug" {
				if string_helper.ToString(param.Value) == "1" {
					return true
				}
			}
		}
	}

	return false
}
