package data_api

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors/biz"
	"strings"

	compBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/compiler/base"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/openapi/apierr"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/interface_utils"
)

type RequiredType string

const (
	RequiredString  RequiredType = "字符串类型"
	RequiredInteger RequiredType = "整数类型"
	RequiredLong    RequiredType = "长整数类型"
	RequiredFloat   RequiredType = "浮点型类型"
	RequiredDouble  RequiredType = "双精度浮点型类型"
	RequiredBool    RequiredType = "布尔类型"
)

func toInvalidInputParamErr(param *base.InputRequestParam, dataType string) error {
	switch {
	case dataType == string(base.ParamTypeString):
		return errors.New(biz.API_PARAM_INVALID_VALUE_ERR, param.Name, string(RequiredString), param.Value)
	case dataType == string(base.ParamTypeInt):
		return errors.New(biz.API_PARAM_INVALID_VALUE_ERR, param.Name, string(RequiredInteger), param.Value)
	case dataType == string(base.ParamTypeLong):
		return errors.New(biz.API_PARAM_INVALID_VALUE_ERR, param.Name, string(RequiredLong), param.Value)
	case dataType == string(base.ParamTypeFloat):
		return errors.New(biz.API_PARAM_INVALID_VALUE_ERR, param.Name, string(RequiredFloat), param.Value)
	case dataType == string(base.ParamTypeDouble):
		return errors.New(biz.API_PARAM_INVALID_VALUE_ERR, param.Name, string(RequiredDouble), param.Value)
	case dataType == string(base.ParamTypeBoolean):
		return errors.New(biz.API_PARAM_INVALID_VALUE_ERR, param.Name, string(RequiredBool), param.Value)
	default:
		return apierr.NewCommonUserErrf("未实现的类型：%s", dataType)
	}
}

func DiscardEmptyInputParams(inputParams []*base.InputRequestParam) []*base.InputRequestParam {
	newInputParams := []*base.InputRequestParam{}
	for _, param := range inputParams {
		if param.Value == nil {
			continue
		}
		paramStrValue, ok := param.Value.(string)
		if ok && paramStrValue == "" {
			continue
		}
		newInputParams = append(newInputParams, param)
	}
	return newInputParams
}

func validateAndConvertInputParams(apiDetail *base.ApiDetail, params []*base.InputRequestParam) ([]*base.InputRequestParam, error) {
	dbParams := apiDetail.RequestParams
	inputParamMap := map[string]*base.InputRequestParam{}
	for _, inputParam := range params {
		inputParamMap[inputParam.Name] = inputParam
	}
	// 获取请求参数map
	dbReqParamMap := map[string]*base.ApiParamBase{}
	for _, dbReqParam := range dbParams {
		if dbReqParam.ParamType == base.Variable {
			continue
		}
		dbReqParamMap[dbReqParam.Name] = dbReqParam
		// 参数缺失校验
		_, ok := inputParamMap[dbReqParam.Name]
		if ok {
			continue
		}
		// 有默认值先加上
		if dbReqParam.DefaultValue != "" {
			params = append(params, &base.InputRequestParam{Name: dbReqParam.Name, Value: dbReqParam.DefaultValue})
			continue
		}
		// 没有找到对应参数，且没有默认值。必填直接报错，非必填不处理
		if dbReqParam.IsRequired == 1 {
			return nil, errors.New(biz.API_PARAM_NOT_PROVIDE_ERR, dbReqParam.Name)
		}
	}

	for _, param := range params {
		dbReqParam, ok := dbReqParamMap[param.Name]
		if !ok {
			continue
		}
		v, e := interface_utils.ValidateByValueAndOperate(dbReqParam.Operator, dbReqParam.DataType, param.Value)
		if e != nil {
			return nil, toInvalidInputParamErr(param, dbReqParam.DataType)
		}
		param.Value = v
		handleSystemParam(param)
		if param.Name == base.DynamicColumnsPublicParam.Name {
			if err := validateFilteredColumns(param.Value.([]string), apiDetail.ResponseParams); err != nil {
				return nil, err
			}
		}
	}
	return params, nil
}

func validateFilteredColumns(filteredColumns []string, respParams []*base.ApiParamBase) error {
	if len(filteredColumns) == 0 {
		return nil
	}
	dbRespColumnMap := map[string]bool{}
	for _, respParam := range respParams {
		dbRespColumnMap[respParam.Name] = true
	}
	notExistsColumn := []string{}
	for _, column := range filteredColumns {
		if _, ok := dbRespColumnMap[column]; !ok {
			notExistsColumn = append(notExistsColumn, column)
		}
	}
	if len(notExistsColumn) > 0 {
		return apierr.NewCommonUserErrf("请求的动态过滤字段%s不存在", strings.Join(notExistsColumn, ","))
	}
	return nil
}

// handleSystemParam 处理系统参数
func handleSystemParam(input *base.InputRequestParam) {
	if input.Name == base.PageNumPublicParam.Name {
		if input.Value.(int64) <= 0 {
			input.Value = int64(1)
		}
	}
	if input.Name == base.PageSizePublicParam.Name {
		value := input.Value.(int64)
		if value <= 0 || value > compBase.MaxPageSize {
			input.Value = int64(compBase.MaxPageSize)
		}
	}
	if input.Name == base.DynamicColumnsPublicParam.Name {
		valueStr := input.Value.(string)
		value := strings.Split(valueStr, ",")
		for idx, e := range value {
			value[idx] = strings.TrimSpace(e)
		}
		input.Value = value
	}
	if input.Name == base.BigdataTenantCodePublicParam.Name {
		valueStr := input.Value.(string)
		input.Value = valueStr
	}
}

func getTenantCodeFromInputParams(input []*base.InputRequestParam) string {
	for _, p := range input {
		if p.Name == base.BigdataTenantCodePublicParam.Name {
			return p.Value.(string)
		}
	}
	return ""
}
