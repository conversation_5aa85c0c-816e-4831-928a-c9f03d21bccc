package data_api

import (
	"context"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/json_helper"
	fastPkg "gitlab.mypaas.com.cn/dmp/gopkg/bigdata/fast"
	"gitlab.mypaas.com.cn/fast/tracker-go/trace"
	"strings"
	"time"

	"github.com/defval/inject/v2"
	"github.com/gin-gonic/gin"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	fastBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/base"
)

const (
	CHINESE_WORD_YES = "是"
	CHINESE_WORD_NO  = "否"
	DYNAMICCOLUMNS   = "dynamicColumns"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewCallApiFastRecorder))
}

type CallApiFastRecorder struct {}

func NewCallApiFastRecorder() *CallApiFastRecorder {
	return &CallApiFastRecorder{}
}

func (s *CallApiFastRecorder) GenerateApiCallFastBizParam(ctx context.Context, c *gin.Context, startTime time.Time,
	reqCtx *RequestContext, metaData *ApiMetaData,
	sqlList []string, sqlCostTime time.Duration) fastBase.BizParamsGenerator {
	return func() map[string]interface{} {
		sortColumn := ""
		sort := ""
		if len(metaData.Detail.CreateContent.Sorts) > 0 {
			sortColumn = metaData.Detail.CreateContent.Sorts[0].Column
			sort = lo.Ternary("ASC" == metaData.Detail.CreateContent.Sorts[0].Method, "正序", "倒序")
		}

		res := fastPkg.ApiCommonBizParam{
			TraceID:       trace.GetTraceId(ctx),
			TenantCode:    getTenantCodeFromInputParams(reqCtx.inputParams),
			ApiName:       metaData.MainInfo.Name,
			ApiType:       fastPkg.CustomDataApi,
			RequestMethod: string(metaData.Detail.RequestMethod),
			LogTime:       time.Now().Format("2006-01-02 15:04:05.000000"),
			RequestPath:   metaData.Detail.RequestPath,
			RequestTime:   startTime.Format("2006-01-02 15:04:05.000000"),
			RequestParams: strings.Join(lo.Map(metaData.Detail.RequestParams, func(item *base.ApiParamBase, _ int) string {
				return item.Name
			}), ","),
			ResponseTime:   time.Now().Format("2006-01-02 15:04:05.000000"),
			CostTime:       time.Now().Sub(startTime).Milliseconds(),
			ExecuteSql:     strings.Join(sqlList, ";"),
			ExecuteSqlTime: sqlCostTime.Milliseconds(),
			Extra: map[string]interface{}{
				"api_code":        metaData.MainInfo.Code,
				"create_model":    metaData.Detail.CreateMode,
				"response_format": metaData.Detail.ResponseFormat,
				"enable_page":     lo.Ternary(metaData.Detail.EnablePage == 0, CHINESE_WORD_NO, CHINESE_WORD_YES),
				"dynamic": lo.ContainsBy(metaData.Detail.RequestParams, func(item *base.ApiParamBase) bool {
					return item.Name == DYNAMICCOLUMNS
				}),
				"request_value": json_helper.MarshalOr(reqCtx.inputParams, "json marshal failed"),
				"response_params": strings.Join(lo.Map(metaData.Detail.ResponseParams, func(item *base.ApiParamBase, _ int) string {
					return item.Name
				}), ","),
				"quote_table": strings.Join(lo.Map(metaData.Detail.CreateContent.Tables, func(item base.Table, _ int) string {
					return item.TableName
				}), ","),
				"sort_column": sortColumn,
				"sort":        sort,
			},
		}

		return res.ToFlatMap()
	}
}
