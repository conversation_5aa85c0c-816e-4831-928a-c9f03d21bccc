package data_api

import (
	"context"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors/biz"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/json_utils"

	"github.com/defval/inject/v2"
	pkgDi "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/cache"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/api_manage/repo"
)

func init() {
	pkgDi.InjectContext = append(pkgDi.InjectContext, inject.Provide(NewApiMeta))
}

// ApiMeta api编辑管理，要实现缓存
type ApiMeta struct {
	apiCache *cache.ApiCache
	apiRepo  repo.ApiManageRepo
	apiGroup repo.ApiGroupRepo
}

type ApiMetaData struct {
	Detail   *base.ApiDetail
	MainInfo *base.ApiMain
	AuthApps map[string]*base.ApplicationApi
}

func NewApiMeta(apiCache *cache.ApiCache, apiRepo repo.ApiManageRepo, apiGroup repo.ApiGroupRepo) *ApiMeta {
	return &ApiMeta{
		apiCache: apiCache,
		apiRepo:  apiRepo,
		apiGroup: apiGroup,
	}
}

func (am *ApiMeta) GetApiDataByPath(ctx context.Context, project, requestPath string) (metaData *ApiMetaData, err error) {
	if metaData, err = am.GetApiDataByPathCache(ctx, project, requestPath); err == nil {
		return
	}
	metaData = &ApiMetaData{
		AuthApps: map[string]*base.ApplicationApi{},
	}
	detail, err := am.apiRepo.GormGetApiDetailByPath(ctx, project, requestPath)
	if errors.IsCode(err, errors.NOT_FOUND) {
		err = errors.Wrapf(biz.API_NOT_EXISTS_ERR, err, requestPath)
		return
	}
	if err != nil {
		return
	}
	metaData.Detail = detail

	mainInfo, err := am.apiRepo.GormGetApiMainDetail(ctx, project, detail.Code)
	if errors.IsCode(err, errors.NOT_FOUND) {
		err = errors.Wrapf(biz.API_NOT_EXISTS_ERR, err, requestPath)
		return
	}
	if err != nil {
		return nil, err
	}
	metaData.MainInfo = mainInfo
	authApps, err := am.apiRepo.GormApplicationApiRelationList(ctx, project, detail.Code)
	if err != nil {
		return nil, err
	}
	for _, authApp := range authApps {
		metaData.AuthApps[authApp.ApiKey] = authApp
	}
	am.apiCache.SetApiDataCache(ctx, project, requestPath, metaData)
	return
}

func (am *ApiMeta) GetApiDataByPathCache(ctx context.Context, project, requestPath string) (data *ApiMetaData, err error) {
	b, err := am.apiCache.GetApiDataByPathCache(ctx, project, requestPath)
	if err != nil {
		return
	}
	data = &ApiMetaData{}
	err = json_utils.Unmarshal(b, data)
	return
}
