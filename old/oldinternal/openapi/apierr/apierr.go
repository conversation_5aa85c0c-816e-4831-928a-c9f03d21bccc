package apierr

import (
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errors/biz"
)

type ApiErr struct {
	Code     errors.ErrCode `json:"code"`
	ErrMsg   string         `json:"err_msg"`
	Solution string         `json:"solution"`
}

var solutionMap map[errors.ErrCode]string

func init() {
	solutionMap = map[errors.ErrCode]string{
		errors.ErrCode(0):                "接口调用成功",
		biz.API_NO_ACCESS_RIGHT_ERR:      "检查API访问权限",
		biz.API_PARAM_INVALID_FORMAT_ERR: "检查参数格式合法性",
		biz.API_PARAM_INVALID_VALUE_ERR:  "检查参数输入值合法性",
		biz.API_PARAM_NOT_PROVIDE_ERR:    "检查是否缺少必填参数",
		biz.API_NOT_EXISTS_ERR:           "检查API配置",
		biz.API_COMMON_USER_ERR:          "检查API调用通用错误",
		biz.API_SQL_EXECUTE_ERR:          "检查API SQL配置",
		biz.API_RESOURCE_CONFIG_ERR:      "检查查询资源配置",
		biz.API_SQL_EXECUTE_TIMEOUT_ERR:  "检查API超时控制",
		biz.API_SQL_CHECK_ERR:            "检查API语法",
	}
}

func GetAPIErrs() []ApiErr {
	return lo.Map(biz.GetApiErrCodes(), func(item errors.ErrCode, _ int) ApiErr {
		apiErr := ApiErr{
			Code:   item,
			ErrMsg: errors.GetCodeErr(item),
		}
		solution, ok := solutionMap[item]
		if ok {
			apiErr.Solution = solution
		}
		return apiErr
	})
}

func NewCommonUserErrWrap(err error) *errors.Error {
	return errors.Wrap(biz.API_COMMON_USER_ERR, err)
}
func NewCommonUserErrf(format string, a ...any) *errors.Error {
	return errors.New(biz.API_COMMON_USER_ERR).WithMessage(format, a...)
}
