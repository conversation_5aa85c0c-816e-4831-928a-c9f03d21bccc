package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/context_helper"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/session"
	"io/ioutil"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
)

type UserError struct {
	Msg string `json:"msg"`
}

func (err UserError) Error() string {
	return err.Msg
}

func NewUserError(format string, args ...interface{}) *UserError {
	return &UserError{
		Msg: fmt.Sprintf(format, args...),
	}
}

func NewUserErrorWrapf(err error, format string, args ...interface{}) error {
	return NewUserError(errors.Wrapf(err, format, args...).Error())
}

type CommonError struct {
	Errors map[string]interface{} `json:"errors"`
}

func (err CommonError) Error() string {
	for field, msg := range err.Errors {
		return fmt.Sprintf("%s: %+v", field, msg)
	}
	return ""
}

// ErrInvalidForm 无效的表单输入
// 包括json, formdata, query
type ErrInvalidForm string

func (err ErrInvalidForm) Error() string {
	return string(err)
}

func NewValidatorError(err error) CommonError {
	res := CommonError{}
	res.Errors = make(map[string]interface{})
	errs := err.(validator.ValidationErrors)
	for _, v := range errs {
		switch v.Tag() {
		case "required":
			res.Errors[v.Field()] = fmt.Sprintf("%s is required", v.Field())
		case "max":
			res.Errors[v.Field()] = fmt.Sprintf("%s cannot be longer than %s", v.Field(), v.Param())
		case "min":
			res.Errors[v.Field()] = fmt.Sprintf("%s must be longer than %s", v.Field(), v.Param())
		case "gte":
			res.Errors[v.Field()] = fmt.Sprintf("%s cannot be gte than %s", v.Field(), v.Param())
		case "lte":
			res.Errors[v.Field()] = fmt.Sprintf("%s must be lte than %s", v.Field(), v.Param())
		case "email":
			res.Errors[v.Field()] = "Invalid email format"
		case "len":
			res.Errors[v.Field()] = fmt.Sprintf("%s must be %s characters long", v.Field(), v.Param())
		case "tableName":
			res.Errors[v.Field()] = fmt.Sprintf("%s 只能用英文的a-z、A-Z、数字和下划线（_），且以字母开头，名称的长度不超过128字节", v.Param())
		case "columnName":
			res.Errors[v.Field()] = fmt.Sprintf("%s 只能用英文的a-z、A-Z、数字和下划线（_），且以字母开头，名称的长度不超过128字节", v.Param())
		case "nameEn":
			res.Errors[v.Field()] = "只支持英文字符a-zA-Z、数字0-9以及下划线_,且必须以英文开头，不能以下划线结尾"
		case "physicalFiledName":
			res.Errors[v.Field()] = fmt.Sprintf("%v 物理化字段英文名 只支持英文字符a-zA-Z、数字0-9以及下划线_,且必须以英文开头，不能以下划线结尾", v.Value())
		case "validateQuerySorts":
			res.Errors["参数sorts"] = fmt.Sprintf("格式错误，排序字段只能为%s，格式示例：created_on ASC,name DESC", v.Param())
		case "validateApiRequestMethod":
			res.Errors[v.Field()] = "请求方法目前只支持GET和POST"
		case "validateApiRequestPath":
			res.Errors[v.Field()] = "请求路径必须以/开头"
		case "validateApiResponseFormat":
			res.Errors[v.Field()] = "返回类型目前只支持json"
		default:
			res.Errors[v.Field()] = fmt.Sprintf("%s is not valid", v.Field())
		}
	}
	return res
}

type Utility struct {
}

// GetSession get session of current request
//
// abort with 401 http status if failed to get session
func (u Utility) GetSession(c *gin.Context) (session2 *session.Session, exists bool) {
	sess, err := context_helper.GetSession(c)
	if err != nil {
		exists = false
		return
	}

	sessBs, _ := json.Marshal(sess)
	session2 = &session.Session{}
	err = json.Unmarshal(sessBs, session2)
	if err != nil {
		c.String(401, "Invalid Authentication")
		return
	}
	exists = true
	return
}

func (u Utility) SendFailureWithData(c *gin.Context, errmsg string, data interface{}) {
	c.JSON(200, gin.H{"result": false, "msg": errmsg, "data": data})
}

func (u Utility) SendFailure(c *gin.Context, errmsg string) {
	c.JSON(200, gin.H{"result": false, "msg": errmsg})
}

func (u Utility) SendSuccess(c *gin.Context, data ...interface{}) {
	u.SendSuccessWithMessage(c, "ok", data...)
}

func (u Utility) SendSuccessWithMessage(c *gin.Context, msg string, data ...interface{}) {
	dataLen := len(data)
	if dataLen > 1 {
		var items []interface{}
		items = append(items, data...)
		c.JSON(200, gin.H{"result": true, "msg": msg, "data": items})
		return
	} else if dataLen == 1 {
		c.JSON(200, gin.H{"result": true, "msg": msg, "data": data[0]})
	} else {
		c.JSON(200, gin.H{"result": true, "msg": msg})
	}
}

// bind 绑定表单，并验证
func (u Utility) Bind(c *gin.Context, obj interface{}) error {
	b := binding.Default(c.Request.Method, c.ContentType())
	return c.ShouldBindWith(obj, b)
}

// CheckError check error and abort rqeuest
func (u Utility) CheckError(c *gin.Context, err error) bool {
	if err == nil {
		return true
	}
	switch err.(type) {
	case *UserError, *ErrInvalidForm:
		c.JSON(200, gin.H{"result": false, "code": 400, "msg": err.Error()})
		return false
	case validator.ValidationErrors:
		c.JSON(200, gin.H{"result": false, "code": 400, "msg": NewValidatorError(err).Error()})
		return false
	default:
		global.Logger.Errorw("", err)
		// 统一使用服务内部错误
		c.JSON(200, gin.H{"result": false, "code": 500, "msg": err.Error()})
		return false
	}
}

// 目前为了能用bind方式校验请求结构体的字段，做法是在这里用json包先预处理一次，后续考虑更优方案
func (u Utility) ConvertBody(c *gin.Context, obj interface{}) error {
	bodyByte, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		return err
	}
	err = json.Unmarshal(bodyByte, obj)
	if err != nil {
		return err
	}
	v, err := json.Marshal(obj)
	if err != nil {
		return err
	}
	c.Request.Header.Set("Content-Type", "application/json;charset=UTF-8")
	c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(v))
	return nil
}
