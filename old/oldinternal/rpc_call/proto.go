package rpc_call

import (
	"strings"

	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	multiDimModel "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/multi_dim_model"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/time_utils"
)

type AdsPropMode string

const (
	AdvanceProp AdsPropMode = "advance"
	SimpleProp  AdsPropMode = "simple"
)

// 解决包的互相引用问题，将Prop结构体移到此处
type Prop struct {
	global.FilterConditions
	FieldCode                string                 `json:"field_code"`
	Code                     string                 `json:"code"`
	Mode                     AdsPropMode            `json:"mode"` // 指标模式
	ObjName                  string                 `json:"obj_name"`
	PropName                 string                 `json:"prop_name"`
	Func                     string                 `json:"func"`
	Props                    []Prop                 `json:"props"`
	Specifier                string                 `json:"specifier"`
	NameCn                   string                 `json:"name_cn"`
	FieldType                string                 `json:"field_type"`
	Alias                    string                 `json:"alias"`
	Description              string                 `json:"description"`
	PropRaw                  string                 `json:"prop_raw"`
	GrowthName               string                 `json:"growth_name"`
	RatioName                string                 `json:"ratio_name"`
	ValueComment             string                 `json:"value_comment"`
	ConvertFieldType         string                 `json:"convert_field_type"`
	ConvertFieldTypeParams   ConvertFieldTypeParams `json:"convert_field_type_params"`
	ObjectID                 string                 `json:"object_id"`
	Length                   int32                  `json:"length"`
	PropFields               []PropField            `json:"prop_fields"`
	Value                    interface{}            `json:"value"`                   // where条件值
	SystemFieldType          string                 `json:"system_field_type"`       // 系统类型
	AggInfo                  *AggInfo               `json:"agg_info"`                // 聚合函数信息
	CalcRatio                bool                   `json:"calc_ratio"`              // 是否求占比
	IndicatorBusinessCode    string                 `json:"indicator_business_code"` // 指标业务编码
	TimeValueType            int32                  `json:"time_value_type"`         //时间值类型
	ViewModel                *base.FieldViewModel   `json:"view_model"`
	DialectSettingContentStr string                 `json:"dialect_setting_content"`
	DialectSettingContent    global.DialectSettingContent
}

// Deprecated
// AggInfo 指标表达式聚合函数信息, 多维模型会有该信息
// AggInfo 例：原PropRaw=sum({t1}.[a]+{t1}.[b]) / sum(if({t2}.[c]==0, 0, 1))
// AggInfo Tmpl = uuid1 / uuid2
//
//	AggInfo SubExprAggInfos = {
//		"uuid1": {"sql_expr": sum(t1.a+t1.b), "prop_fields": [{"table_name": "t1", "field_name": "a"}, {"table_name": "t1", "field_name": "b"}]},
//	 "uuid2": {"sql_expr": sum(if(t2.c==0, 0, 1), "prop_fields": [{"table_name": "t1", "field_name": "a"}]}
//	}
type AggInfo struct {
	Tmpl            string                    `json:"tmpl"`               // 聚合函数替换模板
	SubExprAggInfos map[string]SubExprAggInfo `json:"sub_expr_agg_infos"` //
}

type SubExprAggInfo struct {
	SqlExpr    string      `json:"sql_expr"`    // 聚合函数对应的sql片段
	PropFields []PropField `json:"prop_fields"` // 聚合函数包含的字段信息
}

type PropField struct {
	ModelFieldID      string `json:"business_model_field_id"`
	FieldType         string `json:"field_type"`
	FieldBusinessType int64  `json:"field_business_type"`
	TableName         string `json:"table_name"`
	FieldName         string `json:"field_name"`
	FieldNameCn       string `json:"field_name_cn"`
	ExpandTableName   string `json:"expand_table_name"`
	FieldCode         string `json:"field_code"`
}

type AdsPropSpecifier string

const (
	Min           AdsPropSpecifier = "min"
	Max           AdsPropSpecifier = "max"
	Avg           AdsPropSpecifier = "avg"
	Count         AdsPropSpecifier = "count"
	Sum           AdsPropSpecifier = "sum"
	CountDistinct AdsPropSpecifier = "count distinct"
)

type ConvertFieldTypeParams struct {
	Length int32 `json:"length"`
	Scale  int32 `json:"scale"`
}

type Dependence struct {
	Category        base.ModelCategory                   `json:"category" db:"category"`
	Name            string                               `json:"name"`
	TableName       string                               `json:"table_name"`
	CodeId          string                               `json:"code_id"`
	ObjPrefix       string                               `json:"obj_prefix"`
	Catalog         string                               `json:"catalog"`
	RelationList    []RelationItem                       `json:"relation_list"`
	RelationalModel multiDimModel.NewMultiDimViewContent `json:"relational_model"`
}

type RelationItem struct {
	Category      string   `json:"category" db:"category"`
	CodeId        string   `json:"code_id"`
	TableName     string   `json:"table_name"`
	Name          string   `json:"name"`
	RelationType  string   `json:"relation_type"`
	RelationField []string `json:"relation_field"`
}

type ConditionOperator string

const (
	NoConditionOperator ConditionOperator = ""
	EQ                  ConditionOperator = "="
	NEQ                 ConditionOperator = "!="
	GT                  ConditionOperator = ">"
	LT                  ConditionOperator = "<"
	GTE                 ConditionOperator = ">="
	LTE                 ConditionOperator = "<="
	IS_NULL             ConditionOperator = "IS NULL"
	IS_NOT_NULL         ConditionOperator = "IS NOT NULL"
	IN                  ConditionOperator = "IN"
	NOT_IN              ConditionOperator = "NOT IN"
	LIKE                ConditionOperator = "LIKE"
	NOT_LIKE            ConditionOperator = "NOT LIKE"
	BETWEEN             ConditionOperator = "BETWEEN"
	NOT_BETWEEN         ConditionOperator = "NOT BETWEEN"
	// 日期
	FROM_TODAY          ConditionOperator = "FAR"
	FROM_YESTERDAY      ConditionOperator = "FAR_YESTERDAY"
	FROM_WEEK           ConditionOperator = "FROM_WEEK"
	FROM_MONTH          ConditionOperator = "FROM_MONTH"
	FROM_QUARTER        ConditionOperator = "FROM_QUARTER"
	FROM_YEAR           ConditionOperator = "FROM_YEAR"
	FROM_RANGE_VARIABLE ConditionOperator = "FROM_RANGE_VARIABLE"
)

type ConditionLogicalRelation string

const (
	NoConditionLogicalRelation ConditionLogicalRelation = ""
	AND                        ConditionLogicalRelation = "AND"
	OR                         ConditionLogicalRelation = "OR"
)

type Condition struct {
	Left            *Prop                    `json:"left"`
	Operator        ConditionOperator        `json:"operator"`
	Right           *Prop                    `json:"right"`
	LogicalRelation ConditionLogicalRelation `json:"logical_relation"`
	Conditions      []Condition              `json:"conditions"`
}

type ModelFilter struct {
	FilterID  string `json:"filter_id"`
	Code      string `json:"code"`
	Name      string `json:"name"`
	TableName string `json:"table_name"`
	global.FilterConditions
}

type QueryStructureTransformResponse struct {
	SQL string `json:"sql"`
}

type VariableType string

const (
	StringVar   VariableType = "string"
	DatetimeVar VariableType = "datetime"
)

type ScopeType string

const (
	SingleValue   ScopeType = "single"
	RangeValue    ScopeType = "range"
	SequenceValue ScopeType = "sequence"
)

type ExactType string

const (
	DateExact     ExactType = "date"
	DatetimeExact ExactType = "datetime"
)

type DynamicType string

const (
	DynamicValue DynamicType = "dynamic"
	StaticValue  DynamicType = "static"
)

type RelativeType string

const (
	RelativeTypeNone RelativeType = ""
	Today            RelativeType = "TODAY"
	Yesterday        RelativeType = "YESTERDAY"
	Far              RelativeType = "FAR"
	FarYesterday     RelativeType = "FAR_YESTERDAY"
	FromWeek         RelativeType = "FROM_WEEK"
	FromMonth        RelativeType = "FROM_MONTH"
	FromQuarter      RelativeType = "FROM_QUARTER"
	FromYear         RelativeType = "FROM_YEAR"
)

type DynamicValueContent struct {
	RelativeType  RelativeType `json:"relative_type"`
	RelativeValue int          `json:"relative_value"`
}

type DatetimeVariableContent struct {
	ExactType           ExactType           `json:"exact_type"`            // 精确类型
	DynamicType         DynamicType         `json:"dynamic_type"`          // 动态类型
	StaticValueContent  []string            `json:"static_value_content"`  // 静态值内容
	DynamicValueContent DynamicValueContent `json:"dynamic_value_content"` // 动态值内容
}

type StringVariableContent struct {
	Value string `json:"value"`
}

type Variable struct {
	ID                     string                  `json:"id"`
	Name                   string                  `json:"name"`
	Desc                   string                  `json:"desc"`
	VariableType           VariableType            `json:"variable_type"`
	ScopeType              ScopeType               `json:"scope_type"`
	DefaultStringContent   StringVariableContent   `json:"default_string_content"`
	DefaultDatetimeContent DatetimeVariableContent `json:"default_datetime_content"`
}

func (v *Variable) getDynamicDefaultValue() (string, error) {
	// 依照产品逻辑，后续会废弃presto做查询, 这里仅需要支持sr语法
	varDef := v.DefaultDatetimeContent
	val := varDef.DynamicValueContent.RelativeValue
	var begin string
	var end string
	switch v.ScopeType {
	case RangeValue:
		switch varDef.DynamicValueContent.RelativeType {
		case Far:
			begin, end = time_utils.FarRange(val, 0)
		case FarYesterday:
			begin, end = time_utils.FarRange(val, 1)
		case FromWeek:
			begin, end = time_utils.FromWeekRange(val)
		case FromMonth:
			begin, end = time_utils.FromMonthRange(val)
		case FromQuarter:
			begin, end = time_utils.FromQuarterRange(val)
		case FromYear:
			begin, end = time_utils.FromYearRange(val)
		default:
			return "", errors.Errorf("不支持的相对时间类型[%s]", varDef.DynamicValueContent.RelativeType)
		}
		return v.wrapValue(begin, end), nil
	case SingleValue:
		switch varDef.DynamicValueContent.RelativeType {
		case Today:
			return v.wrapValue(time_utils.Far(0)), nil
		case Yesterday:
			return v.wrapValue(time_utils.Far(1)), nil
		case Far:
			return v.wrapValue(time_utils.Far(val)), nil
		default:
			return "", errors.Errorf("不支持的相对时间类型[%s]", varDef.DynamicValueContent.RelativeType)
		}
	default:
		return "", errors.Errorf("不支持的时间区间类型[%s]", varDef.DynamicType)
	}
}

func (v *Variable) wrapValue(vals ...string) string {
	if len(vals) == 0 {
		return ""
	}
	return strings.Join(vals, ",")
}

func (v *Variable) GetDefaultValue() (string, error) {
	if v.VariableType == StringVar {
		return v.DefaultStringContent.Value, nil
	}
	if v.ScopeType == SingleValue {
		if v.DefaultDatetimeContent.DynamicType == StaticValue {
			return strings.Join(v.DefaultDatetimeContent.StaticValueContent, ","), nil
		}
		return v.getDynamicDefaultValue()
	}
	if v.DefaultDatetimeContent.DynamicType == StaticValue {
		return strings.Join(v.DefaultDatetimeContent.StaticValueContent, ","), nil
	}
	return v.getDynamicDefaultValue()
}

type RelationBizType string

const (
	OneToOne  RelationBizType = "one_to_one"
	OneToMany RelationBizType = "one_to_many"
	ManyToOne RelationBizType = "many_to_one"
)

type SystemFieldType string

const (
	Bigint   SystemFieldType = "BIGINT"
	Double   SystemFieldType = "DOUBLE"
	Datetime SystemFieldType = "DATETIME"
	Boolean  SystemFieldType = "BOOLEAN"
	Decimal  SystemFieldType = "DECIMAL"
	String   SystemFieldType = "STRING"
)
