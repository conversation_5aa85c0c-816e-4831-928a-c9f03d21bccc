package store

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/dmp/gopkg/db"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

type Repo struct {
	saas db.SaaS
}

func NewRepo(saas db.SaaS) *Repo {
	return &Repo{
		saas: saas,
	}
}

func (s *Repo) GetProjDB(ctx context.Context, projectCode string) (*sqlx.DB, error) {
	db, err := s.saas.GetDB(ctx, projectCode)
	if err != nil {
		return nil, err
	}
	return db.GetSession(), nil
}

func (s *Repo) GetDB() *sqlx.DB {
	return s.saas.GetConfigDB().GetSession()
}
func (s *Repo) GetSysDB(ctx context.Context) (*sqlx.DB, error) {
	db, err := s.saas.GetSysDB(ctx)
	if err != nil {
		return nil, err
	}
	return db.GetSession(), nil
}

// GetTx
func (s *Repo) GetTx() (tx *sql.Tx, err error) {
	sess := s.GetDB()
	tx, err = sess.Begin()
	return
}

func buildUpdateSQL(table string, id interface{}, fieldsValues map[string]interface{}) string {

	var parameters []string
	for fieldName := range fieldsValues {
		parameters = append(parameters, fmt.Sprintf("%s=:%s", fieldName, fieldName))
	}

	if _, ok := fieldsValues["id"]; !ok {
		fieldsValues["id"] = id
	}

	return fmt.Sprintf("update %s set %s where id=:id", table,
		strings.Join(parameters, ","))
}

func (s *Repo) Update(ctx context.Context, project, id, table string, fieldsValues map[string]interface{}) error {

	sqlStore, err := s.saas.GetDB(ctx, project)
	if err != nil {
		return errors.WithStack(err)
	}
	var sql = buildUpdateSQL(table, id, fieldsValues)
	fieldsValues["id"] = id
	_, err = sqlStore.GetSession().NamedExec(sql, fieldsValues)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *Repo) GetProject(ctx context.Context, project string) (*entities.Project, error) {
	var obj entities.Project
	sqlText := "select id,name,status,code,description,modified_on from dap_p_project_main where code=? and status=?"
	err := s.GetDB().GetContext(ctx, &obj, sqlText, project, 1)
	return &obj, err
}

func (s *Repo) GetAllProject(ctx context.Context, status int) ([]*entities.Project, error) {
	var objs []*entities.Project
	sqlText := "select id,name,status,code,description,modified_on from dap_p_project_main where status=?"
	err := s.GetDB().SelectContext(ctx, &objs, sqlText, status)
	return objs, err
}
